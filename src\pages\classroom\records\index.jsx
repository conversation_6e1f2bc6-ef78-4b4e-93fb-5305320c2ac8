import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import Empty from '@/component/empty'
import s from './index.module.scss'
import * as API from '../api'

export const STATUS_MAP = {
  1: { label: '待审核', color: '#3F969D', icon: 'success'  },
  2: { label: '报名成功', color: '#30A1A6', icon: 'success' },
  3: { label: '已驳回', color: '#C55D5D', icon: 'clear' },
  4: { label: '已取消', color: '#C55D5D', icon: 'clear' },
}

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      list: []
    }
  }

  componentDidShow() {
    this.getSignList()
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '活动报名记录',
    navigationBarTextStyle: 'black',
  };

  async getSignList() {
    const { code, data} = await API.getSignList()
    if (code !== 0) return
    this.setState({ list: data || [] })
  }

  toDetail(id) {
    Taro.navigateTo({ url: `/pages/classroom/records/detail?id=${id}` })
  }

  render() {
    const { list } = this.state
    return (
      <View className={s.page}>
        {
          list.map(v => (
            <View key={v.id} className={[s.list_item, 'f-row', 'f-c-center']} onClick={() => this.toDetail(v.id)}>
              <View className='f-1'>
                <View className={s.list_item_title}>{v.trainName}</View>
                <View className={s.list_item_time}>{v.updateTime}</View>
              </View>
              <View className={s.list_item_status} style={{ color: STATUS_MAP[v.status].color }}>{STATUS_MAP[v.status].label}</View>
            </View>
          ))
        }
        {
          (!list || !list.length) ? <Empty text='暂无报名记录' /> : null
        }
      </View>
    )
  }
}
