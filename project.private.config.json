{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "doctor-master", "setting": {"compileHotReLoad": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false}, "condition": {"miniprogram": {"list": [{"name": "test", "pathName": "pages/sample/check-in/index", "query": "", "launchMode": "default", "scene": null}, {"name": "指定路径参数编译", "pathName": "package1/regist/list/index", "query": "", "launchMode": "default", "scene": 1011}]}}, "libVersion": "2.20.2"}