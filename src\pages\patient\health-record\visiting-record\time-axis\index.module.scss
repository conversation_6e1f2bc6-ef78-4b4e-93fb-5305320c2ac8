.allRecord {
  background-color: white;
  margin-bottom: 20px;

  .recordModule {
    display: flex;
    padding: 30px 40px;
    border-bottom: 2px solid rgb(242,242,242);
    align-items: center;
    text {
      margin-top: -5px;
    }

    .timeAxis {
      // margin-top: -30px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 20px;
      align-items: center;

      .circle {
        width: 20rpx;
        height: 20rpx;
        border-radius: 10rpx;
        background: #3ECEB6;
      }

      .line {
        width: 2px;
        height: 160px;
        background-color: #3ECEB6;
        // margin-top:-50px;
      }

      .shortLine {
        width: 2px;
        height: 30px;
        background-color: #3ECEB6;
      }

      .noShortLine {
        width: 2px;
        height: 30px;
        display: none;
      }

      .noLine {
        width: 2px;
        height: 100px;
        visibility: hidden;
      }
    }

    .name {
      font-weight: bold;
      display: flex;
      margin-left: 10px;
    }

    .label {
      background-color: #F8F8F8;
      color: #FF8C26;
      width: 157px;
      height: 47px;
      margin-left: 30px;
      margin-top: -5px;
    }

    .icon {
      // margin-top: -20px;
      flex: 1;
      justify-content: flex-end;
      display: flex;
      font-size: 25px;
    }
  }

  .content {
    background-color: #fff;
    padding: 20px 40px;
    font-weight: 400;
    margin-bottom: 20px;
    // color: #989898;
    word-wrap: break-word;
  }
}

.top {
  width: 18px;
  height: 18px;
  border-top: 2px solid #000;
  border-left: 2px solid #000;
  transform: rotate(225deg);
}

.right {
  width: 18px;
  height: 18px;
  border-top: 2px solid #000;
  border-left: 2px solid #000;
  transform: rotate(135deg);
}
