import Taro, { Component } from '@tarojs/taro'
import { View } from '@tarojs/components'
import * as API from '../api'
import s from './index.module.scss'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      patientPid: '',

      name: '',
      career: '',
      education: '',
      nation: '',
      height: '',
      weight: '',
    }
  }



  componentWillMount() {
    const { patientPid } = this.$router.params
    this.setState({ patientPid }, this.getDetail)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '基本信息',
    navigationBarTextStyle: 'black'
  };

  async getDetail() {
    const { code, data } = await API.getBasicInfo({ patients: this.state.patientPid })
    if (code !== 0) return
    const { name = '', career = '', education = '', nation = '', height = '', weight = ''  } = data || {}
    this.setState({ name, career, education, nation, weight, height })
  }

  render() {
    const { name, career, education, nation, weight, height } = this.state
    return (
      <View className={s.page}>
        <View className={s.page_card}>
          <View className={s.item}>
            <View className={s.title}>姓名</View>
            <View className={s.tips}>{name || '--' }</View>
          </View>
          <View className={s.item}>
            <View className={s.title}>职业</View>
            <View className={s.tips}>{career || '--'}</View>
          </View>
          <View className={s.item}>
            <View className={s.title}>学历</View>
            <View className={s.tips}>{education || '--'}</View>
          </View>
          <View className={s.item}>
            <View className={s.title}>民族</View>
            <View className={s.tips}>{nation || '--'}</View>
          </View>
          <View className={s.item}>
            <View className={s.title}>身高</View>
            <View className={s.tips}>{height || '--'}</View>
          </View>
          <View className={s.item}>
            <View className={s.title}>体重</View>
            <View className={s.tips}>{weight || '--'}</View>
          </View>
        </View>
      </View>
    )
  }
}
