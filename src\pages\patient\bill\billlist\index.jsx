import Taro, { Component } from '@tarojs/taro';
import {ScrollView, Text, View} from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import WPage from '@/component/wpage';
import Empty from '@/component/empty';
import * as Utils from '@/utils/utils';

export default class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      pageNum: 1,
      numPerPage: 10,
      canLoadMore: true,
      recordList: []
    };
  }

  componentWillMount() {
  }

  componentDidMount() {
    this.getRecordList();
  }

  componentWillUnmount() {
  }

  componentDidShow() {
  }

  componentDidHide() {
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '开单记录',
    navigationBarTextStyle: 'black'
  };

  async getRecordList(loadMore = false) {
    let {pageNum, numPerPage, canLoadMore} = this.state;
    if (loadMore && !canLoadMore) return;
    loadMore && pageNum++;
    const param = {
      consultationType: 'prescription',
      pageNum,
      numPerPage
    };
    const {code, data} = await Api.queryRecordList(param);
    if (code === 0) {
      let {recordList = []} = data;
      if (recordList.length < numPerPage) {
        this.setState({canLoadMore: false});
      }
      if (!recordList.length) return ;
      if (loadMore) {
        recordList = this.state.recordList.concat(recordList);
      }
      this.setState({recordList, pageNum});
    }
  }

  goToDetail(item = {}) {
    const query = Utils.jsonToQueryString(item);
    Taro.navigateTo({ url: `/pages/patient/bill/billrecord/index?${query}` });
  }

  scrollLower = () => {
    this.getRecordList('more');
  };

  render() {
    const {
      recordList = []
    } = this.state;
    //会诊状态：(0,"提交申请待审核"),(1,"审核通过待会诊"),(2,"已创建会诊间"),(3,"会诊结束"),(4,"申请不通过"),(5,"申请取消"),(6,"会诊取消");
    const StatusNameList = ['待审核', '已通过', '会诊中', '已结束', '已驳回', '已取消申请', '已取消'];
    const StatusTimePrefix = ['申请', '审核通过', '创建会诊间', '结束会诊', '审核', '取消申请', '会诊取消'];

    return (
      <WPage>
        {
          recordList.length ?
            <ScrollView scrollY onScrollToLower={this.scrollLower} className={`${s.container}`}>
              {
                recordList.map((item) => {
                  return (
                    <View
                      key={item.id}
                      className={s.recordItem}
                      onClick={() => this.goToDetail(item)}
                    >
                      <View className='flex f-m-between'>
                        <View>
                          <Text className={s.name}>{item.patientName}</Text>
                          <Text className={s.divide}>|</Text>
                          <Text className={s.pid}>PID:{item.patientPid}</Text>
                        </View>
                        <View className={s.statusName+' '+ s[`status-${item.status}`]}>{StatusNameList[item.status]}</View>
                      </View>
                      <View>单号：{item.orderNo}</View>
                      <View>{StatusTimePrefix[item.status]}时间：{item.status == '1' ? item.reviewTime : item.updateTime}</View>
                    </View>
                  )
                })
              }
            </ScrollView>
           :
            <Empty text='暂无记录' />
        }

      </WPage>
    );
  }
}
