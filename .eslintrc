{"extends": ["taro"], "rules": {"no-unused-vars": ["error", {"varsIgnorePattern": "<PERSON><PERSON>"}], "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx", ".tsx"]}], "import/prefer-default-export": [0]}, "parser": "babel-es<PERSON>", "globals": {"$DOMAIN": true, "$PAY_DOMAIN": true, "$CDN_DOMAIN": true, "$RUN_ENV": true, "$IPX_BOTTOM_HEIGHT": true, "$PRIMARY_COLOR": true, "$HIS_NAME": true, "$templateId": true, "$REPLACE_IMG_DOMAIN": true, "my": true}}