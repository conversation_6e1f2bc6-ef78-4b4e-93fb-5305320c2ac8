.list{
  background-color: #fff;
  margin-bottom: 20px;
  .item{
    display: flex;
    font-size: 30px;
    padding: 20px 30px 20px 0;
    margin-left: 30px;
    align-items: center;
    border-bottom: 1px solid $color-border;

    &:last-child{
      border: 0;
    }

    .label{
      flex: auto;
      color: $color-title;
      margin-right: 30px;
      min-width: 5em;
      &.link{
        color: blue;
      }
    }
    .content{
      color: $color-title;
      text-align: right;
      word-break: break-all;
      line-height: 1.5;
      .primary{
        color: $color-primary;
      }
      .title{
        color: $color-title;
      }
      .text{
        color: $color-text;
      }
      .link{
        color: blue;
      }
      .left{
        text-align: left;
      }
      .justify{
        text-align: justify;
      }
    }
  }
}