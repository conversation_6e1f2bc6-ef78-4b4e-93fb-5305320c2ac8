import Taro, { Component } from '@tarojs/taro';
import { Image, Picker, View, Input } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import  ReportView  from './report-view/index';

export default class Index extends Component {

  constructor(props) {
    super(props);
    this.state = {
      status: true,
      reportList: [],
      fReportList: [],
      mReportList: [],
      mGrid: '',
      fGrid: ''
    };
  }

  componentDidMount() {
    this.queryUserInfo();
  }

  queryUserInfo = async () => {
    const { patientPid: pid } = this.props;
    const { code, data } = await Api.queryUserInfo({ pid });
    if (code == 0) {
      const { patientList } = data;
      let val = {};
      patientList.map((item) => {
        const { grid } = item;
        if (item.familyMemberName == '女方') {
          val.fGrid = grid;
        } else {
          val.mGrid = grid;
        }
        this.getReportList('', item);
      });
      this.setState(val);
    }
  }

  getReportList = async (searchTxt, val) => {
    let grid = '';
    let familyMemberName = '';
    if (!searchTxt) {
      const { grid: gridOne, familyMemberName: familyMemberNameOne } = val;
      grid = gridOne;
      familyMemberName = familyMemberNameOne;
    }
    const { patientPid: pid } = this.props;
    const { status } = this.state;
    const param = {
      pid,
      grid: grid || (status ? mGrid : fGrid),
      // grid: '10230377',
      reportName: searchTxt || ''
    }
    const { code, data } = await Api.getReportList(param);
    if (code == 0) {
      const { items } = data;
      let reportData = {};
      if (familyMemberName == '女方' || !status) {
        reportData.fReportList = items;
      } else {
        reportData.mReportList = items;
        reportData.reportList = items;
      }
      this.setState(reportData)
    }
  }

  clickEvent = (val) => {
    const { fReportList, mReportList } = this.state;
    let param = { status: val };
    if (val) {
      param.reportList = mReportList;
    } else {
      param.reportList = fReportList;
    }
    this.setState(param)
  }

  search = (e) => {
    this.getReportList(e.target.value)
  }

  render() {
    const {
      status,
      reportList
    } = this.state;
    const { patientPid: pid } = this.props;
    return (
      <View className={`${s.container}`}>
        <View className={`{${s.searchModule}}`}>
          <Input className={`${s.search}`} type='text' confirmType='search' placeholder='搜索' onInput={(e) => this.search(e)} />
        </View>
        <View className={`${s.reportItems}`}>
          <View onClick={() => this.clickEvent(true)} style={{ opacity: status ? 1 : 0.5 }}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/man.png')} />
            <View className={`${s.reportName}`}>男方报告</View>
          </View>
          <View onClick={() => this.clickEvent(false)} style={{ opacity: !status ? 1 : 0.5 }}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/woman.png')} />
            <View className={`${s.reportName}`}>女方报告</View>
          </View>
        </View>
        {(reportList && reportList.length > 0) ? reportList.map((item, index) => {
          return <ReportView data={item} key={index} pid={pid} />
        }) : <View className={`${s.noReport}`}>暂无报告</View>}
      </View>
    );
  }
}
