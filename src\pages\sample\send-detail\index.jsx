import { View, Image, Text, Icon } from "@tarojs/components";
import Taro, { Component } from "@tarojs/taro";
import Empty from '@/component/empty'

import copyPng from '@/resources/images/sample/copy.png'
import arrowPng from '@/resources/images/sample/arrow.png'
import logisticsPng from '@/resources/images/sample/logistics.png'

import * as API from '../api'
import s from './index.module.scss'

export const STATUS_MAP = {
  S: '已寄件',
  C: '已取消',
  P: '异常',
  G: '已签收'
}

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      detail: {},
      sampleList: []
    }
  }

  componentWillMount() {
    const { id } = this.$router.params
    this.getDetail(id)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '样本寄送详情',
    navigationBarTextStyle: 'black',
  };

  async getDetail(id) {
    const { code, data } = await API.getOrderDetail({ orderId: id })
    if (code !== 0) return
    this.setState({
      detail: data,
      sampleList: data.sampleData
    })
    //console.log('getOrderDetail中数据', data)
  }

  /**
   * 复制运单号
   * @param {*} waybillNo 
   * @param {*} e 
   */
  copyWaybillNo = (waybillNo, e) => {
    e.stopPropagation()
    Taro.setClipboardData({
      data: waybillNo
    })
  }

  /**
   * 查看物流详情-跳转快递100小程序
   * @param {*} waybillNo 
   * @param {*} e 
   */
  showLogistics = (waybillNo, e) => {
    e.stopPropagation()
    //打开小程序模式
    // Taro.navigateToMiniProgram({
    //   appId: 'wx6885acbedba59c14',
    //   path: 'pages/result/result?nu=' + waybillNo + '&com=shunfeng&querysource=third_xcx'
    // })
    //组件页面模式
    const { detail } = this.state
    Taro.navigateTo({
      url: 'plugin://kdPlugin/index?num=' + waybillNo + '&appName=' + this.getAppName() + '&mobile=' + detail.senderMobile.substring(7, 11)
    })
  }

  // 获取小程序的名称
  getAppName = () => {
    const appConfig = Taro.getApp().config || {}
    const pages = appConfig.pages || []
    // 通常第一个页面的路径即是小程序的名称
    const firstPage = pages[0] || ''
    const appName = firstPage.split('/').slice(-1)[0]
    return appName
  }

  /**
   * 取消寄件
   */
  handleCancel = () => {
    Taro.showModal({
      title: '提示',
      content: '确认取消寄送？',
      confirmColor: '#30A1A6',
      success: res => {
        if (res.confirm) {
          this.cancel()
        }
      }
    })
  }

  cancel = async () => {
    const { detail } = this.state
    const { code, data } = await API.cancelOrder({ orderId: detail.id })
    if (code !== 0) return
    if (data.resultCode !== '0') {
      Taro.showModal({
        title: '提示',
        content: data.resultMessage || '取消失败',
        showCancel: false,
      });
      return
    }
    Taro.showToast({ title: '取消成功', icon: 'none' })
    setTimeout(Taro.navigateBack, 1500)
  }


  render() {
    const { detail, sampleList } = this.state
    // console.log('测试detail', detail)
    if (!detail) return <Empty />
    return (
      <View className={s.page}>
        <View className={s.list_item}>
          <View className={s.list_item_waybill} onClick={this.copyWaybillNo.bind(this, detail.mailNo)}>
            <View className={['flex', 'f-c-center']}>
              <Text>运单号：{detail.mailNo}</Text>
              <Image src={copyPng} className={s.list_item_copy}></Image>
            </View>
            <View className={['flex', 'f-c-center']} onClick={this.showLogistics.bind(this, detail.mailNo)}>
              <Text>查看物流</Text>
              <Image src={logisticsPng} className={s.list_item_copy}></Image>
            </View>
          </View>
          <View className={s.list_item_content}>
            <View className={s.list_item_contact}>
              <Text className={s.list_item_contact_city}>{detail.senderCityName}</Text>
              <Text className={s.list_item_contact_name}>{detail.senderName}</Text>
            </View>
            <View className={s.list_item_contact}>
              <View className={['flex', 'f-c-center']}>
                <Icon
                  size={16}
                  style={{ marginRight: '3px' }}
                  type={(detail.status === 'S' || detail.status === 'G') ? 'success' : detail.status === 'C' ? 'cancel' : 'info'}
                  color={(detail.status === 'S' || detail.status === 'G') ? '#30A1A6' : detail.status === 'C' ? '#C55D5D' : '#E7AA35'}
                />
                <Text className={s.list_item_contact_status}>{STATUS_MAP[detail.status]}</Text>
              </View>
              {/* <Text className={s.list_item_contact_status}>{STATUS_MAP[detail.status]}</Text> */}
              <Image src={arrowPng} className={s.list_item_arrow}></Image>
            </View>
            <View className={s.list_item_contact}>
              <Text className={s.list_item_contact_city}>{detail.receiverCityName}</Text>
              <Text className={s.list_item_contact_name}>{detail.receiverName}</Text>
            </View>
          </View>

        </View>
        <View className={s.list_item}>

          {/* {console.log('输出日志Current sampleList:', sampleList)} */}
          <Text className={s.list_item_tips}>已寄送样本（{sampleList.length || 0}）</Text>

          {
            sampleList && sampleList ? <View className={s.list_item_samplecontent}>
              {sampleList.map(v => (
                <View key={v.sampleNumber} className={s.list_item_sample}>
                  <Text className={s.list_item_sample_text}>{v.sampleNumber} - {v.productName} - {v.sonProductName||'未选择子产品线'}</Text>
                </View>
              ))}
            </View> : <Empty text='暂无样本' />
          }
        </View>

        {/* 备注字段 */}
        <View className={s.list_item}>
          <Text className={s.list_item_tips}>备注信息</Text>
          <View className={s.list_item_sample2}>
            <Text className={s.list_item_sample_text2}>{detail.remark||'无样本备注信息'}</Text>
    
          </View>
        </View>

        {
          detail.status === 'S' ?
            <View className={s.list_item} onClick={this.handleCancel}>
              <Text className={s.list_item_del}>取消样本寄送</Text>
            </View> : null
        }

      </View>
    )
  }
}
