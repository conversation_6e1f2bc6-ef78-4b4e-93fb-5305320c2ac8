// const projectName = process.env.npm_config_dir;

module.exports = {
  env: {
    NODE_ENV: '"development"'
  },
  defineConstants: {
    $DOMAIN: '"https://zxxy.lockai.cn"',
    // $DOMAIN: '"https://hlwyy.zxxyyy.cn"',
    // $PAY_DOMAIN: '"https://upay.med.gzhc365.com"',
    $CDN_DOMAIN: `"https://zxxy.lockai.cn/hospital"`,
    // $CDN_DOMAIN: `"https://hlwyy.zxxyyy.cn/hospital"`,
  },
  mini: {
  },
  sass: {
    data: `$cdn: "https://ustatic.med.gzhc365.com/miniprogram-static/fe-his-twxapp";`
  },
  h5: {
    /**
     * 如果h5端编译后体积过大，可以使用webpack-bundle-analyzer插件对打包体积进行分析。
     * 参考代码如下：
     * webpackChain (chain) {
     *   chain.plugin('analyzer')
     *     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
     * }
     */
  }
}
