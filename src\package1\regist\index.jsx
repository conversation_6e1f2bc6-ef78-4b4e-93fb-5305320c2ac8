import Taro, { Component } from '@tarojs/taro';
import { View, Button } from '@tarojs/components'
import Field from '@/component/field';
import FieldPicker from '@/component/field/picker';
import Upload from '@/component/upload';

import * as API from './api'
import s from './index.module.scss'

const TIMER_DURATION = 60
const IdCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
const phoneReg = /^1[3456789]\d{9}$/

const titleList = [
  { label: '主任医师', value: 1, },
  { label: '副主任医师', value: 2, },
  { label: '主治医师', value: 3, },
  { label: '住院医师', value: 4, },
  { label: '医师', value: 5, },
  { label: '医士', value: 6, },
]

export default class Regist extends Component {

  constructor(props) {
    super(props)
    this.state = {
      timer: null,
      timerDuration: TIMER_DURATION,
      name: '',
      phone: '',
      code: '',
      idNo: '',
      titleIndex: '',
      title: '',
      unitName: '',
      dept: '',
      yszzzs: [],
      yszyzs: [],
      accountImg: [],
      docAccount: '',
      account: ''
    }
  }

  componentWillMount() {
    let { id, q } = this.$router.params;
    // 若扫码进来，则获取路由上的参数
    const url = decodeURIComponent(q)
    const urlParams = this.urlToObj(url);
    this.setState({
      docAccount: urlParams.userName,
      account: urlParams.acount
    })
    /** 从详情携带id过来重新申请 */
    if (id) {
      /** 填充上次报名信息 */
      this.fillLastSignInfo(id)
    }
  }

  urlToObj(url) {
    let obj = {}
    let str = url.slice(url.indexOf('?') + 1)
    let arr = str.split('&')
    for (let j = arr.length, i = 0; i < j; i++) {
      let arr_temp = arr[i].split('=')
      obj[arr_temp[0]] = arr_temp[1]
    }
    return obj
  }

  fillLastSignInfo = async (id) => {
    const { code, data } = await API.getRegistDetail({ id })
    if (code !== 0) return
    const { name, phone, idNo, title, unitName, dept, yszzzs, yszyzs, accountImg } = data
    this.setState({
      name,
      phone: phone ? phone.slice(0, 11) : '',
      idNo,
      title,
      unitName,
      dept,
      yszzzs: yszzzs ? yszzzs.split(',') : [],
      yszyzs: yszyzs ? yszyzs.split(',') : [],
      accountImg: accountImg ? accountImg.split(',') : [],
    })
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '注册申请',
    navigationBarTextStyle: 'black'
  };

  handleSetState = (key, val) => {
    this.setState({
      [key]: val
    })
  }

  onTitleChange = e => {
    const value = Number(e.detail.value)
    const product = titleList[value]
    this.setState({
      titleIndex: value,
      title: product.label,
    })
  }

  showError(title, duration = 1500) {
    Taro.showToast({ title, icon: 'none', duration });
  }

  /** type 0: 注册 1: 发送验证码 */
  async validate(type = 0) {
    const { name, phone, code, idNo, unitName } = this.state
    if (type === 1) {
      if (!phoneReg.test(phone)) return Promise.resolve('请输入正确的手机号')
      return Promise.resolve()
    }
    if (!name) return Promise.resolve('请输入姓名')
    if (!phoneReg.test(phone)) return Promise.resolve('请输入正确的手机号')
    if (!code) return Promise.resolve('请输入验证码')
    if (!idNo) return Promise.resolve('请输入身份证号')
    if (!IdCardReg.test(idNo)) return Promise.resolve('身份证格式错误')
    if (!unitName) return Promise.resolve('请输入所属单位名称')
    return Promise.resolve()
  }

  async checkPhone (){
    const { code } = await API.checkPhone({phone: this.state.phone});
    if(code == -1){
      Taro.showModal({
        title: "温馨提示",
        content:  "当前手机号码已注册账号，可以直接登录",
        confirmText: "登录",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            Taro.reLaunch({
              url: '/pages/index/index'
            });
          }
        }
      })
      
    }else{
      this.getMsg()
    }
  }
  async getMsg() {
    const { code } = await API.getMsgCode({ phone: this.state.phone })
      if (code !== 0) return
      const timer = setInterval(() => {
        const timerDuration = this.state.timerDuration - 1
        this.setState({ timerDuration })
        if (timerDuration <= 0) {
          clearInterval(this.state.timer)
          this.setState({
            timerDuration: TIMER_DURATION,
            timer: null,
          })
        }
      }, 1000)
      this.setState({ timer })
  }

  async handleTapTimer() {
    const error = await this.validate(1)
    if (error) return this.showError(error)
    if (this.state.timer) return
    if(await !this.checkPhone()) return
  }

  describe = (id) => {
    Taro.requestSubscribeMessage({
      tmplIds: [`${$templateId}`],
      success: () => {
        Taro.redirectTo({ url: `/package1/regist/detail/index?id=${id}` })
      }
    })
  }

  submit = async () => {
    const error = await this.validate()
    if (error) return this.showError(error)
    const { name, idNo, phone, code, title, unitName, dept, yszzzs, yszyzs, accountImg, docAccount, account } = this.state
    const { code: code1, data } = await API.regist({
      name,
      idNo,
      phone,
      code,
      title,
      unitName,
      dept,
      yszzzs: yszzzs.join(','),
      yszyzs: yszyzs.join(','),
      accountImg: accountImg.join(','),
      docAccount: docAccount ? account : ''
    })
    if (code1 !== 0) return
    this.describe(data)
  }

  render() {
    const { name, phone, code, timerDuration, timer, idNo,
      titleIndex, title, unitName, dept, yszzzs, yszyzs, accountImg, docAccount } = this.state

    return (
      <View className={s.page}>
        <View className={s.loginPageNotice}>
          请确保填写信息的准确性，提交申请后我们的工作人员会进行核验，请保存电话畅通。申请通过后会自动为您创建账号，请留意消息提醒。
        </View>
        <View className={s.page_body}>
          <Field
            label='姓名'
            labelWidth='220rpx'
            placeholder='请输入姓名'
            required
            value={name}
            maxlength={20}
            onSetValue={v => this.handleSetState('name', v)}
          />
          <Field
            label='手机号'
            labelWidth='220rpx'
            placeholder='请输入手机号'
            required
            value={phone}
            maxlength={11}
            onSetValue={v => this.handleSetState('phone', v)}
          />
          <Field
            label='验证码'
            labelWidth='220rpx'
            placeholder='请输入验证码'
            required
            value={code}
            maxlength={6}
            onSetValue={v => this.handleSetState('code', v)}
            renderSuffix={() => <View
              className={s.inputItemCode}
              onClick={this.handleTapTimer}
            >{ !timer ? '获取验证码' : `(${timerDuration})s` }</View>}
          />
          <Field
            label='身份证号'
            labelWidth='220rpx'
            placeholder='请输入身份证号'
            required
            value={idNo}
            maxlength={18}
            onSetValue={v => this.handleSetState('idNo', v)}
          />
          <FieldPicker
            label='职称'
            labelWidth='220rpx'
            value={titleIndex}
            valueLabel={title}
            placeholder='请选择'
            range={titleList}
            onChange={this.onTitleChange}
          />
          <Field
            label='所属单位名称'
            labelWidth='220rpx'
            placeholder='请输入完整准确的名称'
            required
            value={unitName}
            maxlength={30}
            onSetValue={v => this.handleSetState('unitName', v)}
          />
          <Field
            label='所属科室或部门'
            labelWidth='220rpx'
            placeholder='请输入所属科室或部门'
            value={dept}
            maxlength={30}
            onSetValue={v => this.handleSetState('dept', v)}
          />
          {
            docAccount &&
            <Field
              label='推荐人'
              labelWidth='220rpx'
              placeholder='请输入推荐人姓名'
              value={docAccount}
              disabled={docAccount}
              maxlength={20}
              onSetValue={v => this.handleSetState('docAccount', v)}
            /> 
          }
          <View style={{ marginTop: '12px' }}>
          <Upload
            title='请上传医师资格证书'
            subTitle='若您是医生，请务必上传'
            fileList={yszzzs}
            limit={1}
            updateFileList={list => this.setState({ yszzzs: list })}
          />
          <Upload
            title='请上传医师执业证书'
            subTitle='若您是医生，请务必上传'
            fileList={yszyzs}
            limit={1}
            updateFileList={list => this.setState({ yszyzs: list })}
          />
          <Upload
            title='请上传个人头像'
            subTitle='证件照或工作照'
            fileList={accountImg}
            limit={1}
            updateFileList={list => this.setState({ accountImg: list })}
          />
          </View>
        </View>

        <View className={s.page_footer}>
          <Button className={[s.page_footer_button, s.submit]} onClick={this.submit}>提交</Button>
          <Button className={[s.page_footer_button, s.cancel]} onClick={Taro.navigateBack}>取消</Button>
          <View className={s.page_footer_records} onClick={() => Taro.navigateTo({ url: '/package1/regist/list/index' })}>申请记录</View>
        </View>
      </View>
    )
  }
}
