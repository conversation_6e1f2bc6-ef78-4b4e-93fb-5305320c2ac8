import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { Tabs } from '@/component/Tabs'
import Chat from '@/component/chat'
import Article from './Child/Article'
import Activity from './Child/Activity'
import s from './index.module.scss'

/** 遗传知识Tab id */
const KNOWLEDGE_ID = 1

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      activeTab: 1,
      list: [{ label: '遗传知识', value: KNOWLEDGE_ID }, { label: '培训活动', value: 2, }],
    }
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '遗传课堂',
    navigationBarTextStyle: 'black'
  };

  handleSwitchTabs = (e) => {
    this.setState({ activeTab: e })
  }


  render() {
    const { activeTab, list } = this.state
    return (
      <View className={s.page}>
        <View className={[s.page_header, activeTab === KNOWLEDGE_ID && s.zero_bottom]}>
          <Tabs value={activeTab} list={list} setValue={this.handleSwitchTabs} />
        </View>

        {
          activeTab === KNOWLEDGE_ID ? <Article /> : <Activity />
        }
         <Chat />
      </View>
    )
  }
}
