import Taro, { Component } from '@tarojs/taro'

import Activity from '@/pages/classroom/Child/Activity'

export default class ActivityList extends Component {

  componentDidMount() {
    // const param = this.$router.params
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '培训活动',
    navigationBarTextStyle: 'black'
  };

  render() {
    return <Activity isGuest={1} />
  }
}
