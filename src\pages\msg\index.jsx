import Taro, {Component} from '@tarojs/taro';
import {Image, Picker, Text, View} from '@tarojs/components';
import { Tabs } from '@/component/Tabs'
import {ChatType} from '@/utils/config';
import DocHheaderMan from '@/static/image/avatar.png'
import Empty from '@/component/empty'

import s from './index.module.scss';
import * as Api from './api';
import {getFormatDate} from '../../utils/utils';

export default class Index extends Component {

  constructor(props) {
    super(props);

    this.state = {
      list: [{ label: '待接诊', value: '0' }, { label: '已结束', value: '1', }],
      activeIndex: '0',
      consultationList: [],
      date: getFormatDate(0, '-'),
      dateIndex: '7',
      dateList: [],
      showEmpty: true
    };
  }

  componentWillMount() {
  }

  componentDidMount() {
    this.getDateList();
  }

  componentWillUnmount() {
  }

  componentDidShow() {
    this.changeTopBar();
  }

  componentDidHide() {
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '问诊',
    navigationBarTextStyle: 'black'
  };

   changeTopBar = async (index) => {
    if (index === undefined) index = this.state.activeIndex;
    const i = String(index);
    this.setState({activeIndex: i}, this.getList);
  }

  getDateList() {
    let arr = [];
    for (let i = -7; i <= 7; i++) {
      let dat = getFormatDate(i, '-');
      arr.push(dat);
    }
    this.setState({dateList: arr});
  }

  async bindTimeChange(e) {
    const index = e.detail.value;
    await this.setState({
      dateIndex: index,
      date: this.state.dateList[index]
    }, this.getList);
  }

  async onTapGotoDetail(item) {
    const {id = '', pid = '', doctorCode = '', depCode = '', grid = '', isEndFeeType = '', chatType = '', patName = '', consultationId = ''} = item;
    const {activeIndex} = this.state;
    if (item.chatType === ChatType.clinic) {
      Taro.showLoading({title: '加载中', mask: true});
      const {code, data} = await Api.creatChat({pid, doctorCode, depCode, grid});
      Taro.hideLoading();
      if (code == '0') {
        Taro.navigateTo({
          url: `/pages/consultation/chat/index?id=${data}&isEndFeeType=${isEndFeeType}&chatType=${chatType}&patName=${patName}`
        });
      }
    } else {
      Taro.navigateTo({
        url: `/pages/consultation/chat/index?id=${id}&isEndFeeType=${activeIndex}&chatType=${chatType}&patName=${patName}&consultationId=${consultationId}&pid=${pid}`
      });
    }

  }

  async getList() {
    Taro.showLoading({title: '加载中', mask: true});
    this.setState({showEmpty: true});
    const {date, activeIndex} = this.state;
    const {code, data = []} = await Api.list({isEndFeeType: activeIndex, beginDate: date, endDate: date});
    Taro.hideLoading();

    if (code == '0') {
      this.setState({
        consultationList: data,
        showEmpty: !data.length
      })
    }
  }

  render() {
    const {
      list,
      activeIndex,
      dateList,
      dateIndex,
      date,
      showEmpty,
      consultationList
    } = this.state;

    const genChatType = (type) => {
      switch (type) {
        case ChatType.clinic:
          return '网络门诊';
        case ChatType.consultation:
          return '会诊';
        default:
          return '';
      }
    }

    return (
      <View className={`${s.container}`}>
        <View className={[s.page_header]}>
          <Tabs value={activeIndex} list={list} setValue={this.changeTopBar} />
        </View>
        <View className={s.pickerWrap}>
          {/* <View>筛选日期：</View> */}
          <View className={s.picker}>
            <Picker
              mode='selector'
              onChange={this.bindTimeChange}
              range={dateList}
              value={dateIndex}
            >
              {date || '全部日期'}
            </Picker>
          </View>
        </View>
        {(activeIndex == '0' || activeIndex == '1') && !showEmpty && (
          <View className={s.list}>
            {consultationList.map((item, index) => {
              return (
                <View className={s.listItem} onClick={() => this.onTapGotoDetail(item)} key={index}>
                  <View className={s.head}>
                    <View className={s.headLeft}>{item.registerDate}</View>
                    <View className={[s.headRight, ChatType.clinic === item.chatType && s.net]}>{genChatType(item.chatType)}</View>
                  </View>
                  <View className={s.main}>
                    <Image
                      src={DocHheaderMan}
                      mode='aspectFit'
                      className={s.mainLeft}
                    />
                    <View className={s.mainRight}>
                      <View className={s.rightHead}>
                        <View className={s.name}>{item.patName}</View>
                        {/* <View className={s.extra}>{item.pid}</View> */}
                      </View>
                      {
                        item.chatType === ChatType.consultation ?
                        <View className={s.rightBottom}>
                          邀请方: <Text>{item.doctorHospitalName}</Text>
                        </View> : null
                      }
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
        )}
        {
          showEmpty ? <Empty text='太棒了！当前所有问诊都处理完了' /> : null
        }
      </View>
    );
  }
}
