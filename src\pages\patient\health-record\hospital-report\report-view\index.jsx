import Taro, { Component } from '@tarojs/taro';
import { Image, Picker, View, Input } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';

export default class Index extends Component {

  constructor(props) {
    super(props);
    this.state = {
      status: false,
      reportDetails: []
    };
  }

  componentDidMount() {

  }

  getReportDetail = async (status) => {
    const { pid, data: { fyjg, reportId, type } } = this.props;
    let param = {
      pid,
      grid: '10230377',
      fyjg,
      reportId,
      type,
    }
    const { code, data } = await Api.getReportDetail(param);
    if (code == 0) {
      const { items } = data;
      this.setState({
        detailData: data,
        reportDetails: items,
        status: !status,
      })
    }
  }

  clickEvent = () => {
    const { status } = this.state;
    if(!status){
      this.getReportDetail(status);
    }else{
      this.setState({
        status: !status,
      })
    }
  }

  render() {
    const {
      status,
      reportDetails = [],
      detailData = {}
    } = this.state;
    const { data } = this.props;
    return (
      <View>
        <View className={`${s.reportTitle}`} style={{ borderRadius: status ? '6px 6px 0 0' : '6px' }} onClick={this.clickEvent}>
          <View className={`${s.titleInfo}`}>
            <View className={`${s.title}`}>{data.reportName}</View>
            <text>报告时间 {data.reportTime}</text>
          </View>
          <View className={`${s.viewShow}`}>{!status ? '展开 v' : '收起 >'}</View>
        </View>
        {
          status && reportDetails && reportDetails.length > 0 ?
          <View>
            <View className={`${s.itemModule}`}>
              <View className={s.item}>检查项目</View>
              <View className={s.item}>状态</View>
              <View className={s.item}>结果</View>
              <View className={s.item}>单位</View>
              <View className={s.item}>参考值</View>
            </View>
            {reportDetails && reportDetails.length > 0 && reportDetails.map((item, index) => {
              return <View key={index} className={`${s.itemData}`} >
                <View className={s.item}>{item.itemName}</View>
                <View className={s.item}>{item.abnormal}</View>
                <View className={s.item}>{item.result}</View>
                <View className={s.item}>{item.itemUnit}</View>
                <View className={s.item}>{item.refRange}</View>
              </View>
            })}
          </View> : status ?
          <View>
            {
              detailData.patientName &&
              <View className={s.itemCard}>
                <View className={s.cardTit}>就诊人</View>
                <View className={s.cardContent}>{detailData.patientName}</View>
              </View>
            }
            {
              detailData.deptName &&
              <View className={s.itemCard}>
                <View className={s.cardTit}>开方科室</View>
                <View className={s.cardContent}>{detailData.deptName}</View>
              </View>
            }
            {
              detailData.doctorName &&
              <View className={s.itemCard}>
                <View className={s.cardTit}>开方医生</View>
                <View className={s.cardContent}>{detailData.doctorName}</View>
              </View>
            }
            {
              detailData.checkName &&
              <View className={s.itemCard}>
                <View className={s.cardTit}>报告名称</View>
                <View className={s.cardContent}>{detailData.checkName}</View>
              </View>
            }
            {
              detailData.checkPart &&
              <View className={s.itemCard}>
                <View className={s.cardTit}>检查部位</View>
                <View className={s.cardContent}>{detailData.checkPart}</View>
              </View>
            }
            {
              detailData.checkSituation &&
              <View className={s.itemCard}>
                <View className={s.cardTit}>检查所见</View>
                <View className={s.cardContent}>{detailData.checkSituation}</View>
              </View>
            }
            {
              detailData.option &&
              <View className={s.itemCard}>
                <View className={s.cardTit}>诊断意见</View>
                <View className={s.cardContent}>{detailData.option}</View>
              </View>
            }
            
          </View> : ''
        }
      </View>
    );
  }
}
