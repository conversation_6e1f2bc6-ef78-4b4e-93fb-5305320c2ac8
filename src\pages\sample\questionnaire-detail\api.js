import Taro from '@tarojs/taro';
import { request, post, uploadFile as uploadFileUtil } from '../../../utils/request';

/**
 * 获取问卷详情
 * @param {Object} params - 请求参数
 * @param {String} params.id - 问卷ID
 * @returns {Promise}
 */
export function getSurveyDetail(params = {}) {
  return post('/api/questionphone/getquestionscopeforid', params);
}

/**
 * 获取问卷答案
 * @param {Object} params - 请求参数
 * @param {String} params.id - 问卷ID
 * @param {String} params.questionUserId - 问卷用户ID
 * @returns {Promise}
 */
export function getSurveyAnswer(params = {}) {
  return post('/api/questionphone/getquestiondetailbyid', params);
}

/**
 * 保存问卷答案
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function saveQuestion(params = {}) {
  return post('/api/questionphone/savequestion', params);
}

/**
 * 获取问卷类型列表
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getQuestionTypes(params = {}) {
  return request({
    url: '/api/survey/types',
    method: 'GET',
    data: params
  });
}

/**
 * 上传文件
 * @param {String} filePath - 文件路径
 * @param {String} fileType - 文件类型 image/pdf
 * @returns {Promise}
 */
export function uploadFile(filePath, fileType = 'image') {
  console.log('问卷上传文件:', filePath, fileType);

  // 确保文件路径有效
  if (!filePath) {
    console.error('文件路径无效');
    return Promise.reject(new Error('文件路径无效'));
  }

  // 使用医生端现有的上传方法
  return uploadFileUtil(filePath)
    .then(url => {
      console.log('上传成功，返回URL:', url);
      return {
        code: 0,
        data: {
          url: url
        }
      };
    })
    .catch(error => {
      console.error('上传失败:', error);
      throw error;
    });
}
