page {
  height: 100%;
  background: $color-bg;
}
.container {
  .page_header{
    padding: 10px 24px 24px;
    background-color: #FFF;
  }
  &.white{
    height: 100%;
  }
  .pickerWrap{
    display: flex;
    width: 90%;
    margin: 20px auto;
    .picker{
      margin: 0 auto;
      color: #000;
      padding: 0 10px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      &::after{
        margin: 6px 0 0 12px;
        content: '';
        display: inline-block;
        border: 10px solid #CCC;
        border-right-color: transparent;
        border-left-color: transparent;
        border-bottom-color: transparent;
      }
    }
  }

  // display: flex;
  // align-items: center;
  .noData {
    width: 100%;
    text-align: center;
    padding-top: 25%;
    font-size: 30px;
    color: $color-text;
    image {
      width: 400px;
      height: 190px;
      margin-bottom: 22px;
    }
  }
}
.topbarBox{
  position: relative;
  padding-bottom: 100px;
  .topBar{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: #fff;
    font-size: 34px;
    color: $color-title;
    box-shadow:0px 4px 8px 2px rgba(0,0,0,0.04);
    z-index: 999;
    .barItem{
      position: relative;
      &.barActive{
        color: #3eceb6;
        &::after{
          content: '';
          height: 4px;
          width: 120px;
          background: #3eceb6;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
}
.searchBox{
  width:90%;
  height: 60px;
  margin: 20px auto;
  background: #fff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  input{
    flex: 1;
    color: #cccccc;
    padding-left: 20px;
    font-size: 30px;
    line-height: 40px;
  }
  .icon{
    width: 30px;
    height: 30px;
    margin-right: 30px;
  }
}
.list{
  width: 90%;
  margin: 0 auto;
  .listItem{
    border-radius: 10px;
    margin-bottom: 20px;
    background: #FFF;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.12);
    .head{
      display: flex;
      color: #000;
      line-height: 40px;
      padding: 24px 32px;
      justify-content: space-between;
      border-bottom: .5px solid rgba(0, 0, 0, 0.10);
      .headLeft{
        font-size: 28px;
        font-style: normal;
        font-weight: bold;
        line-height: 42px;
      }
      .headRight{
        padding: 2px 16px;
        border-radius: 44px;
        background: rgba(48, 161, 166, 0.12);
        color: #3F969D;
        font-size: 24px;
        font-weight: bold;
        line-height: 36px;
        &.net{
          background: rgba(26, 99, 166, 0.12);
          color: #1A63A6;
        }
      }
    }
    .main{
      display: flex;
      padding: 24px 32px;
      align-items: center;
      .mainLeft{
        width: 100px;
        height: 100px;
        margin-right: 40px;
        image{
          width: 100%;
        }
      }
      .mainRight{
        .rightHead{
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          .name{
            margin-right: 20px;
            color: rgba(0, 0, 0, 0.90);
            font-size: 32px;
            font-weight: bold;
            line-height: 48px;
          }
          .extra{
            color: rgba(0, 0, 0, 0.70);
            font-size: 26px;
            line-height: 39px;
          }
        }
        .rightBottom{
          color: #000;
          /* 28R */
          font-size: 28px;
          line-height: 42px; /* 150% */
        }
      }
    }
    .inviteSource {
      padding: 0 40px 20px;
      text {
        color: $color-primary;
      }
    }
    .bottom{
      padding: 0 40px 20px 40px;
    }
  }
}
