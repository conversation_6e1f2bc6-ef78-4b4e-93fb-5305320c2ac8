.page {
  padding: 24px;
}

.list_item {
  margin-bottom: 20rpx;
  padding: 30rpx 30rpx 20rpx 30rpx;
  background-color: #FFF;
  border-radius: 20rpx;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.12);
  font-size: 28rpx;

  &_waybill {
    color: #333;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &_content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 50rpx 0 30rpx 0;
  }

  &_copy {
    width: 32rpx;
    height: 32rpx;
    margin-left: 20rpx;
  }

  &_contact {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;

    &_city {
      font-size: 36rpx;
      font-weight: bold;
      color: #000;
    }

    &_name {
      font-size: 28rpx;
      color: #333;
    }

    &_status {
      font-size: 28rpx;
      color: #333;
      padding-bottom: 5rpx;
    }
  }

  &_time {
    color: #333;
    line-height: 70rpx;
  }

  &_arrow {
    width: 120rpx;
    height: 16rpx;
  }

  &_tips {
    color: #000;
    font-weight: 600;
    font-size: 32rpx;
  }


  &_samplecontent {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 20rpx;

  }

  &_sample {
    display: flex;
    align-items: center;
    border: 1rpx solid $color-primary;
    width: 100%;
    justify-content: space-evenly;
    border-radius: 50rpx;
    padding: 10rpx 20rpx;
    background: #d2f9fb;
    margin: 10rpx 0;
    min-width: 300rpx;

    &_text {
      padding-right: 10rpx 5rpx;
      color: $color-primary;
      flex: 1;
      text-align: left;
      white-space: normal;
      word-wrap: break-word;
      word-break: break-all;
      // 增加行高
      line-height: 1.5;
      // 调整字体大小
      font-size: 26rpx;
      // 添加内边距
      padding: 0 20rpx;

    }

    &_text2 {
      padding-right: 10rpx;
      border: 1rpx;
      color: black;
      flex: 1;
      text-align: left;
    }


    &_img {
      width: 32rpx;
      height: 32rpx;
    }
  }


  &_sample2 {
    display: flex;
    align-items: center;
    border: 1rpx ;
    width:auto;
    justify-content: space-evenly;
    border-radius: 50rpx;
    padding: 10rpx 10rpx;
    margin: 10rpx 0;

    &_text2 {
      padding-right: 10rpx;
      border: 1rpx;
      color: black;
      flex: 1;
      text-align: left;
    }

  }





  &_del {
    color: $color-primary;
    display: flex;
    justify-content: center;
    font-size: 32rpx;
  }
}

// .block{
//   padding: 0 32px;
//   background: #FFF;
//   border-radius: 8px;
// }

// .item{
//   padding: 32px 0;
//   display: flex;
//   flex-direction: column;
//   align-items: flex-start;
//   box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.06) inset;
//   &:last-of-type{
//     box-shadow: none;
//   }
//   &_title{
//     color: rgba(0, 0, 0, 0.90);
//     font-size: 28px;
//     font-weight: bold;
//     line-height: 42px;
//   }
//   &_value{
//     margin-top: 8px;
//     color: rgba(0, 0, 0, 0.70);
//     font-size: 32px;
//     font-weight: 400;
//     line-height: 48px;
//   }
//   &_files{
//     margin-top: 24px;
//     display: flex;
//     flex-wrap: wrap;
//     &__item{
//       padding: 16px;
//     }
//     &__inner{
//       width: 160px;
//       height: 160px;
//     }
//   }
// }