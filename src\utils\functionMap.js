// urlType: 1 // 普通小程序内链接
// urlType：2 // 其他小程序
// urlType：3 // 跳转h5

export default {
  '1': {
    name: '预约挂号',
    icon: 'yygh-v1.png',
    url: '/pages/register/deptlist/index',
  },
  '2': {
    name: '门诊缴费',
    icon: 'mzjf-v1.png',
    url: '/pages/treat/untreatlist/index',
  },
  '3': {
    name: '住院服务',
    icon: 'zyfw-v1.png',
    url: '/pages/inpatient/home/<USER>',
  },
  '4': {
    name: '就诊卡充值',
    icon: 'jzkcz-v1.png',
    url: '/pages/recharge/amount/index',
  },
  '5': {
    name: '添加就诊人',
    icon: 'tjjzr-v1.png',
    url: '/pages/usercenter/userlist/index',
  },
  '6': {
    name: '当日挂号',
    icon: 'drgh-v1.png',
    url: '/pages/register/deptlist/index',
  },
  '7': {
    name: '排队候诊',
    icon: 'pd-v1.png',
    url: '/pages/queue/queue/index',
  },
  '8': {
    name: '报告查询',
    icon: 'bgcx-v1.png',
    url: '/pages/report/reportlist/index',
  },
  '9': {
    name: '在线取号',
    icon: 'zxqh-v1.png',
    url: '/pages/takeno/index/index',
  },
  '10': {
    name: '住院日清单',
    icon: 'zyrqd-v1.png',
    url: '/pages/inpatient/userlist/index?keyword=zyrqd',
  },
  '11': {
    name: '住院缴费',
    icon: 'zyjf-v1.png',
    url: '/pages/inpatient/amount/index',
  },
  '12': {
    name: '住院人信息',
    icon: 'zyrxx-v1.png',
    url: '/pages/inpatient/userlist/index?keyword=zyrxx',
  },
  '13': {
    name: '问卷调查',
    icon: 'wjdc-v1.png',
    url: '/pages/survey/surveylist/index',
  },
  '14': {
    name: '微网站',
    icon: 'wwz-v1.png',
    url: '/pages/microsite/home/<USER>',
  },
  '15': {
    name: '就诊须知',
    icon: 'jzxz-v1.png',
    url: '/pages/auth/developing/index',
  },
  '16': {
    name: '智能客服',
    icon: 'znkf-v1.png',
    url: '',
    urlType: 2,
    appId: 'wxa89db5e57ee2a65e',
    envVersion: 'trial'
  },
  '17': {
    name: '无号助手',
    icon: 'whzs-v1.png',
    url: '/pages/auth/developing/index',
  },
  '18': {
    name: '挂号记录',
    icon: 'ghjl-v1.png',
    url: '/pages/register/recordlist/index',
  },
  '19': {
    name: '缴费记录',
    icon: 'jfjl-v1.png',
    url: '/pages/treat/recordlist/index',
  },
  '20': {
    name: '门诊电子病历',
    icon: 'blb-v1.png',
    url: '/pages/medicalrecord/recordlist/index'
  },
  '21': {
    name: '商保理赔',
    icon: 'sblp-v1.png',
    url: '',
    urlType: 2,
    appId: 'wx3fa4464915791992'
  },
  '22': {
    name: '体检预约',
    icon: 'img-tjyy-v1.png',
    url: '',
    urlType: 2,
    appId: 'wxafb9e3096f9810c3',
    bcColor: '#1FCEC3'
  },
  '23': {
    name: '停车缴费',
    icon: 'img-tcjf-v1.png',
    url: '',
    urlType: 2,
    appId: 'wx083c2a74f4c1cddd',
    bcColor: '#FF6D6A'
  },
  '24': {
    name: '住院点餐',
    icon: 'img-zydc-v1.png',
    url: '',
    urlType: 2,
    appId: 'wxa7f456245317a772',
    bcColor: '#FFA63B'
  },
  '25': {
    name: '信用租赁',
    icon: 'xyzl-v1.png',
    url: '/pages/auth/developing/index'
  },
  '26': {
    name: '在线问诊',
    icon: 'zyfw-v1.png',
    url: '/ihpages/pages/consult/deptlist/deptlist',
  },
  '30': {
    name: '快速缴费',
    icon: 'mzjf-v1.png',
    url: '/pages/treat/untreatlist/index',
  },
  'test-zndz': {
    name: '智能导诊',
    icon: 'zndz-v1.png',
    url: 'https://mp.med.gzhc365.com/views/p099/index.html#/guide/guidelist',
    urlType: 3,
    sceneType: 1,
  },
  '31': { //临时演示数据
    name: 'AI导诊',
    icon: 'zndz-v1.png',
    url: 'https://mp.med.gzhc365.com/views/AI-guidance/index.html?auth=1&platformSource=1&platformId=99991#/guidance/guidance?source=miniapp',
    urlType: 3,
    sceneType: 1,
  },
  'test-cccx': { //临时演示数据
    name: '产程查询',
    icon: 'hlwyy-v1.png',
    url: '',
    urlType: 2,
    appId: 'wx4fe7970bd94d058d',
    desc: '产程全了解',
  },
  'test-yfxx':{
    name: '孕妇学校',
    icon: 'img-yfxx-v1.png',
    url: 'https://wxauth.wuaitec.com/wxauth/c/haici/mini/1',
    urlType: 3,
    desc: '智能云端管理',
    id: 'test-yfxx',
    bcColor: '#33C0FF'
  },
};
