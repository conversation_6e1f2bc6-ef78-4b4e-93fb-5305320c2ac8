.page{
  &_header{
    padding: 10px 24px 24px;
    background-color: #FFF;
  }
  &_body{
    padding: 24px;
  }
}

.search{
  padding: 16px 32px 16px 32px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  input{
    color: #000;
  }
}

.header_daterange{
  margin-top: 24px;
  display: flex;
}
.header_daterange__picker{
  flex: 1;
  text-align: center;
}
.picker_content{
  color: rgba(0, 0, 0, 0.9);

  &::after{
    margin: 10px 0 0 12px;
    content: '';
    display: inline-block;
    border: 10px solid rgba(0, 0, 0, 0.9);
    border-right-color: transparent;
    border-left-color: transparent;
    border-bottom-color: transparent;
  }
}

.info_box{
  margin-left: 16px;
  padding: 4px 8px 4px 8px;
  font-size: 20px;
  font-weight: bold;
  border-radius: 4px;
  color: #2D666F;
  background: rgba(63, 150, 157, 0.15);
  &.offline{
    color: #2A2A2A;
    background: rgba(0, 0, 0, 0.07);
  }
}

.list_item{
  margin-bottom: 12px;
  padding: 24px;
  background-color: #FFF;
  border-radius: 8px;
  &_status{
    margin-bottom: 24px;
    font-size: 28px;
    font-weight: 600;
    color: #000;
  }
  &_row{
    margin-top: 8px;
    font-size: 28px;
    font-weight: 400;
    color: $color-text;
    @include ellipsisLn()
  }
}
