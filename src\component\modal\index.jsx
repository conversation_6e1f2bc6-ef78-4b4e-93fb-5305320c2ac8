import Taro, { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import { View } from '@tarojs/components'
import s from './index.module.scss'

export default class Modal extends Component {

  constructor(props) {
    super(props)
  }

  render() {
    const { show, title, children, showCancel, showconfirm, cancelText, confirmText, onCancel, onOk } = this.props
    return (
      <View>
        {show ?
        <View className={[s.c_modal, 'f-center']}>
          <View className={[s.c_modal_container, 'f-col']}>
              { title ? <View className={s.c_modal_title}>{title}</View> : null }
              <View className={s.c_modal_content}>
                { children }
              </View>
              { showconfirm || showCancel ?
                <View className={[s.c_modal_footer, 'f-center']}>
                  { showCancel ? <View onClick={() => onCancel && onCancel()} className={[s.c_modal_footer_cancel, 'f-1', 'f-center']}>{cancelText}</View> : null }
                  { showCancel ? <View className={s.divider}></View> : null }
                  { showconfirm ? <View onClick={() => onOk && onOk()} className={[s.c_modal_footer_confirm, 'f-1', 'f-center']}>{confirmText}</View> : null }
                </View> : null
              }
          </View>
        </View>
        : null }
      </View>
    )
  }
}

Modal.propTypes = {
  show: PropTypes.bool,
  title: PropTypes.string,
  showCancel: PropTypes.bool,
  showconfirm: PropTypes.bool,
  cancelText: PropTypes.string,
  confirmText: PropTypes.string,
  onCancel: PropTypes.func,
  onOk: PropTypes.func,
};
Modal.defaultProps = {
  show: true,
  showCancel: true,
  showconfirm: true,
  cancelText: '取消',
  confirmText: '确认'
};
