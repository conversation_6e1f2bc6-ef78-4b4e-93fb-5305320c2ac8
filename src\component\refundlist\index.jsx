import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeIndex: 0
    }
  }

  componentWillMount() { }

  componentDidMount() { }

  componentWillUnmount() { }

  componentDidShow() { }

  componentDidHide() { }

  render() {
    const { refundList = [] } = this.props;
    const { activeIndex } = this.state;
    return (
      <View className='wgt-refund-list'>
        {
          refundList.length === 1 ?
            <View className='wgt-refundlist'>
              <View className='wgt-refundlist-tit'>退款进度</View>
              <View className='wgt-refundlist-box'>
                <View className={`wgt-refundlist-item ${refundList[0].refundStatus == 2 ? 'active' : ''}`}>
                  <View className='wgt-refundlist-item-icon'></View>
                  <View className='wgt-refundlist-item-line'></View>
                  <View className='wgt-refundlist-item-title'>已到账</View>
                </View>
                <View className={`wgt-refundlist-item ${(refundList[0].refundStatus == 1) || (refundList[0].refundStatus == 3) ? 'active' : ''}`}>
                  <View className='wgt-refundlist-item-icon'></View>
                  <View className='wgt-refundlist-item-line'></View>
                  <View className='wgt-refundlist-item-title'>已退款（预计1到7个工作日）</View>
                </View>
                <View className='wgt-refundlist-item'>
                  <View className='wgt-refundlist-item-icon'></View>
                  <View className='wgt-refundlist-item-line'></View>
                  <View className='wgt-refundlist-item-title'>发起退款</View>
                </View>
              </View>
            </View>
            :
            null
        }

        {
          refundList.length > 1 ?
            <View className='wgt-refundlist'>
              <View className='wgt-refundlist-tit'>
                退款进度
              <text className='f-color-text f-fs-text'>（有{refundList.length}笔退款）</text>
              </View>
              <View
                className='wgt-refundlist-select'
              >
                <View className='wgt-refundlist-select-label'>退款{activeIndex + 1}：</View>
                <View className='wgt-refundlist-select-value'>
                  {refundList[activeIndex].refundSerialNo}
                </View>
                <View className='wgt-refundlist-select-icon'>
                  <View className='iconfont'>&#xe626;</View>
                </View>
                <View className='wgt-refundlist-box'>
                  <View className={`wgt-refundlist-item ${refundList[activeIndex].refundStatus == 2 ? 'active' : ''}`}>
                    <View className='wgt-refundlist-item-icon'></View>
                    <View className='wgt-refundlist-item-line'></View>
                    <View className='wgt-refundlist-item-title'>已到账</View>
                  </View>
                  <View className={`wgt-refundlist-item ${(refundList[activeIndex].refundStatus == 1) || (refundList[activeIndex].refundStatus == 3) ? 'active' : ''}`}>
                    <View className='wgt-refundlist-item-icon'></View>
                    <View className='wgt-refundlist-item-line'></View>
                    <View className='wgt-refundlist-item-title'>已退款（预计1到7个工作日）</View>
                  </View>
                  <View className='wgt-refundlist-item'>
                    <View className='wgt-refundlist-item-icon'></View>
                    <View className='wgt-refundlist-item-line'></View>
                    <View className='wgt-refundlist-item-title'>发起退款</View>
                  </View>
                </View>
              </View>
            </View>
            :
            null
        }
      </View>
    )
  }
}
