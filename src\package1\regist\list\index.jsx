import Taro, { Component } from '@tarojs/taro'
import { View } from '@tarojs/components'
import Empty from '@/component/empty'
import s from './index.module.scss'
import * as API from '../api'
import { STATUS_MAP } from '../config'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      list: []
    }
  }

  componentWillMount() {
    this.getList()
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '注册申请记录',
    navigationBarTextStyle: 'black'
  };

  async getList() {
    const {code, data } = await API.getRegistList()
    if (code !== 0) return
    this.setState({ list: data || [] })
  }


  render() {
    const { list } = this.state
    return (
    <View className={s.page}>
      { list.map(v => (
        <View
          key={v.id}
          className={[s.list_item, 'f-row', 'f-c-center']}
          onClick={() => Taro.navigateTo({ url: `/package1/regist/detail/index?id=${v.id}` })}
        >
          <View className='f-1'>
            <View className={s.list_item_title}>{v.name}</View>
            <View className={s.list_item_time}>{v.createTime}</View>
          </View>
          <View className={s.list_item_status} style={{ color: STATUS_MAP[v.status].color }}>{STATUS_MAP[v.status].label}</View>
        </View>
      )) }
      {
        (!list || !list.length) ? <Empty text='暂无申请记录' /> : null
      }
    </View>
    )
  }
}
