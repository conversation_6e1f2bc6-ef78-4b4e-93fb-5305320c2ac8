import Taro, { Component } from '@tarojs/taro'
import { View, Icon, Text, Image, Button } from '@tarojs/components'
import * as API from '../api'
import s from './index.module.scss'
import { STATUS_MAP } from '../config'

export default class RegistDetail extends Component {

  constructor(props) {
    super(props)
    this.state = {
      detail: {},
    }
  }

  componentWillMount() {
    const { id } = this.$router.params;
    this.getDetail(id)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '注册申请',
    navigationBarTextStyle: 'black'
  };

  async getDetail(id) {
    const { code, data } = await API.getRegistDetail({ id })
    if (code !== 0) return
    this.setState({ detail: { ...data } })
  }

  getStatus = (status) => {
    return STATUS_MAP[status] || {}
  }

  login = () => {
    Taro.redirectTo({ url: '/pages/index/index' })
  }

  signAgain = async () => {
    const { id } = this.state.detail
    Taro.redirectTo({ url: `/package1/regist/index?id=${id}` })
  }

  previewImage(current, urls) {
    Taro.previewImage({ current, urls})
  }

  render() {
    const { detail } = this.state
    return (
      <View className={s.detail_page}>
        <View className='f-row f-c-center'>
          <Icon
            style={{ marginRight: '12px' }}
            size={32}
            type={this.getStatus(detail.status).icon}
            color={this.getStatus(detail.status).color}
          />
          <Text
            className={s.herder_title}
            style={{ color: this.getStatus(detail.status).color }}
          >{this.getStatus(detail.status).label}</Text>
        </View>
        <View className={s.herder_tips}>
          {
            detail.status === 1 ? '您的平台账号注册申请已提交成功，请保持电话畅通，工作人员将尽快完成审核' :
            detail.status === 2 ? '您提交的平台账号注册申请已审核通过，账号已创建。请通过手机验证码登录。' :
            detail.status === 3 ? '您提交的平台账号注册申请审核未通过，请核对后重新提交申请。' : ''
          }
        </View>

        <View className={s.page_body}>
          <View className={[s.block, s.bottom_border]}>
            <View className={s.title}>
              <Text>申请信息</Text>
            </View>
            <View>
              <View className={s.line}>
                <Text className={s.line_label}>申请时间</Text>
                <Text className={s.line_value}>{detail.createTime}</Text>
              </View>
              { [2, 3].includes(detail.status) ?
              <View className={s.line}>
                <Text className={s.line_label}>审核时间</Text>
                <Text className={s.line_value}>{detail.auditDate}</Text>
              </View> : null }
              { [3].includes(detail.status) ?
              <View className={s.line}>
                <Text className={s.line_label}>驳回原因</Text>
                <Text className={s.line_value}>{detail.reason}</Text>
              </View> : null }
            </View>
          </View>

          <View className={[s.block, s.bottom_border]}>
            <View className={s.title}>
              <Text>个人信息</Text>
            </View>
            <View>
              <View className={s.line}>
                <Text className={s.line_label}>姓名</Text>
                <Text className={s.line_value}>{detail.name}</Text>
              </View>
              <View className={s.line}>
                <Text className={s.line_label}>手机号</Text>
                <Text className={s.line_value}>{detail.phone}</Text>
              </View>
              <View className={s.line}>
                <Text className={s.line_label}>身份证号</Text>
                <Text className={s.line_value}>{detail.idNo}</Text>
              </View>
              <View className={s.line}>
                <Text className={s.line_label}>职称</Text>
                <Text className={s.line_value}>{detail.title}</Text>
              </View>
              <View className={s.line}>
                <Text className={s.line_label}>所属单位名称</Text>
                <Text className={s.line_value}>{detail.unitName}</Text>
              </View>
              <View className={s.line}>
                <Text className={s.line_label}>所属科室或部门</Text>
                <Text className={s.line_value}>{detail.dept}</Text>
              </View>
            </View>
          </View>

          <View className={s.block}>
            <View className={s.line3}>
              <Text className={s.line3_label}>医师资格证书</Text>
              <View className={s.line3_value}>
                {
                  detail.yszzzs && detail.yszzzs.split(',').map(url => (
                    <View key={url} className={s.line3_image}>
                      <Image
                        src={url}
                        className={s.line3_image_inner}
                        onClick={() => this.previewImage(url, detail.yszzzs.split(','))}
                      />
                    </View>
                  ))
                }
              </View>
            </View>
            <View className={s.line3}>
              <Text className={s.line3_label}>医师执业证书</Text>
              <View className={s.line3_value}>
                {
                  detail.yszyzs && detail.yszyzs.split(',').map(url => (
                    <View key={url} className={s.line3_image}>
                      <Image
                        src={url}
                        className={s.line3_image_inner}
                        onClick={() => this.previewImage(url, detail.yszyzs.split(','))}
                      />
                    </View>
                  ))
                }
              </View>
            </View>
            <View className={s.line3}>
              <Text className={s.line3_label}>个人头像</Text>
              <View className={s.line3_value}>
                {
                  detail.accountImg && detail.accountImg.split(',').map(url => (
                    <View key={url} className={s.line3_image}>
                      <Image
                        src={url}
                        className={s.line3_image_inner}
                        onClick={() => this.previewImage(url, detail.accountImg.split(','))}
                      />
                    </View>
                  ))
                }
              </View>
            </View>
          </View>
        </View>

        <View className={s.page_footer}>
          { detail.status === 2 ? <Button className={[s.btn, s.submit]} onClick={this.login}>登 录</Button> : null }
          { [3].includes(detail.status) ? <Button className={[s.btn, s.submit]} onClick={this.signAgain}>重新申请</Button> : null }
        </View>
      </View>
    )
  }
}
