import Taro from "@tarojs/taro";

import { WebView } from "@tarojs/components";

export default class WebViewPage extends Taro.Component {

  constructor(props) {
    super(props)
    this.state = {
      url: '',
    }
  }

  componentWillMount() {
    const { url, title } = this.$router.params
    Taro.setNavigationBarTitle({ title })
    this.setState({
      url: decodeURIComponent(url),
    })
  }

  render() {
    const { url } = this.state
    return (
      <WebView src={url} />
    )
  }
}