import Taro, { Component } from '@tarojs/taro';
import { View, Checkbox, Button, Text } from '@tarojs/components';
import Field from '@/component/field';
import FieldTextarea from '@/component/field/textarea';
import Upload from '@/component/upload';

import s from './index.module.scss'
import * as API from '../api'

var IdCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
var phoneReg = /^1[3456789]\d{9}$/

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      /** 活动id */
      id: '',
      /** 额外资料 */
      moreFileLabel: '',

      /** 是否扫码进来(没有用户信息) */
      isGuest: 0,

      formData: {
        name: '',
        idNo: '',
        education: '',
        major: '',
        jobName: '',
        jobTitle: '',
        organization: '',
        deptName: '',
        phone: '',
        contact: '',
        workExp: '',
        professionalism: '',
        docs: '',

        amount: '',
      },

      files: {
        jobCertificate: [],
        credentials: [],
        docsImg: [],
      },

      isAggree: false,

      activeDetail: {},

    }
  }

  componentWillMount() {
    let { isGuest, lastSignId, id } = this.$router.params
    this.setState({ id })
    API.getActivityDetail({ id }).then(res => {
      const { code, data } = res
      if (code !== 0) return
      this.setState({ moreFileLabel: data.docs, activeDetail: data })
      if (data.feeType == 2) {
        this.handleSetState('amount', 1);
      }
    })
    /** 从报名详情携带id过来重新报名 */
    if (lastSignId) {
      /** 填充上次报名信息 */
      this.fillLastSignInfo(lastSignId)
      return
    }
    /** 游客不填充信息 */
    isGuest = isGuest ? Number(isGuest) : 0
    if (isGuest) {
      this.setState({ isGuest })
      return
    }

    /** 登录用户填充身份信息 */
    const { formData, files, } = this.state
    const { dept = '', institutionName = '', userName = '', idNo = '', phone = '', title = '',
      yszyzs = '', yszzzs = '' } = JSON.parse(Taro.getStorageSync('userInfo') || '{}');
    this.setState({
      formData: {
        ...formData,
        deptName: dept,
        organization: institutionName,
        name: userName,
        jobTitle: title,
        phone,
        idNo: idNo,
      },
      files: {
        ...files,
        jobCertificate: yszyzs ? yszyzs.split(',') : [],
        credentials: yszzzs ? yszzzs.split(',') : [],
      },
    })
  }

  fillLastSignInfo = async (id) => {
    const { code, data } = await API.getSignDetail({ id })
    if (code !==0) return
    const { jobCertificate, credentials, docsImg, ...restData } = data
    this.setState({
      formData: {
        ...restData,
      },
      files: {
        jobCertificate: jobCertificate ? jobCertificate.split(',') : [],
        credentials: credentials ? credentials.split(',') : [],
        docsImg: docsImg ? docsImg.split(',') : [],
      },
    })
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '遗传培训',
    navigationBarTextStyle: 'black'
  };

  handleSetState = (key, val) => {
    const { formData } = this.state
    this.setState({
      formData: {
        ...formData,
        [key]: val
      }
    })
  }

  handleSetFileList = (key, list) => {
    const { files } = this.state
    this.setState({
      files: {
        ...files,
        [key]: list
      }
    })
  }

  handleSelectAggrement = () => {
    const isAggree = !this.state.isAggree
    this.setState({ isAggree })
  }

  showError(title, duration = 1500) {
    Taro.showToast({ title, icon: 'none', duration });
  }

  async validate() {
    const { isAggree, formData, files, activeDetail } = this.state
    if (!isAggree) return Promise.resolve('请先勾选下方须知')
    const { name, idNo, education ,major, organization, phone, contact, workExp, professionalism, amount } = formData
    const { jobCertificate, credentials, docsImg } = files;
    if (!name) return Promise.resolve('请填写姓名')
    // if (!idNo) return Promise.resolve('请填写身份证')
    // if (!IdCardReg.test(idNo)) return Promise.resolve('身份证格式错误')
    // if (!education) return Promise.resolve('请填写学历')
    // if (!major) return Promise.resolve('请填写专业')
    if (!organization) return Promise.resolve('请填写所在单位')
    // if (!phone) return Promise.resolve('请填写联系电话')
    // if (!phoneReg.test(phone)) return Promise.resolve('电话格式错误')
    // if (!contact) return Promise.resolve('请填写微信/邮箱')
    // if (!workExp) return Promise.resolve('请填写学习与工作经历')
    // if (!professionalism) return Promise.resolve('请填写专业水平')
    // if (!jobCertificate || !jobCertificate.length) return Promise.resolve('请上传进修申请表照片')
    if (activeDetail.feeType == 1 && !amount) return Promise.resolve('请输入报名学习月数')
    // if (!credentials || !credentials.length) return Promise.resolve('请上传资格证书照片')
    // if (moreFileLabel && !docs) return Promise.resolve(moreFileLabel)
    // if (moreFileLabel && (!docsImg || !docsImg.length)) return Promise.resolve(moreFileLabel)
    return Promise.resolve()
  }

  submit = async () => {
    const error = await this.validate()
    if (error) return this.showError(error)
    const { id, formData, files, activeDetail } = this.state
    const prams = {...formData}
    if (activeDetail.feeFlg == 1) {
      prams.feePrice = activeDetail.feePrice;
      prams.totalFee = parseInt(prams.amount * (activeDetail.feePrice || 0));
      if (activeDetail.feeType == 2) {
        prams.amount = '';
      }
    } else {
      prams.amount = '';
    }
    const { code, data } = await API.signActivity({
      ...prams,
      trainId: id,
      ...Object.keys(files).reduce((res, key) => {
        res[key] = files[key].join(',')
        return res
      }, {}),
    })
    if (code !== 0) return
    if (activeDetail.feeFlg == 1) {
      const orderData = await API.saveOrder({
        totalFee: prams.totalFee,
        bizType: 'outside_check',
        patientName: prams.name,
        hisRecepitNo: data,
      })
      if (orderData.code === 0) {
        this.getPayConfig(orderData.data.orderId, data);
      } 
      // else {
      //   Taro.navigateTo({ url: `/pages/classroom/records/detail?id=${data}` })
      // }
    } else {
      Taro.navigateTo({ url: `/pages/classroom/records/detail?id=${data}` })
    }
  }

  async getPayConfig(orderId, id) {
    const { code, data} = await API.prePayOrder({ orderId });
    if (code !== 0) return;
    const { payOrderId } = data;
    /** 获取可选的支付方式 暂时先定死微信支付 */
    const res2 = await API.choosePayMode({ payMode: 'weixin_miniapp', payOrderId });
    this.chooseWeChatWapPay(res2.data.payParameter, id);
  }

  async chooseWeChatWapPay(config, id) {
    let requestPaymentRes;
    try {
      requestPaymentRes = await Taro.requestPayment({ ...config });
    } catch (e) {
      console.log(e);
      requestPaymentRes = e;
    }
    if (requestPaymentRes.errMsg == 'requestPayment:fail cancel') {
      // 取消支付
      // Taro.navigateTo({ url: `/pages/classroom/records/detail?id=${id}` });
    } else if (requestPaymentRes.errMsg == 'requestPayment:ok') {
      // 支付成功
      Taro.navigateTo({ url: `/pages/classroom/records/detail?id=${id}` });
    }
  }


  render() {

    const { formData, files, isAggree, isGuest, moreFileLabel, activeDetail } = this.state;
    const { name, idNo, education, major, jobName, jobTitle, organization, deptName, phone, contact,
      workExp, professionalism, docs, amount, } = formData
    const { jobCertificate, credentials, docsImg } = files
    return (
      <View className={s.page}>
        <View className={s.page_body}>
          <Field
            label='姓名'
            placeholder='请输入姓名'
            required
            value={name}
            maxlength={20}
            onSetValue={v => this.handleSetState('name', v)}
          />
          {/* <Field
            value={idNo}
            label='身份证'
            maxlength={18}
            placeholder='请输入身份证号'
            required
            onSetValue={v => this.handleSetState('idNo', v)}
          />
          <Field
            value={education}
            label='学历'
            placeholder='请输入'
            required
            maxlength={20}
            onSetValue={v => this.handleSetState('education', v)}
          />
          <Field
            value={major}
            label='所学专业'
            placeholder='请输入'
            required
            maxlength={20}
            onSetValue={v => this.handleSetState('major', v)}
          />
          <Field
            value={jobName}
            required={isGuest}
            label='职务'
            placeholder='请输入'
            maxlength={20}
            onSetValue={v => this.handleSetState('jobName', v)}
          />
          <Field
            value={jobTitle}
            label='职称'
            required={isGuest}
            placeholder='请输入'
            maxlength={20}
            onSetValue={v => this.handleSetState('jobTitle', v)}
          /> */}
          <Field
            value={organization}
            label='所在单位'
            placeholder='请输入'
            required
            maxlength={50}
            onSetValue={v => this.handleSetState('organization', v)}
          />
          {/* <Field
            value={deptName}
            label='所在科室'
            required={isGuest}
            placeholder='请输入'
            maxlength={20}
            onSetValue={v => this.handleSetState('deptName', v)}
          />
          <Field
            value={phone}
            label='联系电话'
            type='number'
            maxlength={11}
            placeholder='请输入'
            required
            onSetValue={v => this.handleSetState('phone', v)}
          />
          <Field
            value={contact}
            label='邮箱'
            placeholder='请输入'
            required
            maxlength={20}
            onSetValue={v => this.handleSetState('contact', v)}
          /> */}

          <Upload
            title='请上传进修申请表照片（需盖章完毕）'
            fileList={jobCertificate}
            updateFileList={list => this.handleSetFileList('jobCertificate', list)}
          />

          {/* <Upload
            title='请上传资格证书照片'
            fileList={credentials}
            updateFileList={list => this.handleSetFileList('credentials', list)}
          />

          <FieldTextarea
            value={workExp}
            label='主要学习与工作经历'
            placeholder='请输入'
            required
            maxlength={100}
            onSetValue={v => this.handleSetState('workExp', v)}
          />

          <FieldTextarea
            value={professionalism}
            style={{ marginTop: '24rpx' }}
            label='本人专业水平'
            placeholder='请输入'
            required
            maxlength={100}
            onSetValue={v => this.handleSetState('professionalism', v)}
          />

          {
            moreFileLabel ?
              <FieldTextarea
                value={docs}
                label={moreFileLabel}
                style={{ marginTop: '24rpx' }}
                placeholder='请输入'
                maxlength={100}
                onSetValue={v => this.handleSetState('docs', v)}
              /> : null
          }
          {
            moreFileLabel ?
            <Upload
              title=''
              fileList={docsImg}
              updateFileList={list => this.handleSetFileList('docsImg', list)}
            /> : null
          } */}
          {activeDetail.feeFlg == 1 ? <View>
            {activeDetail.feeType == 1 ? <Field
              label='学习月数'
              placeholder='请输入报名学习月数'
              required
              type='number'
              value={amount}
              onSetValue={v => this.handleSetState('amount', v)}
            /> : null}
            <View className={s.wx_field}>
              <View className={[s.wx_field__body, 'f-row', 'f-c-center']}>
                <Text className={s.wx_field__label}>报名费</Text>
                <View className={[s.wx_field__input, 'f-1']}>{(((amount || 0) * (activeDetail.feePrice || 0)) / 100).toFixed(2)}元</View>
              </View>
            </View>
          </View> : null}
        </View>

        <View className={s.page_footer}>
          <View onClick={this.handleSelectAggrement}>
            <Checkbox
              className={s.page_footer_checkbox}
              color='#30A1A6'
              value={isAggree}
              checked={isAggree}
            ></Checkbox>
            <Text className={s.page_footer_tips}>我已知晓活动内容，且报名信息准确，我要报名</Text>
          </View>

          <Button className={[s.page_footer_submit, !isAggree && s.disabled]} disabled={!isAggree} onClick={this.submit}>
            {activeDetail.feeFlg == 1 ? '缴费报名' : '确 定'}
          </Button>
        </View>
      </View>
    )
  }
}
