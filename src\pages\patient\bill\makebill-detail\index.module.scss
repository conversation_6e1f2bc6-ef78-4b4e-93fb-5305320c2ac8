.page{
  // padding: 24px;
  // padding-top: 48px;
}
.header{
  padding: 48px 24px;
  &_title{
    color: #3F969D;
    font-family: PingFang SC;
    font-size: 48px;
    font-style: normal;
    font-weight: 600;
    line-height: 72px; /* 150% */
  }
  &_tips{
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.50);
    font-family: PingFang SC;
    font-size: 28px;
    font-style: normal;
    font-weight: 400;
    line-height: 42px; /* 150% */
  }
}
.img{
  margin-right: 16px;
  width: 196px;
  height: 196px;
  border-radius: 8px;
}

.btn{
  display: flex;
  padding: 24px;
  margin: 0 24px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex: 1 0 0;
  border-radius: 76px;
  background: var(--Linear, linear-gradient(90deg, #30A1A6 0%, #2F848B 100%));
  color: var(--grey-grey-00, #FFF);
  text-align: center;
  /* 34B */
  font-family: PingFang SC;
  font-size: 34px;
  font-style: normal;
  font-weight: 600;
  line-height: 52px; /* 152.941% */
}

.page_body{
  padding: 24px;
}
.block{
  padding: 32px;
  background-color: #FFF;
  border-radius: 24px;
}
.fee_summary{
  margin-bottom: 32px;
  font-family: PingFang SC;
  font-size: 28px;
  font-weight: 600;
  line-height: 42px; /* 150% */
  &_left{
    color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
  }
  &_right{
    color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));
    font-size: 24px;
    font-weight: 500;
    &_money{
      color: #D2962B;
    }
  }
}

.table{
  margin-top: 48px;
  &_row{
    display: flex;
    align-items: stretch;
    &:nth-last-of-type(1) .table_col{
      border-bottom: 1px solid $color-bg;
    }
  }
  &_col{
    flex: 1 1 50%;
    padding: 16px;
    border-top: 1px solid $color-bg;
    border-left: 1px solid $color-bg;
    color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));
    font-family: PingFang SC;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 36px; /* 150% */
    // @include ellipsisLn();
    &:nth-of-type(1) {
      text-align: left;
    }
    &:nth-last-of-type(1) {
      text-align: right;
      border-right: 1px solid $color-bg;
    }
    &.table_col_h{
      color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
      padding: 8px 16px;
      font-weight: 500;
    }
  }
}
.title{
  margin: 32px 0;
  color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
  /* 28B */
  font-family: PingFang SC;
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 42px; /* 150% */
}

.line{
  margin-bottom: 24px;
  display: flex;
  align-items: flex-start;
  color: var(--grey-grey-40, rgba(0, 0, 0, 0.40));
  /* 32R */
  font-family: PingFang SC;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 48px; /* 150% */
  &_label{
    flex: 0 0 210px;
  }
  &_value{
    color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
    word-break: break-all;
  }
}
