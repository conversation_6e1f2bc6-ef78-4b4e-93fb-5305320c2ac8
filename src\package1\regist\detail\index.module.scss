.detail_page{
  padding: 36px;
  padding-top: 48px;
}
.herder_title{
  font-size: 48px;
  font-weight: bold;
  line-height: 72px;
}
.herder_tips{
  margin-top: 16px;
  margin-bottom: 24px;
  color: rgba(0, 0, 0, 0.50);
  font-size: 28px;
  line-height: 42px;
}
.page_body{
  padding: 32px;
  background-color: #FFF;
  border-radius: 24px;
  .block{
    &.bottom_border{
      margin-bottom: 32px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.07);
    }
  }
  .title{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.90);
    font-size: 28px;
    line-height: 42px;
  }
  .title_icon{
    width: 24px;
    height: 24px;
    &.collapsed{
      transform: rotate(180deg);
    }
  }

  .line{
    margin: 24px 0;
    display: flex;
    align-items: center;
    font-size: 32px;
    line-height: 48px;
    font-weight: 400;
    &_label{
      flex: 0 0 240px;
      color: #00000066;
    }
    &_value{
      color: #000000E5
    }
  }
  .line3{
    margin: 16px 0;
    display: flex;
    flex-direction: column;
    font-size: 32px;
    line-height: 48px;
    &_label{
      margin-bottom: 16px;
      color: #00000066;
    }
    &_value{
      display: flex;
      flex-wrap: wrap;
    }
    &_image{
      padding: 16px;
      &_inner{
        width: 160px;
        height: 160px;
      }
    }
  }
}

.page_footer {
  margin-top: 48px;
}
.btn{
  margin-bottom: 24px;
  height: 96px;
  line-height: 96px;
  font-size: 34px;
  border-radius: 76px;
  font-weight: 600;
  &::after{
    border: none;
  }
  &.submit{
    color: #fff;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  }
}
