import Taro, { Component } from '@tarojs/taro';
import { Button, Image, Navigator, ScrollView, Text, View } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import WPage from '@/component/wpage';
import DetailStatus from '@/component/detailstatus';

export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '会诊单详情',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);
    this.state = {
      id: '',
      detail: {},
      status: '',
      isExpandPatient: true,
      statusConfig: {},
      isNew: 0,
    };
  }

  componentWillMount() {
  }

  componentDidMount() {
    const { id, isNew } = this.$router.params;
    this.setState({ id, isNew }, this.queryRecordDetail);
  }

  componentWillUnmount() {
    const { isNew } = this.state;
    if (isNew == 1) {
      Taro.navigateBack({
        delta: 2
      });
    }
  }

  componentDidShow() {
  }

  componentDidHide() {
  }

  setNavColor = (colorParam) => {
    Taro.setNavigationBarColor(colorParam);
  };

  queryRecordDetail = async () => {
    const { id } = this.state;
    const { code, data: detail } = await Api.queryRecordDetail({ id });
    if (code === 0) {
      const statusConfig = this.genConfig(detail.status);
      this.setState({ detail, status: detail.status, statusConfig });
    }
  }

  genConfig(status) {
    switch (status) {
      case 0:
        return { statusName: '待审核', status: 'P' };
      case 1:
        return { statusName: '申请通过待安排', status: 'S' };
      case 2:
        return { statusName: '会诊中', status: 'S' };
      case 3:
        return { statusName: '已结束', status: 'S' };
      case 4:
        return { statusName: '申请不通过', status: 'F' };
      case 5:
        return { statusName: '已取消申请', status: 'C' };
      case 6:
        return { statusName: '已取消', status: 'C' };
    }
  }

  previewImage() {
    const { detail } = this.state;
    const urls = detail.fileList.map(item => item.url);
    Taro.previewImage({ urls });
  }

  cancelApply = async () => {
    const _this = this;
    const { detail } = this.state;
    Taro.showModal({
      title: '温馨提示',
      content: '确定取消申请？',
      success: async function (res) {
        if (res.confirm) {
          const { code } = await Api.cancelApply({ consultationId: detail.consultationId });
          if (code !== 0) return;
          Taro.showToast({
            title: '取消成功',
            icon: 'success'
          });
          setTimeout(_this.queryRecordDetail, 2000);
        }
      }
    });
  }

  render() {
    const {
      detail = {},
      status,
      statusConfig,
      isExpandPatient,
    } = this.state;

    return (
      <WPage>
        <DetailStatus
          onSetNavColor={this.setNavColor}
          statusConfig={statusConfig}
        />
        <ScrollView style={{ paddingBottom: '50px' }}>
          <View className={s.container}>
            <View className={s.row}>
              <Text>申请单号</Text>
              <Text>{detail.orderNo}</Text>
            </View>
            <View className={s.row}>
              <Text>申请时间</Text>
              <Text>{detail.applyTime}</Text>
            </View>
            {
              status !== 0 && status !== 5 && (
                <View className={s.row}>
                  <Text>审核时间</Text>
                  <Text>{detail.auditStatusLog.time}</Text>
                </View>
              )
            }

            {
              (status === 1 || status === 2 || status === 4 || status === 6) && detail.auditStatusLog.remarks && (
                <View className={s.row}>
                  <Text>审核备注</Text>
                  <Text>{detail.auditStatusLog.remarks}</Text>
                </View>
              )
            }

            {
              status === 2 && (
                <View className={s.row}>
                  <Text>计划会诊时间</Text>
                  <Text>{detail.plannedTime}</Text>
                </View>
              )
            }

            {
              status === 3 && (
                <View className={s.row}>
                  <Text>结束时间</Text>
                  <Text>{detail.currentStatusLog.time}</Text>
                </View>
              )
            }
            {
              status === 5 && (
                <View className={s.row}>
                  <Text>申请取消时间</Text>
                  <Text>{detail.currentStatusLog.time}</Text>
                </View>
              )
            }
            {
              status === 6 && (
                <View className={s.row}>
                  <Text>取消会诊时间</Text>
                  <Text>{detail.currentStatusLog.time}</Text>
                </View>
              )
            }

            <View className={s.row}>
              <Text>邀请方</Text>
              <Text>{`${detail.doctorHospitalName || ''} ${detail.doctorName || ''}`}</Text>
            </View>

            {
              (status === 2 || status === 3 || status === 6) && (
                <View className={s.row}>
                  <Text>受邀医师</Text>
                  <Text>
                    {detail.doctorList.map((doctor) => {
                      return `${doctor.name}${doctor.userType === '1' ? '(主诊)' : ''}\n`
                    }).join('')}
                  </Text>
                </View>
              )
            }

            <View className={s.row}>
              <Text>患者信息</Text>
              <Text>{detail.patientName}</Text>
            </View>
            <View className={`${s.patientDetail} ${isExpandPatient && s.expand}`}>
              <View
                className={s.row}
                onClick={() => this.setState({ isExpandPatient: !isExpandPatient })}
              >
                <Text>患者详情</Text>
                <Text style={{ fontWeight: 600 }}>
                  {isExpandPatient ? '收起' : '展开'}
                  <Text className={s.arrow}>⟩</Text>
                </Text>
              </View>
              <View>
                <View className={s.row}>
                  <Text>主诉</Text>
                  <Text>{detail.mainIssue}</Text>
                </View>
                <View className={s.row}>
                  <Text>现病史</Text>
                  <Text>{detail.nowIssue}</Text>
                </View>
                <View className={s.row}>
                  <Text>既往史</Text>
                  <Text>{detail.historyIssue}</Text>
                </View>
                <View className={s.row}>
                  <Text>家族史</Text>
                  <Text>{detail.familyIssue}</Text>
                </View>
                <View className={s.row}>
                  <Text>个人史</Text>
                  <Text>{detail.personalIssue}</Text>
                </View>
                <View className={s.row}>
                  <Text>婚育史</Text>
                  <Text>{detail.marriageIssue}</Text>
                </View>
                <View className={s.row}>
                  <Text>月经史</Text>
                  <Text>{detail.periodIssue}</Text>
                </View>
                <View className={s.row}>
                  <Text>现有诊断</Text>
                  <Text>{detail.existDiagnosis}</Text>
                </View>
                <View className={s.row}>
                  <Text>会诊目的</Text>
                  <Text>{detail.purpose}</Text>
                </View>
                <View className={s.row}>
                  <Text>会诊原因</Text>
                  <Text>{detail.reason}</Text>
                </View>
              </View>
            </View>
            {
              detail.fileList.length && (
                <View className={`${s.attachFile} ${s.row}`}>
                  <Text>附件</Text>
                  <View className={s.previewBtn} onClick={this.previewImage}>
                    <Image src={detail.fileList[0].url} />
                    <Text>点击查看图片</Text>
                  </View>
                </View>
              )
            }

            {
              status === 3 && (
                <View className={`${s.record} ${s.row}`}>
                  <Text>会诊报告</Text>
                  <Navigator url={`/pages/consultation-opinion/consultation-report/index?consultationId=${detail.consultationId}`}>详细</Navigator>
                </View>
              )
            }
          </View>

          {
            detail.shouldEnd && status === 0 && <Button className={s.cancelBtn} onClick={this.cancelApply}>取消申请</Button>
          }

          {
            detail.shouldEnter && status === 2 && <Navigator className={s.entryBtn} url={`/pages/consultation/chat/index?id=${detail.chatGroupId}&isEndFeeType=${0}&chatType=${3}&patName=${detail.patientName}&consultationId=${detail.consultationId}&pid=${detail.patientPid}`}>进入会诊间</Navigator>
          }
        </ScrollView>
      </WPage>
    );
  }
}
