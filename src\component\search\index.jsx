import Taro, { Component } from '@tarojs/taro';
import { View, Image, Text, Input, Block } from '@tarojs/components';
import cn from 'classnames';
import * as Utils from '@/utils/utils';
import Empty from '../empty/index';

import * as Api from './api';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      // 搜索内容
      searchValue: '',
      // 搜索结果
      dataList: [],
      searchFocus: false,
    };
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  search = async (word) => {
    this.setState({ dataList: [] });
    const { code, data = {} } = await Api.subSearch({ inputData: word });
    const { searchValue } = this.state;
    if (code != 0 || searchValue !== word) {
      // 搜索结果与当前输入框不等，数据不录入
      return false;
    }
    const dataList = [];
    Object.entries(data).map((item) => {
      if((item[1].deptList || []).length > 0 || (item[1].doctorList || []).length > 0) {
        dataList.push({ title: item[0], ...item[1] });
      }
    });
    this.setState({ dataList });
  }

  /**
   * 开启搜索状态
   * @returns {string}
   */
  bindSearchFocus = () => {
    this.setState({ searchFocus: true });
  }
  /**
   * 关闭搜索状态
   * @returns {string}
   */
  bindSearchBlur = () => {
    this.setState({ searchFocus: false, dataList: [], searchValue: '' });
  }
  /**
   * 输入搜索内容
   * @param e
   * @returns {string}
   */
  bindSearchInput = (e) => {
    const { value } = e.detail;
    this.setState({ searchValue: value });

    if (!value) {
      this.setState({ dataList: [] });
      return false;
    }

    // 获取搜索结果
    clearTimeout(this.searchTimer || '');

    this.searchTimer = setTimeout(() => {
      this.search(value);
    }, 200);

    return value;
  }
  /**
   * 跳转到医生详情页
   * @param item
   */
  bindToDocInfo = (item) => {
    const { deptId, doctorId, hisId } = item;
    const query = Utils.jsonToQueryString({ doctorId, deptId, subHisId: hisId });
    Taro.navigateTo({ url: `/pages/register/docinfo/index?${query}` });
  }
  /**
   * 跳转到医生列表页
   * @param item
   */
  bindToDocList = (item) => {
    const { deptId, deptName, hisId } = item;
    const query = Utils.jsonToQueryString({ deptName, deptId, hisId });
    Taro.navigateTo({ url: `/pages/register/doclist/index?${query}` });
  }
  /**
   * 查看更多
   */
  bindMore = (hisId) => {
    const { searchValue } = this.state;
    const query = Utils.jsonToQueryString({ word: searchValue, hisId });
    Taro.navigateTo({ url: `/pages/register/search/index?${query}` });
  }

  render () {
    const { dataList = [], searchFocus, searchValue } = this.state;

    const { icon, noborder = false, children } = this.props;

    const iconStyle = {};

    if (icon) {
      iconStyle.background = `url(${icon}) no-repeat center center / 100% 100%`;
    }

    return (
      <View className={cn('g-search', { 'noborder': noborder })}>
        { searchFocus ? <View className='m-serach-seat' /> : null }
        <View className={`m-search ${searchFocus ? 'active' : ''}`}>
          <View className='search-ipt'>
            <View className='ipt-icon' style={iconStyle} />
            <Input
              className='ipt'
              placeholder='搜索科室、医生'
              onFocus={this.bindSearchFocus}
              onInput={this.bindSearchInput}
              value={searchValue}
              maxLength='20'
            />
          </View>
          { searchFocus ? <View className='extra-cancel' onClick={this.bindSearchBlur}>取消</View> : null }
          <View style={{ display: searchFocus ? 'none' : 'block' }}>{children}</View>
        </View>
        {
          searchFocus ?
            <View className='m-search-content'>
              <View className='search-content'>
                {
                  !dataList.length ? <Empty text={!searchValue.length ? '请输入关键字搜索' : '暂未查询到相关数据'} /> : null
                }
                {
                  dataList.map((obj) => {
                    const { doctorList = [], deptList = [] } = obj;
                    return (
                      <View className='content' key={obj.title}>
                        <View className='content-title'>{obj.title}</View>
                        {
                          doctorList.length ?
                            <View className='content-list'>
                              {
                                doctorList.map((item, index) => {
                                  if (index >= 2) {
                                    return null;
                                  }
                                  const nameSplitIdx = item.doctorName.indexOf(searchValue);
                                  let nameStart = '';
                                  let nameEnd = '';
                                  if (searchValue && nameSplitIdx > -1) {
                                    nameStart = item.doctorName.substr(0, nameSplitIdx);
                                    nameEnd = item.doctorName.substr(nameSplitIdx + searchValue.length);
                                  }
                                  return (
                                    <View className='list-item' onClick={() => this.bindToDocInfo(item)} key={item.doctorId}>
                                      <View className='item-image'>
                                        <Image mode='widthFix' className='image'
                                          src={item.doctorImg || `${$CDN_DOMAIN}/doc-header-man.png`}
                                        />
                                      </View>
                                      <View className='item-info'>
                                        <View className='info-title'>
                                          {
                                            nameSplitIdx > -1 ?
                                              <Block>
                                                {nameStart}
                                                <Text className='match'>{searchValue}</Text>
                                                {nameEnd}
                                              </Block>
                                              : item.doctorName
                                          }
                                          <Text className='info-text'>{item.deptName ? '/ ' : null}{item.deptName}</Text>
                                        </View>
                                        <View className='info-text'>{item.doctorTitle}</View>
                                        <View className='info-des'>{item.doctorSkill || ''}</View>
                                      </View>
                                    </View>
                                  );
                                })
                              }
                              {
                                doctorList.length > 2 ?
                                  <View className='list-item' onClick={() => this.bindMore(obj.doctorList[0].hisId)}>
                                    <View className='item-info'>
                                      <View className='info-more'>更多医生</View>
                                    </View>
                                    <View className='item-arrow' />
                                  </View> : null
                              }
                            </View> : null
                        }
                        {
                          deptList.length > 0 ?
                            <View className='content-list'>
                              {
                                deptList.map((item, index) => {
                                  if (index >= 2) {
                                    return null;
                                  }
                                  const nameSplitIdx = item.deptName.indexOf(searchValue);
                                  let nameStart = '';
                                  let nameEnd = '';
                                  if (searchValue && nameSplitIdx > -1) {
                                    nameStart = item.deptName.substr(0, nameSplitIdx);
                                    nameEnd = item.deptName.substr(nameSplitIdx + searchValue.length);
                                  }
                                  return (
                                    <View className='list-item' onClick={() => this.bindToDocList(item)} key={item.deptId}>
                                      <View className='item-info'>
                                        <View className='info-title'>
                                          {
                                            nameSplitIdx > -1 ?
                                              <Block>
                                                {nameStart}
                                                <Text className='match'>{searchValue}</Text>
                                                {nameEnd}
                                              </Block>
                                              : item.deptName
                                          }
                                        </View>
                                      </View>
                                    </View>
                                  );
                                })
                              }
                              {
                                deptList.length > 2 ?
                                  <View className='list-item' onClick={() => this.bindMore(obj.deptList[0].hisId)}>
                                    <View className='item-info'>
                                      <View className='info-more'>更多科室</View>
                                    </View>
                                    <View className='item-arrow' />
                                  </View> : null
                              }
                            </View> : null
                        }
                      </View>
                    );
                  })
                }
              </View>
            </View> : null
        }
      </View>
    )
  }
}
