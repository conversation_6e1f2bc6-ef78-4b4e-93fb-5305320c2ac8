.page{
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header{
  padding: 24px;
  background-color: #FFF;
  display: flex;
  align-items: center;
  &_daterange {
    height: 72px;
    padding: 0 32px;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 16px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.10);
    &__picker{
      flex: 1;
    }
    &__split{
      flex: 0;
      margin: 0 16px;
    }
    &__clear{
      width: 48px;
      color: #308B91;
      font-size: 24px;
    }
  }
  &_search {
    margin-left: 16px;
    border-radius: 8px;
    background: #308B91;
    height: 72px;
    line-height: 72px;
    text-align: center;
    color: #FFF;
    font-size: 32px;
    font-weight: 500;
  }
}

.contaoner {
  position: relative;
  flex: 1;
}
.body{
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  padding: 24px;
  .list{
    box-sizing: border-box;
    background-color: #FFF;
    height: 100%;
    border-radius: 8px;
    .item{
      padding: 24px 32px;
      box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.06) inset;
      &:last-of-type{
        box-shadow: none;
      }
      &_header{
        display: flex;
        align-items: center;
        &__icon{
          margin-right: 12px;
          width: 32px;
          height: 32px;
        }
        &__title{
          color: #3F969D;
          font-size: 32px;
          font-weight: 600;
          line-height: 48px; /* 150% */
        }
      }
      &_footer{
        margin-top: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba(0, 0, 0, 0.40);
        font-size: 24px;
        font-weight: 400;
        line-height: 36px; /* 150% */
      }
    }
  }
}
