import Taro from '@tarojs/taro';
/**
 * 平台id
 * @type {{platformId: number}}
 */
export const REQUEST_QUERY = {
  hisId: 242,
  platformId: 242,
  platformSource: 3,
  subSource: 2,
};

export const HC_HEADER_CONFIG = {
  'Hc-Src-Hisid': `${REQUEST_QUERY.hisId}`,
  'Hc-Proj-Info': `project/his;type/${Taro.ENV_TYPE[Taro.getEnv()]};ch/${Taro.ENV_TYPE[Taro.getEnv()]};ver/1.0`,
}

/**
 * 健康卡
 */
export const HEALTHCARDINFO = {
  ADD_HEALTHCARD: true, //是否接入健康卡，如果设置为true暂时需要手动去app.wpy把healthCard注释放开，后续优化
  CHANGE_DIRECTLY: true, //升级电子健康卡his支持直接修改
  hospitalCardConfig: {
    hospitalAddress: "海鹚大医疗产品研发中心",
    hospitalAddressEng: "Haici Province Health Commission",
    maker: "中华人民共和国国家卫生健康委员会监制",
    hisLogo: 'REPLACE_IMG_DOMAIN/his-miniapp/icon/usercenter/hc-logo.png',
  },
  healthCardConfig: {
    healthCardAddress: "青海省卫生和计划生育委员会",
    healthCardAddressEng: "Qinhai  Province  Health&Family  Commission",
    maker: "中华人民共和国国家卫生健康委员会监制",
  }
}

/**
 * 一维码类型  code39 || code128
 * @type {string}
 */
export const CODE_TYPE = 'code128';

/**
 * 前后端业务类型映射关系
 * @type {{DBGH: string, YYGH: string, YYQH: string, MZJF: string, YFKCZ: string, ZYYJBJ: string}}
 */
export const TYPE_MAP = {
  DBGH: 'current_register',
  YYGH: 'appointment_register',
  YYQH: 'take_register',
  MZJF: 'outpatient',
  YFKCZ: 'patcard_recharge',
  ZYYJBJ: 'inpatient',
  SCYJJN: 'inpatient',
  SQJCJF: 'surgery_check_pay',  // 术前检查缴费
  ZYSSJF: 'inpatient_surgery',  // 住院手术缴费
  MZSSJF: 'outpatient_surgery', // 门诊手术缴费
};

/**
 * 订阅消息模板ID
 */
 // 所有moduleId
export const MODULE_IDS = [1, 2, 5, 6, 7, 8, 9, 10, 11, 13, 18, 19, 20, 21, 22, 23, 24, 30, 31, 32, 33, 34, 40, 41, 42, 50, 51, 52, 60, 61, 62, 90, 91, 92, 93, 94, 95, 105, 106, 107];

/**
 * 是否开通代扣
 */
export const IS_WITHHOLD = false;

/**
 * 门诊缴费订单详情是否多级药品
 * @type {boolean}
 */
export const IS_SEC_ITEM = false;

/**
 * 是否支持门诊合并支付
 * @type {boolean}
 */
export const TREAT_MERGE_PAY = false;

/**
 * 前后端时间代码对应表
 * @type {string}
 */
export const TIME_MAP = {
  0: null,
  1: '上午',
  2: '下午',
  3: '晚上',
  4: '白天',
  5: '全天'
};

/**
 * 是否显示结束时间
 * @type {string}
 */
export const IS_NEED_ENDTIME = true;

/**
 * 预约挂号是否付费
 * @type {boolean}
 */
export const APPOINTMENT_REG_PAY = false;

/**
 * 当班挂号是否付费
 * @type {boolean}
 */
export const TODAY_REG_PAY = true;

/**
 * 是否接入预问诊，IS_PREINQUIRY，0：不接入，1：腾讯预问诊， 2：左手医生预问诊，VIEW_ID：2.0接入时需要配置，为对应的工程名
 */
export const PREINQUIRY = {
  IS_PREINQUIRY: 1,
  VIEW_ID: 'p099',
};

/**
 * 通用配置文件，以功能点作为切入点，便于不同医院的小程序项目实施
 */
export const _config = {
  name: '湖南家辉遗传专科医院',         // 小程序名称，用于首页展示和分享
  hisId: '242',
  rtcOrigin: 'tx',                          // 音视频方案    tx || ali
  useRecordInChat: true,
  home: {
    hospitalIntro: true,                    // 展示医院介绍
  },
  // 在线开药功能
  medicine: {
    use: false,
  },// 网约护理
  netnurse: {
    use: true,
    whiteList: true,            // 患者白名单
    dualPay: true,              // 多重支付，护士到家后再收取一次耗材费用
  },
  // 预约检查功能
  appointmentCheck: {
    use: true,
  },
  // 随访功能
  followUp: {
    use: true,
  },
  // 开具处方功能
  prescribe: {
    use: true,
    pharmacistReview: true,                     // 是否需要药师审方
    recall: true,                               // 是否可以撤回处方
    signWay: 'mediSign',                        // 签名方式 ： mediSign(医信签)，不填则为信任度
  },
  // 开具检查和检验报告功能
  inspect: {
    use: false,
    signWay: 'mediSign',                        // 签名方式 ： mediSign(医信签)，不填则为信任度
  },
  // 案例公开功能
  openCase: true,
  // 慢病管理功能
  CDM: {
    use: true,
  },
  // 直播功能
  live: {
    use: true,
    origin: 'tx',             // tx || ali
  },
  // 圈子功能
  moment: {
    use: true,
  },
  // 检查检验报告
  report: {
    use: true,
  },
  // 病历查看
  medicalRecord: {
    use: true,
  },
  // 医院是否支持修改手机号码
  modifyMobile: {
    use: true,
  },
  // 会诊功能
  consultation: {
    use: true,
  },
  // 配置视频等待时长
  waitNum: 60,
}

// 聊天类型
export const ChatType = {
  clinic: '2', // 网络门诊
  consultation: '3' // 会诊
}
