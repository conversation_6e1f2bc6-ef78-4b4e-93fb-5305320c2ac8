page {
  height: 100%;
  background: $color-bg;
}

.topModule {
  background-color: white;
  display: flex;
  // justify-content: space-between;
  justify-content: center;
  padding: 20px 20px 30px 20px;
  align-items: center;
  border-bottom: 1px solid rgb(242,242,242);
  // position: fixed;
  // top: 96px;
}

.addRecord {
  color: #fff;
  padding: 10rpx 10rpx;
  background: #3ECEB6;
  width: 25%;
  text-align: center;
  border-radius: 5px;
  margin-top: 10px;

}

.allRecord {
  // background-color: white;
  margin-bottom: 120px;
  // margin-top: 90px;
  z-index: 99;
}

.buttomStyle {
  display: flex;
  justify-content: space-between;
  position: fixed;
  margin: auto;
  right: 0;
  left: 0;
  color: #FFFFFF;
  font-size: 36px;
  bottom: 0;
  padding: 20px 30px;
  .applyRecord {
    padding: 20px 0;
    background-color: #3ECEB6;
    padding: 20px;
    text-align: center;
    width: 40%;
    border-radius: 10px;
  }
}

.isNo {
  width: 100%;
  text-align: center;
  margin-top: 50%;
  color: #888888;
}
