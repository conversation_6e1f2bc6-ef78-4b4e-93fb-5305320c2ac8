import { post, uploadFile } from '@/utils/request';

export const uploadImages = param => uploadFile(param);
/**
 * 获取状态
 */
export const getStatus = (param) => post('/api/doctor/liveStream/getStatus', param, false);

/**
 * 聊天记录的列表
 */
export const getChat = (param) => post('/api/docChat/queryChatInfo', param, false);

/**
 * 发送聊天信息
 */
export const sendMsg = (param) => post('/api/docChat/sendMessage', param);


/**
 * 结束问诊
 */
export const closure = (param) => post('/api/docChat/endFeeChat', param, false, false);

/**
 * 结束会诊权限
 */
export const hasAdminAuth = (param) => post('/api/consultation/chat/isMainDoctorOrHasConsultationManagement', param);
/**
 * 结束会诊
 */
export const endConsultation = (param) => post('/api/consultation/chat/cancelChat', param);


