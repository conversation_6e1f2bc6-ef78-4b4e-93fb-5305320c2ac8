import Taro, { Component } from "@tarojs/taro";
import { View, Image, Text, Button, Camera, Input, Block } from "@tarojs/components";
import dayjs from "dayjs";
import Empty from '@/component/empty'

import uploadPng from '@/resources/images/sample/upload.png'
import arrowPng from '@/resources/images/arrow-right.png'
import barcodePng from '@/resources/images/sample/barcode.png'
import scandPng from '@/resources/images/sample/scan.png'

import * as API from '../api'

import s from './index.module.scss'


export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      list: [],
      // 新增样本采集相关状态
      showUserScanCamera: false, // 控制用户扫描相机
      showSampleScanCamera: false, // 控制样本管扫描相机
      collectStep: 0, // 0: 初始状态, 1: 已扫描用户, 2: 已扫描样本管
      userInfo: null, // 用户信息
      sampleBarcode: '', // 样本管条形码
      orderSampleNumber: '', // 订单中的样本编号，用于比对
      isFromSampleCollection: false, // 是否通过样本采集按钮进入
      // 样本详情展示相关状态
      showDetailScanCamera: false, // 控制样本详情扫描相机
      detailUserInfo: null, // 样本详情用户信息
    }
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '收样本',
    navigationBarTextStyle: 'black'
  };

  componentDidShow() {
    this.getList()
  }

  componentWillMount() {
    // 检查相机权限
    this.checkCameraAuth()
  }

  // 检查相机权限
  checkCameraAuth = async () => {
    try {
      const res = await Taro.getSetting()
      const authorized = res.authSetting['scope.camera']
      // 如果未授权或未定义，不自动打开相机
      if (authorized === false) {
        Taro.showToast({ 
          title: '请点击扫码图标授权相机使用', 
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('获取授权失败', error)
    }
  }

  // 请求相机权限
  requestCameraAuth = () => {
    Taro.authorize({
      scope: 'scope.camera',
      success: () => {
        // 授权成功，可以打开相机
        this.setState({ showUserScanCamera: true })
      },
      fail: () => {
        // 授权失败，提示用户
        Taro.showModal({
          title: '提示',
          content: '请授权相机权限以便扫描条码',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              Taro.openSetting()
            }
          }
        })
      }
    })
  }

  async getList() {
    try {
      Taro.showLoading({ title: '加载中' });
      const { code, data = {}, message } = await API.getReadySampleList()
      Taro.hideLoading();
      
      if (code !== 0) {
        console.error('获取列表失败:', message);
        Taro.showToast({
          title: message || '获取列表失败',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      const list = data.recordList || []
      this.setState({
        list,
      });
      
      console.log('加载列表成功，数量:', list.length);
    } catch (error) {
      Taro.hideLoading();
      console.error('获取列表异常:', error);
      Taro.showToast({
        title: '获取列表异常，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }

  handleSampleUpload = async () => {
    const { list } =  this.state
    const { code } = await API.batchUpdateStatus({ ids: list.map(v => v.id), status: 2 })
    if (code !== 0) return
    Taro.showModal({
      title: '上传样本',
      content: '样本上传成功',
      showCancel: false,
      success: res => {
        if (res.confirm) {
          Taro.redirectTo({ url: '/pages/sample/already-list/index' })
        }
      }
    })
  }

  submit = async () => {
    Taro.showModal({
      title: '上传样本',
      content: '请确认是否上传此批样本',
      success: res => {
        if (res.confirm) {
          this.handleSampleUpload()
        }
      }
    })
  }

  toDetail = id => {
    Taro.navigateTo({ url: `/pages/sample/check-in/index?id=${id}` })
  }

  // 开始样本详情展示流程
  toSampleDetail = () => {
    // 检查相机权限
    Taro.getSetting().then(res => {
      const authorized = res.authSetting['scope.camera']
      if (authorized === false) {
        // 未授权，请求授权
        this.requestDetailCameraAuth()
      } else {
        // 已授权或未定义，直接打开扫描相机
        this.setState({ showDetailScanCamera: true })
      }
    }).catch(() => {
      // 获取设置失败，尝试直接打开相机
      this.setState({ showDetailScanCamera: true })
    })
  }

  // 请求样本详情扫描相机权限
  requestDetailCameraAuth = () => {
    Taro.authorize({
      scope: 'scope.camera',
      success: () => {
        // 授权成功，打开扫描相机
        this.setState({ showDetailScanCamera: true })
      },
      fail: () => {
        // 授权失败，提示用户
        Taro.showModal({
          title: '提示',
          content: '请授权相机权限以便扫描条码',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              Taro.openSetting()
            }
          }
        })
      }
    })
  }

  // 打开样本采集流程
  startSampleCollection = () => {
    this.setState({ 
      collectStep: 0,
      showUserScanCamera: true,
      userInfo: null,
      sampleBarcode: '',
      isFromSampleCollection: true // 标记为采集流程
    })
  }

  // 用户条码扫描成功处理
  handleUserScanSuccess = async (e) => {
    const scanResult = e.detail.result
    console.log('扫描用户条形码结果:', scanResult)
    
    try {
      Taro.showLoading({ title: '获取用户信息中' })
      
      // 调用API获取大健康样本订单详情
      const { code, data } = await API.getHealthOrderById(scanResult)
      
      if (code !== 0 || !data) {
        Taro.hideLoading()
        Taro.showToast({
          title: '未找到该订单信息',
          icon: 'none',
          duration: 2000
        })
        return
      }
      
      // 获取到用户信息
      let userInfo = {}
      
      // 根据问卷类型展示不同的用户信息
      if (data.questionType === '1') {
        // 成人版: 显示姓名、证件号码、联系电话
        userInfo = {
          name: data.sjzName || '未知姓名',
          idCard: data.sjzIdNum || '未知证件号',
          phone: data.sjzPhone || '未知手机号',
          id: scanResult,
          questionType: '1'
        }
      } else if (data.questionType === '2') {
        // 儿童版: 显示姓名、监护人姓名、监护人证件号码、联系电话
        userInfo = {
          name: data.sjzName || '未知姓名',
          guardianName: data.jhrName || '未知监护人姓名',
          guardianIdCard: data.jhrIdNum || '未知监护人证件号',
          phone: data.sjzPhone || '未知手机号',
          id: scanResult,
          questionType: '2'
        }
      } else {
        // 未知问卷类型，保持兼容处理
        userInfo = {
          name: data.sjzName || '未知姓名',
          idCard: data.sjzIdNum || '未知证件号',
          phone: data.sjzPhone || '未知手机号',
          id: scanResult,
          questionType: 'unknown'
        }
      }

      // 保存订单中的样本编号
      const orderSampleNumber = data.sampleNumber || '';
      
      // 更新状态
      this.setState({
        showUserScanCamera: false,
        collectStep: 1,
        userInfo,
        orderSampleNumber
      })
      
      // 打印日志，方便调试
      console.log('订单样本编号:', orderSampleNumber);

      Taro.hideLoading()
      Taro.showToast({
        title: '用户信息获取成功',
        icon: 'success',
        duration: 1500
      })
    } catch (error) {
      console.error('获取用户信息失败:', error)
      Taro.hideLoading()
      Taro.showToast({
        title: '获取用户信息失败',
        icon: 'none',
        duration: 2000
      })
    }
  }

  // 样本管条码扫描成功处理
  handleSampleScanSuccess = async (e) => {
    const scanResult = e.detail.result

    console.log('扫描到样本管编号:', scanResult)

    // 关闭扫描窗口
    this.setState({
      showSampleScanCamera: false,
      collectStep: 2,
      sampleBarcode: scanResult
    })

    Taro.showToast({
      title: '样本管编号获取成功',
      icon: 'success',
      duration: 1500
    })
  }

  // 样本详情扫描成功处理
  handleDetailScanSuccess = async (e) => {
    const scanResult = e.detail.result
    console.log('扫描样本详情条形码结果:', scanResult)

    try {
      Taro.showLoading({ title: '获取用户信息中' })

      // 调用API获取大健康样本订单详情（与样本采集使用相同的API）
      const { code, data } = await API.getHealthOrderById(scanResult)

      if (code !== 0 || !data) {
        Taro.hideLoading()
        Taro.showToast({
          title: '未找到该订单信息',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 构建用户信息对象
      const userInfo = {
        sjzName: data.sjzName || data.name || '未知',
        sjzPhone: data.sjzPhone || data.phone || '未知',
        sjzAge: data.sjzAge || data.age || '',
        sjzSex: data.sjzSex || data.gender || '',
        sjzIdcard: data.sjzIdcard || data.idCard || '',
        sampleCode: scanResult,
        orderData: data
      }

      // 关闭扫描窗口
      this.setState({
        showDetailScanCamera: false,
        detailUserInfo: userInfo
      })

      Taro.hideLoading()
      Taro.showToast({
        title: '用户信息获取成功',
        icon: 'success',
        duration: 1500
      })

      // 跳转到样本详情页面，传递用户信息
      setTimeout(() => {
        Taro.navigateTo({
          url: `/pages/sample/sample-detail/index?sampleCode=${scanResult}&userInfo=${encodeURIComponent(JSON.stringify(userInfo))}`
        })
      }, 1500)

    } catch (error) {
      console.error('获取用户信息失败:', error)
      Taro.hideLoading()
      Taro.showToast({
        title: '获取用户信息失败',
        icon: 'none',
        duration: 2000
      })
    }
  }

  // 绑定样本管
  bindSample = async () => {
    const { userInfo, sampleBarcode } = this.state
    
    if (!userInfo || !sampleBarcode) {
      Taro.showToast({
        title: '信息不完整，无法绑定',
        icon: 'none',
        duration: 1500
      })
      return
    }
    
    // 直接保存
    this.saveSampleNumber()
  }
  
  // 保存样本编号
  saveSampleNumber = async () => {
    const { userInfo, sampleBarcode } = this.state
    
    Taro.showLoading({ title: '保存中' })

    try {
      // 调用绑定样本编号API
      const { code, message } = await API.bindSampleNumber(
        userInfo.id,  // 订单ID
        sampleBarcode // 样本管编号
      )

      Taro.hideLoading()
      
      if (code !== 0) {
        Taro.showToast({
          title: message || '保存失败',
          icon: 'none',
          duration: 2000
        })
        return
      }
      
      Taro.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 1500
      })
      
      // 重置状态并刷新列表
      this.setState({
        collectStep: 0,
        userInfo: null,
        sampleBarcode: '',
        orderSampleNumber: '',
        isFromSampleCollection: false // 采集流程结束
      })
      this.getList()
      
    } catch (error) {
      Taro.hideLoading()
      Taro.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 1500
      })
      console.error('保存样本编号失败:', error)
    }
  }

  // 取消采集过程
  cancelCollection = () => {
    this.setState({
      collectStep: 0,
      showUserScanCamera: false,
      showSampleScanCamera: false,
      userInfo: null,
      sampleBarcode: '',
      orderSampleNumber: '' // 同时清除订单样本编号
    })
  }

  // 处理相机错误
  handleCameraError = (e) => {
    console.error('相机错误:', e)
    Taro.showToast({
      title: '相机调用失败，请检查权限',
      icon: 'none',
      duration: 2000
    })
    this.setState({
      showUserScanCamera: false,
      showSampleScanCamera: false
    })
  }

  render() {
    const { list, showUserScanCamera, showSampleScanCamera, collectStep, userInfo, sampleBarcode, showDetailScanCamera } = this.state
    
    return (
      <View className={s.page}>
        <View className={s.header} onClick={() => Taro.navigateTo({ url: '/pages/sample/check-in/index' })}>
          <Image className={s.header_icon} src={uploadPng} />
          <Text className={s.header_title}>添加样本</Text>
          <Image className={s.header_right} src={arrowPng} />
        </View>

        {/* 样本采集按钮 */}
        <View className={s.collectHeader} onClick={this.toSampleDetail}>
          <Image className={s.header_icon} src={uploadPng} />
          <Text className={s.header_title}>样本采集</Text>
          <Image className={s.header_right} src={arrowPng} />
        </View>

        {/* 样本详情展示按钮 */}
        {/* <View className={s.detailHeader} onClick={this.toSampleDetail}>
          <Image className={s.header_icon} src={barcodePng} />
          <Text className={s.header_title}>样本详情展示</Text>
          <Image className={s.header_right} src={arrowPng} />
        </View> */}

        {/* 用户扫描相机 */}
        {showUserScanCamera && (
          <View className={s.scannerOverlay}>
            <View className={s.scannerContainer}>
              <View className={s.scannerTitle}>第一步：请扫码用户端条形码录入用户信息</View>
              <Camera
                className={s.scanner}
                mode='scanCode'
                devicePosition='back'
                resolution='high'
                frameSize='large'
                flash='off'
                onError={this.handleCameraError}
                onScanCode={this.handleUserScanSuccess}
              />
              <View className={s.scannerControls}>
                <Button className={s.cancelButton} onClick={this.cancelCollection}>取消</Button>
              </View>
            </View>
          </View>
        )}

        {/* 用户信息卡片 - 仅在已扫描用户后显示 */}
        {(collectStep >= 1 && userInfo) && (
          <View className={s.userInfoCard}>
            <View className={s.cardTitle}>用户信息</View>
            <View className={s.userInfo}>
              <View className={s.infoItem}>
                <Text className={s.infoLabel}>姓名：</Text>
                <Text className={s.infoValue}>{userInfo.name}</Text>
              </View>
              
              {userInfo.questionType === '1' ? (
                // 成人版信息展示
                <View className={s.infoItem}>
                  <Text className={s.infoLabel}>证件号码：</Text>
                  <Text className={s.infoValue}>{userInfo.idCard}</Text>
                </View>
              ) : userInfo.questionType === '2' ? (
                // 儿童版信息展示 - 显示监护人信息
                <Block>
                  <View className={s.infoItem}>
                    <Text className={s.infoLabel}>监护人姓名：</Text>
                    <Text className={s.infoValue}>{userInfo.guardianName}</Text>
                  </View>
                  <View className={s.infoItem}>
                    <Text className={s.infoLabel}>监护人证件号：</Text>
                    <Text className={s.infoValue}>{userInfo.guardianIdCard}</Text>
                  </View>
                </Block>
              ) : (
                // 其他情况下默认展示证件号
                <View className={s.infoItem}>
                  <Text className={s.infoLabel}>证件号码：</Text>
                  <Text className={s.infoValue}>{userInfo.idCard}</Text>
                </View>
              )}
              
              <View className={s.infoItem}>
                <Text className={s.infoLabel}>联系电话：</Text>
                <Text className={s.infoValue}>{userInfo.phone}</Text>
              </View>
            </View>
          </View>
        )}

        {/* 第二步提示 - 仅在已扫描用户后显示 */}
        {(collectStep >= 1) && (
          <View className={s.stepPrompt}>
            <View className={s.stepTitle}>第二步：请使用手机扫描采样管上面的条形码</View>
            
            <View className={s.sampleInputContainer}>
              <Input
                className={s.sampleInput}
                placeholder="样本管编号"
                value={sampleBarcode}
                onInput={(e) => this.setState({ sampleBarcode: e.detail.value })}
              />
              <Image 
                className={s.scanIcon} 
                src={scandPng} 
                onClick={() => this.setState({ showSampleScanCamera: true })}
              />
            </View>

            {/* 仅在已扫描样本管后显示绑定按钮 */}
            {(collectStep >= 2 || sampleBarcode) && (
              <Button className={s.bindButton} onClick={this.bindSample}>绑定此样本管</Button>
            )}
          </View>
        )}

        {/* 样本管扫描相机 */}
        {showSampleScanCamera && (
          <View className={s.scannerOverlay}>
            <View className={s.scannerContainer}>
              <View className={s.scannerTitle}>请扫描采样管条形码</View>
              <Camera
                className={s.scanner}
                mode='scanCode'
                devicePosition='back'
                resolution='high'
                frameSize='large'
                flash='off'
                onError={this.handleCameraError}
                onScanCode={this.handleSampleScanSuccess}
              />
              <View className={s.scannerControls}>
                <Button className={s.cancelButton} onClick={() => this.setState({ showSampleScanCamera: false })}>取消</Button>
              </View>
            </View>
          </View>
        )}

        {/* 样本详情扫描相机 */}
        {showDetailScanCamera && (
          <View className={s.scannerOverlay}>
            <View className={s.scannerContainer}>
              <View className={s.scannerTitle}>请扫描用户端条形码录入用户信息</View>
              <Camera
                className={s.scanner}
                mode='scanCode'
                devicePosition='back'
                resolution='high'
                frameSize='large'
                flash='off'
                onError={this.handleCameraError}
                onScanCode={this.handleDetailScanSuccess}
              />
              <View className={s.scannerControls}>
                <Button className={s.cancelButton} onClick={() => this.setState({ showDetailScanCamera: false })}>取消</Button>
              </View>
            </View>
          </View>
        )}

        <View className={s.block}>
          <View className={s.block_title}>
            待上传样本（<Text className={s.block_title__inner}>{list.length}个</Text>）
          </View>
          {
            list && list.length ?
            <View className={s.list}>
              { list.map(v => (
                <View key={v.id} className={s.item} onClick={() => this.toDetail(v.id)}>
                  <View className={s.item_header}>
                    <Image className={s.item_header__icon} src={barcodePng} />
                    <Text className={s.item_header__title}>{v.sampleNumber}</Text>
                  </View>
                  <View className={s.item_footer}>
                    <Text className={s.item_header__his}>{v.submitHospitalName}</Text>
                    <Text className={s.item_header__date}>{dayjs(v.createTime).format('YYYY-MM-DD HH:mm')}</Text>
                  </View>
                </View>
              ))}
            </View> :
            <Empty text='暂无样本' />
          }
        </View>

        {
        list && list.length ?
        <View className={s.footer}>
          <Button className={[s.button]} onClick={this.submit}>确认收样</Button>
        </View> : null
        }
      </View>
    )
  }
}
