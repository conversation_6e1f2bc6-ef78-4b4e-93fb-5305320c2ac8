import Taro, { Component } from '@tarojs/taro';
import { View, Text, Image, Radio, RadioGroup, Checkbox, CheckboxGroup, Textarea, Input, Button } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';

export default class QuestionnaireDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      quesInfo: {}, // 问卷基本信息
      titleList: [], // 问题列表
      mode: 'view', // 模式：view-查看，edit-编辑
      isSubmitting: false, // 是否正在提交
      sampleId: '', // 样本ID（问卷ID）
      healthSampleOrderId: '', // 健康样本订单ID（从getHealthOrderById接口获取的id字段）
      surveyId: '', // 问卷ID
      questionnaireStatus: '0', // 问卷状态
      sampleCode: '', // 样本编码
      questionUserId: '', // 问卷用户ID
    };
  }

  componentDidMount() {
    const { id, sampleId, questionnaireStatus, sampleCode, questionUserId, healthSampleOrderId, questionnaireUrl } = this.$router.params;

    // 优先使用 id 参数，如果没有则使用 sampleId
    const surveyId = id || sampleId || '';

    // questionUserId 优先使用 questionnaireUrl，其次使用传入的 questionUserId
    const finalQuestionUserId = questionnaireUrl || questionUserId || '';

    this.setState({
      sampleId: surveyId,
      surveyId: surveyId, // 添加 surveyId 状态
      healthSampleOrderId: healthSampleOrderId || sampleCode || '', // 健康样本订单ID
      questionnaireStatus: questionnaireStatus || '0',
      sampleCode: sampleCode || '',
      questionUserId: finalQuestionUserId
    }, () => {
      // 在状态更新完成后再加载问卷
      // 根据问卷状态决定加载方式
      if (questionnaireStatus === '1') {
        // 已填写，加载已填写的问卷
        this.loadFilledQuestionnaire();
      } else {
        // 未填写，加载空白问卷
        this.loadEmptyQuestionnaire(surveyId);
      }
    });
  }

  config = {
    navigationBarTitleText: '问卷详情',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black'
  };

  // 加载空白问卷
  loadEmptyQuestionnaire = async (surveyId) => {
    try {
      Taro.showLoading({ title: '加载中...' });

      // 使用传入的问卷ID，如果没有传入则从状态中获取
      const finalSurveyId = surveyId || this.state.surveyId || this.state.sampleId;

      if (!finalSurveyId) {
        Taro.hideLoading();
        Taro.showToast({
          title: '问卷ID不能为空',
          icon: 'none'
        });
        return;
      }

      const { code, data } = await Api.getSurveyDetail({
        id: finalSurveyId
      });

      if (code === 0 && data) {
        // 处理问卷描述
        try {
          data.examDesc = (data.examDesc || '').split('\n');
        } catch (error) {
          data.examDesc = [data.examDesc || ''];
        }

        this.setState({
          quesInfo: data,
          titleList: data.titleList || [],
          mode: 'edit'
        }, () => {
          // 在状态更新完成后再初始化问卷数据
          this.initializeQuestionnaireData();
        });
      } else {
        Taro.showToast({
          title: '加载问卷失败',
          icon: 'none'
        });
      }
    } catch (error) {
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      Taro.hideLoading();
    }
  }

  // 加载已填写的问卷
  loadFilledQuestionnaire = async () => {
    try {
      Taro.showLoading({ title: '加载中...' });

      // 使用传入的问卷ID和问卷用户ID
      const surveyId = this.state.surveyId || this.state.sampleId;
      const questionUserId = this.state.questionUserId;

      // 参考用户端实现，传入更完整的参数
      const { code, data } = await Api.getSurveyAnswer({
        id: surveyId,
        questionUserId: questionUserId,
        orderId: this.state.healthSampleOrderId || this.state.sampleCode, // 添加orderId参数，使用健康样本订单ID
        examId: surveyId,
        checkSubmit: 'yes'
      });

      if (code === 0 && data) {
        // 参考用户端的数据处理逻辑
        try {
          data.examDesc = (data.examDesc || '').split('\n');
        } catch (error) {
          data.examDesc = [data.examDesc || ''];
        }

        // 处理题目列表，参考用户端的处理逻辑
        (data.titleList || []).forEach((item) => {
          // 如果是打分题，倒序
          if (item.questionsType == 3) {
            item.optionList.reverse();
          }

          // 如果是下拉单选和多选，增加是否折叠参数isHide
          if (item.questionsType == 7 || item.questionsType == 8) {
            item.isHide = true;
          }

          // 处理单选题的二级选项
          if (item.questionsType == 0) {
            (item.optionList || []).forEach(opt => {
              if (opt.frameDefaultText) {
                const extInputList = opt.frameDefaultText.split(";");
                let extData = (extInputList || []).map(ext => {
                  return {
                    label: ext,
                    value: ""
                  };
                });
                opt.extInputList = extData;
              }

              // 处理二级复选框
              if (opt.optionType === '2' && opt.secondOptionContent) {
                opt.secondOptionList = opt.secondOptionContent.split(',');
                opt.secondCheckedMap = {};
              }

              // 处理二级多值填空
              if (opt.optionType === '3' && opt.secondOptionContent) {
                opt.secondContentParts = this.parseSecondFillBlankContent(opt.secondOptionContent);
              }
            });
          }

          // 处理多选题的二级选项
          if (item.questionsType == 1) {
            (item.optionList || []).forEach(opt => {
              // 处理二级复选框
              if (opt.optionType === '2' && opt.secondOptionContent) {
                opt.secondOptionList = opt.secondOptionContent.split(',');
                opt.secondCheckedMap = {};
              }

              // 处理二级多值填空
              if (opt.optionType === '3' && opt.secondOptionContent) {
                opt.secondContentParts = this.parseSecondFillBlankContent(opt.secondOptionContent);
              }
            });
          }

          // 处理多值填空题（带占位符）
          if (item.questionsType === '18') {
            // 如果有选项列表且第一项存在
            if (item.optionList && item.optionList.length > 0) {
              const content = item.optionList[0].optionContent;

              // 计算需要的空格数量
              const valCount = (content.match(/{val}/g) || []).length;

              // 初始化空白值数组
              item.blankValues = new Array(valCount).fill('');

              // 查找此题对应的答案
              if (item.answerContent) {
                // 已经有回显的答案
                const answerValues = item.answerContent.split(',');
                item.blankValues = answerValues.concat(new Array(Math.max(0, valCount - answerValues.length)).fill(''));
              } else if (item.answerContentList && item.answerContentList.length > 0) {
                // 从answerContentList获取答案
                const answer = item.answerContentList[0];
                if (answer && answer.answerContent) {
                  const answerValues = answer.answerContent.split(',');
                  item.blankValues = answerValues.concat(new Array(Math.max(0, valCount - answerValues.length)).fill(''));
                  item.answerContent = answer.answerContent;
                }
              }
            }
          }
        });

        this.setState({
          quesInfo: data,
          titleList: data.titleList || [],
          mode: 'view'
        }, () => {
          // 在状态更新完成后再处理答案和初始化
          // 处理已填写的答案
          if (data.answerContentList && data.answerContentList.length > 0) {
            this.fillAnswersFromResponse(data.answerContentList);
          }

          this.initializeQuestionnaireData();
        });
      } else {
        Taro.showToast({
          title: '加载问卷失败',
          icon: 'none'
        });
      }
    } catch (error) {
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      Taro.hideLoading();
    }
  }

  // 初始化问卷数据
  initializeQuestionnaireData = () => {
    const { titleList } = this.state;

    titleList.forEach(question => {
      // 初始化选项状态
      if (question.optionList) {
        question.optionList.forEach(option => {
          if (!option.hasOwnProperty('checked')) {
            option.checked = false;
          }

          // 初始化填空题相关数据
          if (question.questionsType === '18') {
            this.initializeFillBlankOption(option);
          }

          // 初始化二级选项
          if (option.optionType === '1') {
            option.secondAnswerContent = option.secondAnswerContent || '';
          }

          // 初始化二级复选框
          if (option.optionType === '2' && option.secondOptionContent) {
            // 解析二级选项内容
            if (option.secondOptionContent.indexOf(',') !== -1) {
              option.secondOptionArray = option.secondOptionContent.split(',');
            } else {
              option.secondOptionArray = [option.secondOptionContent];
            }
            option.selectedSecondOptions = option.selectedSecondOptions || [];
          }

          // 初始化二级多值填空
          if (option.optionType === '3' && option.secondOptionContent) {
            option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent);
            option.secondInputCount = option.secondContentParts.filter(part => part.isInput).length;
            option.secondAnswerValues = option.secondAnswerValues || new Array(option.secondInputCount).fill('');
          }
        });
      }

      // 初始化文件上传
      if (question.fileFlg === '1') {
        question.uploadedFiles = question.uploadedFiles || [];
        question.imageCount = question.uploadedFiles.filter(f => f.type === 'image').length || 0;
      }
    });

    this.setState({ titleList });
  }

  // 解析二级多值填空内容
  parseSecondFillBlankContent = (content) => {
    if (!content) return [];

    const parts = [];
    let currentIndex = 0;
    let inputIndex = 0;

    const regex = /\{val\}/g;
    let match;
    let lastIndex = 0;

    while ((match = regex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        const textPart = content.substring(lastIndex, match.index);
        if (textPart) {
          parts.push({
            text: textPart,
            isInput: false,
            index: currentIndex++
          });
        }
      }

      parts.push({
        text: '',
        isInput: true,
        index: inputIndex,
        uniqueId: `second_input_${Date.now()}_${inputIndex}`,
        value: ''
      });

      inputIndex++;
      currentIndex++;
      lastIndex = regex.lastIndex;
    }

    if (lastIndex < content.length) {
      const textPart = content.substring(lastIndex);
      if (textPart) {
        parts.push({
          text: textPart,
          isInput: false,
          index: currentIndex++
        });
      }
    }

    return parts;
  }

  // 初始化填空题选项
  initializeFillBlankOption = (option) => {
    if (!option.contentParts) {
      option.contentParts = this.parseContentParts(option.optionContent);
    }
    if (!option.answerValues) {
      option.answerValues = new Array(option.inputCount || 0).fill('');
    }
  }

  // 初始化二级复选框
  initializeSecondaryCheckboxes = (option) => {
    if (!option.secondaryOptions) {
      try {
        option.secondaryOptions = JSON.parse(option.secondOptionContent || '[]');
      } catch (e) {
        option.secondaryOptions = [];
      }
    }
    if (!option.secondaryCheckedValues) {
      option.secondaryCheckedValues = [];
    }
  }

  // 初始化二级多值填空
  initializeSecondaryFillBlank = (option) => {
    if (!option.secondContentParts) {
      option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent);
    }
    option.secondInputCount = option.secondContentParts.filter(part => part.isInput).length;
    if (!option.secondAnswerValues) {
      option.secondAnswerValues = new Array(option.secondInputCount).fill('');
    }
  }

  // 填充已有答案
  fillAnswersFromResponse = (answerContentList) => {
    const { titleList } = this.state;

    answerContentList.forEach(answer => {
      const question = titleList.find(q => q.titleId === answer.titleId);
      if (question && question.optionList) {
        question.optionList.forEach(option => {
          if (answer.optionNum === option.optionNum) {
            option.checked = true;
            if (answer.secondAnswerContent) {
              if (option.optionType === '1') {
                // 二级文本框
                option.secondAnswerContent = answer.secondAnswerContent;
              } else if (option.optionType === '2') {
                // 二级复选框
                option.secondaryCheckedValues = answer.secondAnswerContent.split(',');
              } else if (option.optionType === '3') {
                // 二级多值填空
                option.secondAnswerValues = answer.secondAnswerContent.split(',');
                // 同步到contentParts
                if (option.secondContentParts) {
                  let valueIndex = 0;
                  option.secondContentParts.forEach(part => {
                    if (part.isInput && valueIndex < option.secondAnswerValues.length) {
                      part.value = option.secondAnswerValues[valueIndex];
                      valueIndex++;
                    }
                  });
                }
              }
            }
          }
        });
      }
    });

    this.setState({ titleList });
  }

  // 处理单选题变化
  handleRadioChange = (e) => {
    const { titleId } = e.currentTarget.dataset;
    const selectedValue = e.detail.value;

    const { titleList } = this.state;
    const question = titleList.find(q => String(q.titleId) === String(titleId));

    if (question && question.optionList) {
      question.optionList.forEach(option => {
        const isSelected = String(option.optionNum) === String(selectedValue);
        option.checked = isSelected;

        // 如果选中了有二级选项的选项，需要初始化二级选项
        if (isSelected && option.optionType === '2' && option.secondOptionContent) {
          if (!option.secondOptionArray) {
            if (option.secondOptionContent.indexOf(',') !== -1) {
              option.secondOptionArray = option.secondOptionContent.split(',');
            } else {
              option.secondOptionArray = [option.secondOptionContent];
            }
            option.selectedSecondOptions = option.selectedSecondOptions || [];
          }
        }

        if (isSelected && option.optionType === '3' && option.secondOptionContent) {
          if (!option.secondContentParts) {
            option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent);
            option.secondInputCount = option.secondContentParts.filter(part => part.isInput).length;
            option.secondAnswerValues = option.secondAnswerValues || new Array(option.secondInputCount).fill('');
          }
        }
      });
    }

    this.setState({ titleList });
  }

  // 处理多选题变化
  handleCheckboxChange = (e) => {
    const { titleId } = e.currentTarget.dataset;
    const selectedValues = e.detail.value;

    const { titleList } = this.state;
    const question = titleList.find(q => String(q.titleId) === String(titleId));

    if (question && question.optionList) {
      question.optionList.forEach(option => {
        const isSelected = selectedValues.includes(String(option.optionNum));
        option.checked = isSelected;

        // 如果选中了有二级选项的选项，需要初始化二级选项
        if (isSelected && option.optionType === '2' && option.secondOptionContent) {
          if (!option.secondOptionArray) {
            if (option.secondOptionContent.indexOf(',') !== -1) {
              option.secondOptionArray = option.secondOptionContent.split(',');
            } else {
              option.secondOptionArray = [option.secondOptionContent];
            }
            option.selectedSecondOptions = option.selectedSecondOptions || [];
          }
        }

        if (isSelected && option.optionType === '3' && option.secondOptionContent) {
          if (!option.secondContentParts) {
            option.secondContentParts = this.parseSecondFillBlankContent(option.secondOptionContent);
            option.secondInputCount = option.secondContentParts.filter(part => part.isInput).length;
            option.secondAnswerValues = option.secondAnswerValues || new Array(option.secondInputCount).fill('');
          }
        }
      });
    }

    this.setState({ titleList });
  }

  // 处理二级文本框变化
  handleSecondAnswerChange = (e) => {
    const { titleId, optionnum } = e.currentTarget.dataset;
    const value = e.detail.value;

    const { titleList } = this.state;
    const question = titleList.find(q => q.titleId === titleId);

    if (question && question.optionList) {
      const option = question.optionList.find(opt => opt.optionNum === optionnum);
      if (option) {
        option.secondAnswerContent = value;
      }
    }

    this.setState({ titleList });
  }

  // 处理二级复选框变化
  handleSecondaryCheckboxChange = (e) => {
    const { titleId, optionnum } = e.currentTarget.dataset;
    const selectedValues = e.detail.value;

    const { titleList } = this.state;
    const question = titleList.find(q => String(q.titleId) === String(titleId));

    if (question && question.optionList) {
      const option = question.optionList.find(opt => String(opt.optionNum) === String(optionnum));
      if (option) {
        option.selectedSecondOptions = selectedValues;
      }
    }

    this.setState({ titleList });
  }

  // 处理填空题输入变化
  handleFillBlankChange = (e) => {
    const { titleId, optionnum, index, uniqueId } = e.currentTarget.dataset;
    const value = e.detail.value;

    const { titleList } = this.state;
    const question = titleList.find(q => q.titleId === titleId);

    if (question && question.optionList) {
      const option = question.optionList.find(opt => opt.optionNum === optionnum);
      if (option) {
        // 更新answerValues数组
        if (!option.answerValues) {
          option.answerValues = new Array(option.inputCount || 0).fill('');
        }
        if (index !== undefined) {
          option.answerValues[index] = value;
        }

        // 同步更新contentParts中的值
        if (option.contentParts && uniqueId) {
          const part = option.contentParts.find(p => p.uniqueId === uniqueId);
          if (part) {
            part.value = value;
          }
        }
      }
    }

    this.setState({ titleList });
  }

  // 解析填空题内容部分
  parseContentParts = (content) => {
    if (!content) return [];

    const parts = [];
    let currentIndex = 0;
    let inputIndex = 0;

    // 使用正则表达式匹配输入框占位符
    const inputRegex = /\[input\]/g;
    let match;

    while ((match = inputRegex.exec(content)) !== null) {
      // 添加文本部分
      if (match.index > currentIndex) {
        parts.push({
          type: 'text',
          content: content.substring(currentIndex, match.index),
          uniqueId: `text_${parts.length}`
        });
      }

      // 添加输入框部分
      parts.push({
        type: 'input',
        index: inputIndex,
        value: '',
        uniqueId: `input_${inputIndex}`
      });

      currentIndex = match.index + match[0].length;
      inputIndex++;
    }

    // 添加剩余的文本部分
    if (currentIndex < content.length) {
      parts.push({
        type: 'text',
        content: content.substring(currentIndex),
        uniqueId: `text_${parts.length}`
      });
    }

    return parts;
  }

  // 解析二级多值填空内容
  parseSecondFillBlankContent = (content) => {
    if (!content) return [];

    const parts = [];
    let currentIndex = 0;
    let inputIndex = 0;

    const regex = /\{val\}/g;
    let match;
    let lastIndex = 0;

    while ((match = regex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        const textPart = content.substring(lastIndex, match.index);
        if (textPart) {
          parts.push({
            text: textPart,
            isInput: false,
            index: currentIndex++
          });
        }
      }

      parts.push({
        text: '',
        isInput: true,
        index: inputIndex,
        uniqueId: `second_input_${Date.now()}_${inputIndex}`,
        value: ''
      });

      inputIndex++;
      currentIndex++;
      lastIndex = regex.lastIndex;
    }

    if (lastIndex < content.length) {
      const textPart = content.substring(lastIndex);
      if (textPart) {
        parts.push({
          text: textPart,
          isInput: false,
          index: currentIndex++
        });
      }
    }

    return parts;
  }

  // 处理二级多值填空输入变化
  handleSecondFillBlankChange = (e) => {
    const { titleId, optionnum, uniqueId } = e.currentTarget.dataset;
    const value = e.detail.value;

    const { titleList } = this.state;
    const question = titleList.find(q => q.titleId === titleId);

    if (question && question.optionList) {
      const option = question.optionList.find(opt => opt.optionNum === optionnum);
      if (option && uniqueId && option.secondContentParts) {
        const partIndex = option.secondContentParts.findIndex(part => part.uniqueId === uniqueId);
        if (partIndex !== -1) {
          option.secondContentParts[partIndex].value = value;

          if (!option.secondAnswerValues) {
            option.secondAnswerValues = new Array(option.secondInputCount || 0).fill('');
          }

          const targetIndex = option.secondContentParts[partIndex].index;
          while (option.secondAnswerValues.length <= targetIndex) {
            option.secondAnswerValues.push('');
          }

          option.secondAnswerValues[targetIndex] = value || '';
        }
      }
    }

    this.setState({ titleList });
  }

  // 提交问卷
  submitQuestionnaire = async () => {
    if (this.state.isSubmitting) return;
    
    // 验证必填项
    const validation = this.validateQuestionnaire();
    if (!validation.isValid) {
      Taro.showToast({
        title: validation.message,
        icon: 'none'
      });
      return;
    }
    
    try {
      this.setState({ isSubmitting: true });
      Taro.showLoading({ title: '提交中...' });
      
      // 格式化答案数据
      const formattedAnswers = this.formatAnswersForSubmit();
      
      const params = {
        JsonToAnswer: JSON.stringify(formattedAnswers),
        healthSampleOrderId: this.state.healthSampleOrderId || this.state.sampleCode,
        examId: this.state.sampleId
      };
      
      const { code } = await Api.saveQuestion(params);
      
      if (code === 0) {
        Taro.showToast({
          title: '提交成功',
          icon: 'success'
        });

        // 通知样本详情页面刷新数据
        Taro.eventCenter.trigger('refreshSampleDetail');

        setTimeout(() => {
          Taro.navigateBack();
        }, 1500);
      } else {
        Taro.showToast({
          title: '提交失败',
          icon: 'none'
        });
      }
    } catch (error) {
      Taro.showToast({
        title: '提交失败',
        icon: 'none'
      });
    } finally {
      this.setState({ isSubmitting: false });
      Taro.hideLoading();
    }
  }

  // 验证问卷
  validateQuestionnaire = () => {
    const { titleList } = this.state;

    for (let question of titleList) {
      if (question.required === '1' || question.required === 1) {
        let hasAnswer = false;

        if (question.questionsType === '18') {
          // 填空题验证
          if (question.optionList) {
            hasAnswer = question.optionList.some(option =>
              option.answerValues && option.answerValues.some(val => val && val.trim())
            );
          }
        } else if (question.questionsType === '0') {
          // 单选题验证
          if (question.optionList && question.optionList.length > 0) {
            const checkedOptions = question.optionList.filter(option => option.checked);
            hasAnswer = checkedOptions.length > 0;

            if (hasAnswer) {
              // 检查选中选项的二级内容
              for (const option of checkedOptions) {
                if (option.optionType === '1' && (!option.secondAnswerContent || option.secondAnswerContent.trim() === '')) {
                  return {
                    isValid: false,
                    message: `请填写第${question.titleNum}题的输入框`
                  };
                } else if (option.optionType === '2') {
                  // 检查二级复选框：如果一级选中了，二级至少要选一个
                  if (!option.selectedSecondOptions || option.selectedSecondOptions.length === 0) {
                    const optionText = option.optionContent || `选项${option.optionNum}`;
                    return {
                      isValid: false,
                      message: `请选择第${question.titleNum}题 "${optionText}" 的二级选项`
                    };
                  }
                } else if (option.optionType === '3' && option.secondAnswerValues) {
                  // 检查每个填空是否都已填写
                  for (let i = 0; i < option.secondAnswerValues.length; i++) {
                    const val = option.secondAnswerValues[i];
                    if (!val || val.trim() === '') {
                      const optionText = option.optionContent || `选项${option.optionNum}`;
                      return {
                        isValid: false,
                        message: `请填写第${question.titleNum}题 "${optionText}" 第${i + 1}个空`
                      };
                    }
                  }
                }
              }
            }
          }
        } else if (question.questionsType === '1') {
          // 多选题验证
          if (question.optionList && question.optionList.length > 0) {
            const checkedOptions = question.optionList.filter(option => option.checked);
            hasAnswer = checkedOptions.length > 0;

            if (hasAnswer) {
              // 检查选中选项的二级内容
              for (const option of checkedOptions) {
                if (option.optionType === '1' && (!option.secondAnswerContent || option.secondAnswerContent.trim() === '')) {
                  return {
                    isValid: false,
                    message: `请填写第${question.titleNum}题的输入框`
                  };
                } else if (option.optionType === '2') {
                  // 检查二级多选：如果一级选中了，二级至少要选一个
                  if (!option.selectedSecondOptions || option.selectedSecondOptions.length === 0) {
                    return {
                      isValid: false,
                      message: `请选择第${question.titleNum}题 "${option.optionContent}" 的二级选项`
                    };
                  }
                } else if (option.optionType === '3' && option.secondAnswerValues) {
                  // 检查每个填空是否都已填写
                  for (let i = 0; i < option.secondAnswerValues.length; i++) {
                    const val = option.secondAnswerValues[i];
                    if (!val || val.trim() === '') {
                      const optionText = option.optionContent || `选项${option.optionNum}`;
                      return {
                        isValid: false,
                        message: `请填写第${question.titleNum}题 "${optionText}" 第${i + 1}个空`
                      };
                    }
                  }
                }
              }
            }
          }
        }

        // 注意：附件上传不是必选项，即使题目标记为必填
        // 文件上传不进行必填验证

        if (!hasAnswer) {
          return {
            isValid: false,
            message: `请回答第${question.titleNum}题`
          };
        }
      }
    }

    return { isValid: true };
  }

  // 选择文件
  chooseFile = (e) => {
    const { titleId } = e.currentTarget.dataset;
    const titleIndex = this.state.titleList.findIndex(item => item.titleId === titleId);

    let uploadedCount = 0;
    if (titleIndex !== -1 && this.state.titleList[titleIndex].uploadedFiles) {
      uploadedCount = this.state.titleList[titleIndex].uploadedFiles.filter(f => f.type === 'image').length;
    }

    Taro.showActionSheet({
      itemList: ['拍摄', '从相册选择', '上传PDF文件'],
      success: (res) => {
        const tapIndex = res.tapIndex;
        if (tapIndex === 0 || tapIndex === 1) {
          // 选择图片
          Taro.chooseImage({
            count: 9 - uploadedCount,
            sizeType: ['compressed'],
            sourceType: tapIndex === 0 ? ['camera'] : ['album'],
            success: (res) => {
              const tempFilePaths = res.tempFilePaths;
              if (!tempFilePaths || tempFilePaths.length === 0) return;

              this.uploadImages(tempFilePaths, titleIndex);
            }
          });
        } else if (tapIndex === 2) {
          // 选择PDF文件
          Taro.chooseMessageFile({
            count: 1,
            type: 'file',
            success: (res) => {
              const file = res.tempFiles[0];
              if (file && file.path) {
                this.uploadPDF(file, titleIndex);
              }
            }
          });
        }
      }
    });
  }

  // 上传图片
  uploadImages = async (tempFilePaths, titleIndex) => {
    for (let i = 0; i < tempFilePaths.length; i++) {
      try {
        Taro.showLoading({ title: '上传中...', mask: true });

        const uploadRes = await Api.uploadFile(tempFilePaths[i], 'image');

        if (uploadRes && uploadRes.code === 0 && uploadRes.data && uploadRes.data.url) {
          const { titleList } = this.state;
          if (titleIndex !== -1) {
            if (!titleList[titleIndex].uploadedFiles) {
              titleList[titleIndex].uploadedFiles = [];
            }
            titleList[titleIndex].uploadedFiles.push({
              url: uploadRes.data.url,
              type: 'image',
              fileName: '图片' + (titleList[titleIndex].uploadedFiles.length + 1)
            });

            // 更新图片数量
            const imageCount = titleList[titleIndex].uploadedFiles.filter(f => f.type === 'image').length;
            titleList[titleIndex].imageCount = imageCount;

            this.setState({ titleList });
          }
        } else {
          Taro.showToast({ title: uploadRes.msg || '上传失败', icon: 'none' });
        }
      } catch (error) {
        Taro.showToast({ title: '上传失败，请重试', icon: 'none' });
      } finally {
        Taro.hideLoading();
      }
    }

    Taro.showToast({ title: '上传成功', icon: 'success' });
  }

  // 上传PDF
  uploadPDF = async (file, titleIndex) => {
    try {
      Taro.showLoading({ title: '上传中...', mask: true });

      const uploadRes = await Api.uploadFile(file.path, 'pdf');

      if (uploadRes && uploadRes.code === 0 && uploadRes.data && uploadRes.data.url) {
        const { titleList } = this.state;
        if (titleIndex !== -1) {
          if (!titleList[titleIndex].uploadedFiles) {
            titleList[titleIndex].uploadedFiles = [];
          }
          titleList[titleIndex].uploadedFiles.push({
            url: uploadRes.data.url,
            type: 'pdf',
            fileName: file.name || 'PDF文件' + (titleList[titleIndex].uploadedFiles.length + 1)
          });

          // 更新图片数量
          const imageCount = titleList[titleIndex].uploadedFiles.filter(f => f.type === 'image').length;
          titleList[titleIndex].imageCount = imageCount;

          this.setState({ titleList });
          Taro.showToast({ title: '上传成功', icon: 'success' });
        }
      } else {
        Taro.showToast({ title: uploadRes.msg || '上传失败', icon: 'none' });
      }
    } catch (error) {
      Taro.showToast({ title: '上传失败，请重试', icon: 'none' });
    } finally {
      Taro.hideLoading();
    }
  }

  // 查看文件
  viewFile = (e) => {
    const { url } = e.currentTarget.dataset;

    if (url.toLowerCase().includes('.pdf')) {
      // PDF文件，跳转到webview查看
      Taro.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(url)}`
      });
    } else {
      // 图片文件，使用预览
      Taro.previewImage({
        current: url,
        urls: [url]
      });
    }
  }

  // 删除文件
  deleteFile = (e) => {
    const { url, titleId } = e.currentTarget.dataset;
    const { titleList } = this.state;
    const titleIndex = titleList.findIndex(item => item.titleId === titleId);

    if (titleIndex !== -1 && titleList[titleIndex].uploadedFiles) {
      titleList[titleIndex].uploadedFiles = titleList[titleIndex].uploadedFiles.filter(file => file.url !== url);

      // 更新图片数量
      const imageCount = titleList[titleIndex].uploadedFiles.filter(f => f.type === 'image').length;
      titleList[titleIndex].imageCount = imageCount;

      this.setState({ titleList });
      Taro.showToast({ title: '删除成功', icon: 'success' });
    }
  }

  // 格式化答案数据
  formatAnswersForSubmit = () => {
    const { titleList } = this.state;
    const answers = [];

    titleList.forEach(question => {
      if (question.questionsType === '18') {
        // 填空题处理
        if (question.optionList) {
          question.optionList.forEach(option => {
            if (option.answerValues && option.answerValues.some(val => val.trim())) {
              const answerContent = option.answerValues.join(',');
              const answerItem = {
                titleId: question.titleId,
                optionNum: option.optionNum,
                answerContent: answerContent,
                type: '18'
              };

              // 添加附件路径
              if (question.uploadedFiles && question.uploadedFiles.length > 0) {
                const filePaths = question.uploadedFiles.map(file => file.url).join(',');
                answerItem.filePath = filePaths;
              }

              answers.push(answerItem);
            }
          });
        }

        // 如果没有填写内容但有附件，创建一个空答案项
        if (question.uploadedFiles && question.uploadedFiles.length > 0) {
          const hasAnswers = answers.some(item => item.titleId === question.titleId);
          if (!hasAnswers) {
            const answerItem = {
              titleId: question.titleId,
              optionNum: '',
              answerContent: '',
              type: '18',
              filePath: question.uploadedFiles.map(file => file.url).join(',')
            };
            answers.push(answerItem);
          }
        }
      } else {
        // 单选题和多选题处理
        if (question.optionList) {
          question.optionList.forEach(option => {
            if (option.checked) {
              const answerItem = {
                titleId: question.titleId,
                optionNum: option.optionNum
              };

              // 处理二级答案
              if (option.optionType === '1' && option.secondAnswerContent) {
                answerItem.secondAnswerContent = option.secondAnswerContent;
              } else if (option.optionType === '2' && option.selectedSecondOptions && option.selectedSecondOptions.length > 0) {
                answerItem.secondAnswerContent = option.selectedSecondOptions.join(',');
              } else if (option.optionType === '3' && option.secondAnswerValues && option.secondAnswerValues.length > 0) {
                const hasValue = option.secondAnswerValues.some(val => val && val.trim() !== '');
                if (hasValue) {
                  answerItem.secondAnswerContent = option.secondAnswerValues.join(',');
                }
              }

              answers.push(answerItem);
            }
          });
        }
      }

      // 处理文件上传
      if (question.fileFlg === '1' && question.uploadedFiles && question.uploadedFiles.length > 0) {
        // 将多个文件路径用逗号连接
        const filePaths = question.uploadedFiles.map(file => file.url).join(',');

        // 如果有选中的选项，将附件添加到第一个选中选项
        const checkedOptions = question.optionList ? question.optionList.filter(option => option.checked) : [];
        if (checkedOptions.length > 0) {
          // 找到该题目的第一个答案项，添加文件路径
          const firstAnswerIndex = answers.findIndex(answer => answer.titleId === question.titleId);
          if (firstAnswerIndex !== -1) {
            answers[firstAnswerIndex].filePath = filePaths;
          }
        } else {
          // 如果没有选中选项但有附件，创建一个空答案项来保存附件
          answers.push({
            titleId: question.titleId,
            optionNum: '',
            filePath: filePaths
          });
        }
      }
    });

    return answers;
  }

  render() {
    const { quesInfo, titleList, mode, isSubmitting } = this.state;

    return (
      <View className={s.page}>
        {/* 问卷标题 */}
        <View className={s.questionnaireHeader}>
          <View className={s.questionnaireTitle}>{quesInfo.examTitle }</View>
          <View className={s.questionnaireDescription}>
            {Array.isArray(quesInfo.examDesc) ? (
              quesInfo.examDesc.map((desc, index) => (
                <Text key={index} className={s.descText}>{desc}</Text>
              ))
            ) : (
              <Text className={s.descText}>{quesInfo.examDesc || '请认真填写以下问卷内容'}</Text>
            )}
          </View>
        </View>

        {/* 问卷内容 */}
        <View className={s.questionnaireContent}>
          {titleList && titleList.length > 0 ? titleList.map((question, index) => (
            <View key={question.titleId} className={s.questionItem}>
              <View className={s.questionTitle}>
                {question.titleNum}、{question.questionsTitle}
                {question.questionsType === '0' && <Text className={s.questionType}>（单选）</Text>}
                {question.questionsType === '1' && <Text className={s.questionType}>（多选）</Text>}
                {question.questionsType === '18' && <Text className={s.questionType}>（填空）</Text>}
                {(question.required === '1' || question.required === 1) && <Text className={s.required}>*</Text>}
              </View>

              {/* 单选题 */}
              {question.questionsType === '0' && (
                <RadioGroup
                  className={s.radioGroup}
                  data-title-id={question.titleId}
                  data-questions-type={question.questionsType}
                  onChange={this.handleRadioChange}
                >
                  {question.optionList && question.optionList.map((option) => (
                    <View key={option.optionNum} className={`${s.radioGroupItem} ${option.checked ? s.active : ''}`}>
                      <View className={s.optionContent}>
                        <Radio
                          checked={option.checked}
                          value={option.optionNum}
                          color="#30A1A6"
                          disabled={mode === 'view'}
                        />
                        <Text className={s.optionLabel}>{option.optionContent}</Text>
                      </View>

                      {/* 二级文本框 */}
                      {option.optionType === '1' && option.checked && (
                        <View className={s.secondaryContentContainer}>
                          <Textarea
                            className={s.secondaryTextarea}
                            placeholder={option.secondOptionContent || '请在此输入详细信息...'}
                            value={option.secondAnswerContent || ''}
                            data-title-id={question.titleId}
                            data-optionnum={option.optionNum}
                            onInput={this.handleSecondAnswerChange}
                            disabled={mode === 'view'}
                            autoHeight
                          />
                        </View>
                      )}

                      {/* 二级复选框 */}
                      {option.optionType === '2' && option.checked && option.secondOptionArray && option.secondOptionArray.length > 0 && (
                        <View className={s.secondaryContentContainer}>
                          <CheckboxGroup
                            className={s.secondaryCheckboxGroup}
                            data-title-id={question.titleId}
                            data-optionnum={option.optionNum}
                            onChange={this.handleSecondaryCheckboxChange}
                          >
                            {option.secondOptionArray.map((secOption, secIndex) => (
                              <View key={secIndex} className={s.secondaryCheckboxItem}>
                                <Checkbox
                                  checked={option.selectedSecondOptions && option.selectedSecondOptions.includes(secOption)}
                                  value={secOption}
                                  color="#30A1A6"
                                  disabled={mode === 'view'}
                                />
                                <Text>{secOption}</Text>
                              </View>
                            ))}
                          </CheckboxGroup>
                        </View>
                      )}

                      {/* 二级多值填空 */}
                      {option.optionType === '3' && option.checked && option.secondContentParts && option.secondContentParts.length > 0 && (
                        <View className={s.secondaryContentContainer}>
                          <View className={s.secondaryFillBlankContainer}>
                            {option.secondContentParts.map((part) => (
                              <View key={part.uniqueId || part.index} className={s.secondaryFillBlankPart}>
                                {!part.isInput ? (
                                  <Text className={s.secondaryFillBlankText}>{part.text}</Text>
                                ) : (
                                  <Input
                                    className={s.secondaryFillBlankInput}
                                    placeholder="请填写"
                                    value={part.value || ''}
                                    data-title-id={question.titleId}
                                    data-optionnum={option.optionNum}
                                    data-unique-id={part.uniqueId}
                                    onInput={this.handleSecondFillBlankChange}
                                    disabled={mode === 'view'}
                                  />
                                )}
                              </View>
                            ))}
                          </View>
                        </View>
                      )}
                    </View>
                  ))}
                </RadioGroup>
              )}

              {/* 多选题 */}
              {question.questionsType === '1' && (
                <CheckboxGroup
                  className={s.checkboxGroup}
                  data-title-id={question.titleId}
                  data-questions-type={question.questionsType}
                  onChange={this.handleCheckboxChange}
                >
                  {question.optionList && question.optionList.map((option) => (
                    <View key={option.optionNum} className={`${s.checkboxGroupItem} ${option.checked ? s.active : ''}`}>
                      <View className={s.optionContent}>
                        <Checkbox
                          checked={option.checked}
                          value={option.optionNum}
                          color="#30A1A6"
                          disabled={mode === 'view'}
                        />
                        <Text className={s.optionLabel}>{option.optionContent}</Text>
                      </View>

                      {/* 二级文本框 */}
                      {option.optionType === '1' && option.checked && (
                        <View className={s.secondaryContentContainer}>
                          <Textarea
                            className={s.secondaryTextarea}
                            placeholder={option.secondOptionContent || '请在此输入详细信息...'}
                            value={option.secondAnswerContent || ''}
                            data-title-id={question.titleId}
                            data-optionnum={option.optionNum}
                            onInput={this.handleSecondAnswerChange}
                            disabled={mode === 'view'}
                            autoHeight
                          />
                        </View>
                      )}

                      {/* 二级复选框 */}
                      {option.optionType === '2' && option.checked && option.secondOptionArray && option.secondOptionArray.length > 0 && (
                        <View className={s.secondaryContentContainer}>
                          <CheckboxGroup
                            className={s.secondaryCheckboxGroup}
                            data-title-id={question.titleId}
                            data-optionnum={option.optionNum}
                            onChange={this.handleSecondaryCheckboxChange}
                          >
                            {option.secondOptionArray.map((secOption, secIndex) => (
                              <View key={secIndex} className={s.secondaryCheckboxItem}>
                                <Checkbox
                                  checked={option.selectedSecondOptions && option.selectedSecondOptions.includes(secOption)}
                                  value={secOption}
                                  color="#30A1A6"
                                  disabled={mode === 'view'}
                                />
                                <Text>{secOption}</Text>
                              </View>
                            ))}
                          </CheckboxGroup>
                        </View>
                      )}

                      {/* 二级多值填空 */}
                      {option.optionType === '3' && option.checked && option.secondContentParts && option.secondContentParts.length > 0 && (
                        <View className={s.secondaryContentContainer}>
                          <View className={s.secondaryFillBlankContainer}>
                            {option.secondContentParts.map((part) => (
                              <View key={part.uniqueId || part.index} className={s.secondaryFillBlankPart}>
                                {!part.isInput ? (
                                  <Text className={s.secondaryFillBlankText}>{part.text}</Text>
                                ) : (
                                  <Input
                                    className={s.secondaryFillBlankInput}
                                    placeholder="请填写"
                                    value={part.value || ''}
                                    data-title-id={question.titleId}
                                    data-optionnum={option.optionNum}
                                    data-unique-id={part.uniqueId}
                                    onInput={this.handleSecondFillBlankChange}
                                    disabled={mode === 'view'}
                                  />
                                )}
                              </View>
                            ))}
                          </View>
                        </View>
                      )}
                    </View>
                  ))}
                </CheckboxGroup>
              )}

              {/* 填空题 */}
              {question.questionsType === '18' && (
                <View className={s.fillBlankContainer}>
                  {question.optionList && question.optionList.map((option) => (
                    <View key={option.optionNum} className={s.fillBlankItem}>
                      {option.contentParts && option.contentParts.map((part) => (
                        <View key={part.uniqueId} className={s.fillBlankPart}>
                          {part.type === 'text' ? (
                            <Text className={s.fillBlankText}>{part.content}</Text>
                          ) : (
                            <Input
                              className={s.fillBlankInput}
                              placeholder="请填写"
                              value={part.value || ''}
                              data-title-id={question.titleId}
                              data-optionnum={option.optionNum}
                              data-index={part.index}
                              data-unique-id={part.uniqueId}
                              onInput={this.handleFillBlankChange}
                              disabled={mode === 'view'}
                            />
                          )}
                        </View>
                      ))}
                    </View>
                  ))}
                </View>
              )}

              {/* 文件上传 */}
              {question.fileFlg === '1' && (
                <View className={s.fileUploadSection}>
                  <View className={s.fileUploadTitle}>
                    <Text className={s.fileUploadTitleText}>附件上传</Text>
                    <Text className={s.fileUploadTitleDesc}>支持图片和PDF文件</Text>
                  </View>
                  <View className={s.fileUploadContent}>
                    {question.uploadedFiles && question.uploadedFiles.map((file, fileIndex) => (
                      <View key={fileIndex} className={s.fileItem}>
                        {file.type === 'image' ? (
                          <Image
                            className={s.img}
                            src={file.url}
                            mode="aspectFill"
                            data-url={file.url}
                            onClick={this.viewFile}
                          />
                        ) : (
                          <View
                            className={s.pdfItem}
                            data-url={file.url}
                            onClick={this.viewFile}
                          >
                            <View className={s.pdfIcon}>PDF</View>
                            <Text className={s.pdfName}>{file.fileName}</Text>
                          </View>
                        )}
                        {mode === 'edit' && (
                          <View
                            className={s.deleteBtn}
                            data-url={file.url}
                            data-title-id={question.titleId}
                            onClick={this.deleteFile}
                          >
                            ×
                          </View>
                        )}
                      </View>
                    ))}

                    {mode === 'edit' && (
                      <View
                        className={s.fileUploadBtn}
                        data-title-id={question.titleId}
                        onClick={this.chooseFile}
                      >
                        <Text className={s.uploadIconPlus}>+</Text>
                        <Text className={s.uploadBtnText}>上传文件</Text>
                      </View>
                    )}
                  </View>
                </View>
              )}
            </View>
          )) : (
            <View className={s.emptyState}>
              <Text>暂无题目数据</Text>
            </View>
          )}
        </View>

        {/* 底部按钮 */}
        {mode === 'edit' && (
          <View className={s.questionnaireBottom}>
            <Button 
              className={s.submitBtn} 
              onClick={this.submitQuestionnaire}
              disabled={isSubmitting}
            >
              {isSubmitting ? '提交中...' : '提交问卷'}
            </Button>
          </View>
        )}
      </View>
    );
  }
}
