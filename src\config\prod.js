// const projectName = process.env.npm_config_dir;

module.exports = {
  env: {
    NODE_ENV: '"production"'
  },
  defineConstants: {
    $DOMAIN: '"https://wechat.jiahuiyiyuan.com"',
    $PAY_DOMAIN: '"https://wechat.jiahuiyiyuan.com"',
    $CDN_DOMAIN: `"https://wechat.jiahuiyiyuan.com/merchant"`,
    $templateId: '"Vphwwu5qHsk4nc7GJgeFAQVRQYbnCddKOJmllljpFHw"',
    $REPLACE_IMG_DOMAIN: '"https://wechat.jiahuiyiyuan.com/merchant"',
  },
  mini: {
  },
  sass: {
    data: `$cdn: "https://static.med.gzhc365.com/miniprogram-static/fe-his-twxapp";`
  },
  h5: {
    /**
     * 如果h5端编译后体积过大，可以使用webpack-bundle-analyzer插件对打包体积进行分析。
     * 参考代码如下：
     * webpackChain (chain) {
     *   chain.plugin('analyzer')
     *     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
     * }
     */
  }
}
