.page {
  padding-bottom: 200rpx;
}

.list_item {
  margin: 16rpx 24rpx;
  padding: 32px;
  background-color: #FFF;
  border-radius: 8px;
  border-radius: 8px;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.12);
  font-size: 28px;

  &_info {
    border-bottom: 1rpx solid #F2F4F4;
    width: 100%;
    padding-bottom: 15rpx;

  }

  &_name {
    color: rgba(0, 0, 0, 0.90);
    font-size: 36px;
    font-weight: bold;
    line-height: 54px;
    padding-right: 15rpx;
    @include ellipsisLn();
  }

  &_oper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15rpx;
  }

  &_switch {
    transform-origin: 0 41%;
    transform: scale(.6);
  }

  &_checkbox {
    transform-origin: 0 30%;
    transform: scale(.7);
  }

  &_btn {
    display: flex;
    align-items: center;
    padding-left: 60rpx;
    line-height: 30rpx;
  }

  &_icon {
    padding-right: 5rpx;
  }
}

.bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  display: flex;

  &_btn {
    margin: 40rpx 40rpx 70rpx 40rpx;
    background: $color-primary;
    width: 100%;
    color: $color-white;
  }
}