.chatMsg {
  margin-bottom: 30px;

  .date {
    margin-bottom: 20px;
    text-align: center;
    font-size: 28px;
    color: #B2B2B2;
  }
  .msgContentBox {
    position: relative;
    .commLogo {
      height: 80px;
      width: 80px;
      border-radius: 50%;
    }
    .commTriangle {
      border:16px solid transparent;
      border-right-color:$color-white;
      position: relative;
      height: 0;
      top: 15px;
      &.commTriangleRight {
        border-color: transparent;
        border-left-color: #98E165;
      }
    }

    .commContent {
      background: $color-white;
      padding: 18px 20px;
      border-radius: 16px;
      overflow: hidden;
      word-break: break-all;

      .textContent {
        white-space: pre-wrap;
      }
    }

    .report {
      width: 520px;
      border-radius: 16px;
      overflow: hidden;
      .reportHeader {
        height: 100px;
        line-height: 100px;
        padding-left: 30px;
        background: $color-brand;
        color: $color-bg;
      }
      .reportContent {
        color: $color-title;
        line-height: 48px;
        padding: 30px;
        background: $color-white;
      }
    }

    .tr{
      margin-left: 20px;
    }

    .tl{
      margin-right: 20px;
    }
    &.right {
      .logo {
        margin: 0;
        margin-left: 35px;
      }
      .commContent {
        background: #98E165;
        color: #fff;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.05);
      }
      .commImageBox{
        height: 237px;
        width: 270px;
        background: $color-white;
        .commImage{
          height: 100%;
          width: 100%;
          border-radius: 16px;
        }
        &::after{
          content: '';
          border: 16px solid transparent;
          border-left-color: #98E165;
          position: absolute;
          top: 16px;
          right: -32px;
        }
      }
      .text{
        color: $color-white;
        background: #98E165;
        box-shadow: 0 2px 12px 0 rgba(176, 227, 110, .4);
        .contentBox {
          display: flex;
          .videoImage {
            width: 40px;
            height: 40px;
            margin-right: 16px;
          }
        }
        &::after{
          content: '';
          border: 16px solid transparent;
          border-left-color: #98E165;
          position: absolute;
          top: 16px;
          right: -32px;
        }
      }
      .commContentBox {
        max-width: 520px;
        font-size: 30px;
        color: $color-title;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.05);
        position: relative;
        &::after{
          content: '';
          border: 16px solid transparent;
          border-left-color: #98E165;
          position: absolute;
          top: 16px;
          right: -32px;
        }
        .expressionImage {
          width: 48px;
          height: 48px;
        }
      }
    }
    &.left {
      .logo {
        margin: 0;
        margin-left: 35px;
      }
      .left{
        margin-left: 20px;
      }
      .commContent {
        background: #fff;
        color: #000;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.05);
      }
      .commImageBox{
        height: 237px;
        width: 270px;
        background: $color-white;
        position: relative;
        .commImage{
          height: 100%;
          width: 100%;
          border-radius: 16px;
        }
        &::before{
          content: '';
          border: 16px solid transparent;
          border-right-color: #ffffff;
          position: absolute;
          top: 90px;
          left: -32px;
        }
      }
      .text{
        background: #fff;
        box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.05);
        .contentBox {
          display: flex;
          .videoImage {
            width: 40px;
            height: 40px;
            margin-right: 16px;
          }
        }
        &::before{
          content: '';
          border: 16px solid transparent;
          border-right-color: #ffffff;
          position: absolute;
          top: 16px;
          left: -32px;
        }
      }
      .commContentBox {
        max-width: 520px;
        font-size: 30px;
        color: $color-title;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.05);
        position: relative;
        &::before{
          content: '';
          border: 16px solid transparent;
          border-right-color: #ffffff;
          position: absolute;
          top: 16px;
          left: -32px;
        }
        .expressionImage {
          width: 48px;
          height: 48px;
        }
      }
    }

    .text {
      position: relative;
      padding: 20px 28px;
      background: #fff;
      border-radius: 10px;
      font-size: 30px;
      max-width: 480px;
      box-sizing: border-box;
      color: $color-title;
      white-space: normal;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
}
