.page{
  padding-bottom: 40px;
  &_header{
    background-color: #FFF;
  }
  &_body{
    margin-top: 24px;
    padding: 24px 24px 0;
    background-color: #FFF;
  }
  .field_right{
    width: 46px;
    height: 46px;
  }
  .title{
    margin-bottom: 24px;
    font-size: 28px;
    font-weight: bold;
    line-height: 42px;
    color: rgba(0, 0, 0, 0.9);
  }
  .header_titler{
    padding: 24px 24px 0;
    margin-bottom: 0;
  }
  .patient_content{
    padding: 32px;
    margin-bottom: 24px;
    border-radius: 12px;
    background: linear-gradient(90deg, rgba(48, 161, 166, 0.1) 0%, rgba(47, 132, 139, 0.1) 100%);
  }
  .name{
    margin-bottom: 16px;
    font-size: 40px;
    color: #000;
  }
  .text{
    font-size: 26px;
    color: #000;
  }
  .text_area{
    padding: 24px;
    margin-bottom: 24px;
    width: auto;
    color: #000;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow-y: auto;
  }
  .little_title{
    font-size: 32px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
  }
  .mt{
    margin-top: 24px;
  }
  .check_lable{
    margin-right: 12px;
    color: #000;
  }
  .img-box{
    padding: 0;
    .index-module__file_item{
      width: 214px;
      height: 214px;
    }
  }
  .btn{
    display: flex;
    padding: 24px;
    margin: 40px 24px 24px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex: 1 0 0;
    border-radius: 76px;
    background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
    color: #FFF;
    text-align: center;
    /* 34B */
    font-family: PingFang SC;
    font-size: 34px;
    font-style: normal;
    font-weight: 600;
    line-height: 52px; /* 152.941% */
    &::after{
      border: none;
    }
  }
  .cancel{
    margin: 24px;
    color: rgba(0, 0, 0, 0.7);
    background: rgba(0, 0, 0, 0.04);
  }
}


