import Taro, { Component } from '@tarojs/taro';
import { View, Icon, Text, Image, Button } from '@tarojs/components';
import ArrowPng from '@/resources/images/arrow.png'

import * as API from '../api'
import s from './index.module.scss'
import { STATUS_MAP } from './index'

export default class Detail extends Component {

  constructor(props) {
    super(props)
    this.state = {
      detail: {},
      showActivity: true,
      showDetail: true,
      showOrder: true,
      orderDetail: {},
    }
  }

  componentWillMount() {
    const { id } = this.$router.params;
    this.getDetail(id)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '申请详情',
    navigationBarTextStyle: 'black',
  };

  async getDetail(id) {
    const { code, data } = await API.getSignDetail({ id })
    if (code !==0) return
    this.setState({ detail: { ...data } })
    if (data.extOrderNo) {
      this.getOrderDetail(data.extOrderNo);
    }
  }

  async getOrderDetail(orderId) {
    API.getOrderDetail({ orderId }).then(res => {
      const { code, data } = res
      if (code !== 0) return
      this.setState({ orderDetail: data })
    })
  }

  async saveOrder() {
    const { detail } = this.state;
    const orderData = await API.saveOrder({
      totalFee: detail.totalFee,
      bizType: 'outside_check',
      patientName: detail.name,
      hisRecepitNo: detail.id,
    })
    if (orderData.code === 0) {
      this.getPayConfig(orderData.data.orderId);
    }
  }

  async getPayConfig(orderId) {
    const { code, data} = await API.prePayOrder({ orderId });
    if (code !== 0) return;
    const { payOrderId } = data;
    /** 获取可选的支付方式 暂时先定死微信支付 */
    const res2 = await API.choosePayMode({ payMode: 'weixin_miniapp', payOrderId });
    this.chooseWeChatWapPay(res2.data.payParameter);
  }

  async chooseWeChatWapPay(config) {
    let requestPaymentRes;
    try {
      requestPaymentRes = await Taro.requestPayment({ ...config });
    } catch (e) {
      console.log(e);
      requestPaymentRes = e;
    }
    if (requestPaymentRes.errMsg == 'requestPayment:fail cancel') {
      // 取消支付
    } else if (requestPaymentRes.errMsg == 'requestPayment:ok') {
      // 支付成功
      setTimeout(() => {
        const { detail } = this.state;
        this.getDetail(detail.id)
      }, 50);
    }
  }

  cancel = async () => {
    const { code } = await API.cancelSign({ id: this.state.detail.id })
    if (code !== 0) return
    const { detail } = this.state
    detail.status = 4
    this.setState({ detail })
    Taro.showToast({ title: '取消成功' })
  }

  signAgain = async () => {
    const { trainId, id } = this.state.detail
    Taro.redirectTo({ url: `/pages/classroom/application/index?id=${trainId}&lastSignId=${id}` })

    // const { code } = await API.reSign({ id: this.state.detail.id })
    // if (code !== 0) return
    // const { detail } = this.state
    // detail.status = 1
    // this.setState({ detail })
    // Taro.showToast({ title: '报名成功' })
  }

  getStatus = (status) => {
    return STATUS_MAP[status] || {}
  }

  toggleState = key => {
    const pre = this.state[key]
    this.setState({ [key]: !pre })
  }

  previewImage(current, urls) {
    Taro.previewImage({ current, urls})
  }

  render () {
    const { detail, showActivity, showDetail, showOrder, orderDetail } = this.state
    return (
      <View className={s.detail_page}>
        <View className='f-row f-c-center'>
          <Icon
            style={{ marginRight: '12px' }}
            size={32}
            type={this.getStatus(detail.status).icon}
            color={this.getStatus(detail.status).color}
          />
          <Text
            className={s.herder_title}
            style={{ color: this.getStatus(detail.status).color }}
          >{this.getStatus(detail.status).label}</Text>
        </View>
        <View className={s.herder_tips}>
          {
            detail.status === 1 ? '您已成功提交活动报名申请，工作人员会尽快完成审核，您可重新扫活动二维码查看报名记录及审核情况。' :
            detail.status === 2 ? '您提交的活动报名申请已通过审核，请按活动报名要求安排后续事宜。' :
            detail.status === 3 ? '您提交的活动报名审核不通过，请核对信息后重新提交，若有疑问可联系工作人员。' :
            '您提交的活动报名申请已取消，可重新申请。'
          }
        </View>

        <View className={s.page_body}>

          <View className={s.block}>
            <View className={s.title} onClick={() => this.toggleState('showActivity')}>
              <Text>申请信息</Text>
              <Image src={ArrowPng} className={[s.title_icon, !showActivity && s.collapsed]} />
            </View>
            {
              !showActivity ? null :
              <View>
                <View className={s.line}>
                  <Text className={s.line_label}>申请人</Text>
                  <Text className={s.line_value}>{detail.name}</Text>
                </View>
                <View className={s.line}>
                  <Text className={s.line_label}>活动名称</Text>
                  <Text className={s.line_value}>{detail.trainName}</Text>
                </View>
                <View className={s.line}>
                  <Text className={s.line_label}>申请时间</Text>
                  <Text className={s.line_value}>{detail.createTime}</Text>
                </View>
                { [2, 3].includes(detail.status) ?
                <View className={s.line}>
                  <Text className={s.line_label}>审核时间</Text>
                  <Text className={s.line_value}>{detail.auditTime}</Text>
                </View> : null }
                { [3].includes(detail.status) ?
                <View className={s.line}>
                  <Text className={s.line_label}>描述</Text>
                  <Text className={s.line_value}>{detail.reason}</Text>
                </View> : null }
                { [4].includes(detail.status) ?
                <View className={s.line}>
                  <Text className={s.line_label}>取消时间</Text>
                  <Text className={s.line_value}>{detail.updateTime}</Text>
                </View> : null }
              </View>


            }
          </View>

          <View>
            <View className={s.block}>
              <View className={s.title} onClick={() => this.toggleState('showDetail')}>
                <Text>申请人信息</Text>
                <Image src={ArrowPng} className={[s.title_icon, !showDetail && s.collapsed]} />
              </View>

              {
                !showDetail ? null :
                <View>
                  <View className={s.line}>
                    <Text className={s.line_label}>姓名</Text>
                    <Text className={s.line_value}>{detail.name}</Text>
                  </View>
                  {/* <View className={s.line}>
                    <Text className={s.line_label}>身份证</Text>
                    <Text className={s.line_value}>{detail.idNo}</Text>
                  </View>
                  <View className={s.line}>
                    <Text className={s.line_label}>学历</Text>
                    <Text className={s.line_value}>{detail.education}</Text>
                  </View>
                  <View className={s.line}>
                    <Text className={s.line_label}>所学专业</Text>
                    <Text className={s.line_value}>{detail.major}</Text>
                  </View>
                  <View className={s.line}>
                    <Text className={s.line_label}>职务</Text>
                    <Text className={s.line_value}>{detail.jobName}</Text>
                  </View>
                  <View className={s.line}>
                    <Text className={s.line_label}>职称</Text>
                    <Text className={s.line_value}>{detail.jobTitle}</Text>
                  </View> */}
                  <View className={s.line}>
                    <Text className={s.line_label}>所在单位</Text>
                    <Text className={s.line_value}>{detail.organization}</Text>
                  </View>
                  {/* <View className={s.line}>
                    <Text className={s.line_label}>所在科室</Text>
                    <Text className={s.line_value}>{detail.deptName}</Text>
                  </View>
                  <View className={s.line}>
                    <Text className={s.line_label}>联系电话</Text>
                    <Text className={s.line_value}>{detail.phone}</Text>
                  </View>
                  <View className={s.line}>
                    <Text className={s.line_label}>邮箱</Text>
                    <Text className={s.line_value}>{detail.contact}</Text>
                  </View> */}
                </View>
              }
            </View>

            <View className={s.block}>
              <View className={s.line3}>
                <Text className={s.line3_label}>进修申请表照片</Text>
                <View className={s.line3_value}>
                  {
                    detail.jobCertificate && detail.jobCertificate.split(',').map(url => (
                      <View key={url} className={s.line3_image}>
                        <Image
                          src={url}
                          className={s.line3_image_inner}
                          onClick={() => this.previewImage(url, detail.jobCertificate.split(','))}
                        />
                      </View>
                    ))
                  }
                </View>
              </View>
              {/* <View className={s.line3}>
                <Text className={s.line3_label}>资格证书照片</Text>
                <View className={s.line3_value}>
                  {
                    detail.credentials && detail.credentials.split(',').map(url => (
                      <View key={url} className={s.line3_image}>
                        <Image
                          src={url}
                          className={s.line3_image_inner}
                          onClick={() => this.previewImage(url, detail.credentials.split(','))}
                        />
                      </View>
                    ))
                  }
                </View>
              </View> */}
            </View>

            {/* <View className={s.line2}>
              <Text className={s.line2_label}>学习以及工作经历</Text>
              <Text className={s.line2_value}>{detail.workExp}</Text>
            </View>
            <View className={s.line2}>
              <Text className={s.line2_label}>本人专业水平</Text>
              <Text className={s.line2_value}>{detail.professionalism}</Text>
            </View> */}

            {
              !detail.docs ? null :
              <View className={s.line2}>
                <Text className={s.line2_label}>{detail.docDesc }</Text>
                <Text className={s.line2_value}>{detail.docs}</Text>
              </View>
            }
            {
              !detail.docsImg ? null :
              <View className={s.line3}>
                <Text className={s.line3_label}></Text>
                <View className={s.line3_value}>
                  {
                    detail.docsImg && detail.docsImg.split(',').map(url => (
                      <View key={url} className={s.line3_image}>
                        <Image
                          src={url}
                          className={s.line3_image_inner}
                          onClick={() => this.previewImage(url, detail.docsImg.split(','))}
                        />
                      </View>
                    ))
                  }
                </View>
              </View>
            }
          </View>
          {detail.totalFee > 0 ? <View className={s.block}>
            <View className={s.title} onClick={() => this.toggleState('showOrder')}>
              <Text>缴费信息</Text>
              <Image src={ArrowPng} className={[s.title_icon, !showOrder && s.collapsed]} />
            </View>

            {
              !showOrder ? null :
              <View>
                {detail.amount ? <View className={s.line}>
                  <Text className={s.line_label}>报名学习月数</Text>
                  <Text className={s.line_value}>{detail.amount}</Text>
                </View> : null}
                <View className={s.line}>
                  <Text className={s.line_label}>缴费金额</Text>
                  <Text className={s.line_value}>{(detail.totalFee / 100).toFixed(2)}元</Text>
                </View>
                {detail.extOrderNo ? <View>
                  <View className={s.line}>
                    <Text className={s.line_label}>缴费时间</Text>
                    <Text className={s.line_value}>{orderDetail.payedTime}</Text>
                  </View>
                  <View className={s.line}>
                    <Text className={s.line_label}>支付流水号</Text>
                    <Text className={s.line_value}>{orderDetail.agtOrdNum}</Text>
                  </View>
                </View> : <View className={s.line}>
                  <Text className={s.line_label}>缴费状态</Text>
                  <Text className={s.line_value}>未支付</Text>
                </View>}
              </View>
            }
          </View> : null}
        </View>

        <View className={s.page_footer}>
          {detail.totalFee > 0 && !detail.extOrderNo && ![3, 4].includes(detail.status)  ? <Button className={[s.btn, s.submit]} onClick={this.saveOrder}>继续支付</Button> : null}
          { detail.totalFee > 0 ? null : detail.status === 1 ? <Button className={[s.btn, s.cancel]} onClick={this.cancel}>取消申请</Button> : null }
          { [3, 4].includes(detail.status) ? <Button className={[s.btn, s.submit]} onClick={this.signAgain}>重新申请</Button> : null }
        </View>
      </View>
    )
  }
}
