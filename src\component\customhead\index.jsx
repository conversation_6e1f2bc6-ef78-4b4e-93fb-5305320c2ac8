import Taro, { Component } from '@tarojs/taro';
import { View, Image, Navigator } from '@tarojs/components';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      haveBack: false,
      statusBarHeight: 0,
      navbarHeight: 0,
      navbarBtn: {
        height: 0,
        width: 0,
        top: 0,
        bottom: 0,
        right: 0
      },
      cusnavH: 0,
    };

    this.innerStyle = {};

    this.isIpx = (Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_hrd_ipx`) || '').toString() === '1'; // 1是2否

    if (this.isIpx) {
      this.innerStyle.paddingBottom = Taro.pxTransform($IPX_BOTTOM_HEIGHT * 1);
    }
  }

  componentWillMount () {}

  componentDidMount () { this.setInfo(); }

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () {}

  setInfo = async () => {
    const sysInfoStr = (Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_hrd_info`) || '').toString();
    let sysInfo;
    if (!sysInfoStr) {
      sysInfo = await Taro.getSystemInfoSync();
    } else {
      try {
        sysInfo = JSON.parse(sysInfoStr);
      } catch (error) {
        sysInfo = await Taro.getSystemInfoSync();
      }
    }

    const posiInfoStr = (Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_posi_info`) || '').toString();
    const statusBarHeight = sysInfo.statusBarHeight;
    let headerPosi;
    if (!posiInfoStr) {
      headerPosi = await Taro.getMenuButtonBoundingClientRect();
    } else {
      try {
        headerPosi = JSON.parse(posiInfoStr);
      } catch (error) {
        headerPosi = await Taro.getMenuButtonBoundingClientRect();
      }
    }

    const btnPosi = {
      height: headerPosi.height,
      width: headerPosi.width,
      top: headerPosi.top - statusBarHeight,
      bottom: headerPosi.bottom - headerPosi.height - statusBarHeight,
      right: sysInfo.windowWidth - headerPosi.right
    }
    let haveBack;
    if (Taro.getCurrentPages().length === 1) {
      haveBack = true;
    } else {
      haveBack = false;
    }
    var cusnavH = btnPosi.height + btnPosi.top + btnPosi.bottom;
    this.setState({
      haveBack: haveBack,
      statusBarHeight,
      navbarHeight: headerPosi.bottom + btnPosi.bottom,
      navbarBtn: btnPosi,
      cusnavH
    });
  }

  goBack = () => {
    Taro.navigateBack({
      delta: 1
    });
  }

  render () {
    const {
      navbarHeight, statusBarHeight, cusnavH,
      haveBack, navbarBtn,
    } = this.state;
    const { vTitle } = this.props;
    return (
      <View className='custom_nav' style={{height: `${navbarHeight}px`}}>
        <View className='custom_nav_box' style={{height: `${navbarHeight}px`}}>
          <View className='custom_nav_bar' style={{top: `${statusBarHeight}px`, height: `${cusnavH}px`}}>
            <View
              className='custom_nav_icon'
              style={{height: `${navbarBtn.height}px`, lineHeight: `${navbarBtn.height-2}px`, top: `${navbarBtn.top}px`, left: `${navbarBtn.right}px`, borderRadius: `${navbarBtn.height/2}px`}}
            >
              {
                haveBack ?
                  <View className='icon-back' onClick={this.goBack}>
                    <View className='arrow-top' />
                    <View className='arrow-bottom' />
                  </View> : null
              }
              <View className='icon-home'>
                <Navigator className='home_a' url='/pages/home/<USER>' open-type='switchTab'>
                  <Image src={`${$CDN_DOMAIN}/head-home.png`} mode='aspectFill' className='back-home' />
                </Navigator>
              </View>
            </View>
            <View className='nav_title' style={{height: `${cusnavH}px`, lineHeight: `${cusnavH}px`}}>
              {vTitle}
            </View>
          </View>
        </View>
      </View>
    )
  }
}
