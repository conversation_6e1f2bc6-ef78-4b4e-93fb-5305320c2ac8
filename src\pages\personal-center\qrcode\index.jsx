import Taro, { Component } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';

import * as API from './api'
import s from './index.module.scss';

export default class Qrcode extends Component {

  constructor(props) {
    super(props)
    this.state = {
      account: '',
      institutionId: '',
      userName: '',
      institutionName: '',
      qrcode: '',
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props && !this.props.modalShow && nextProps.modalShow) {
      const { userName, institutionName, institutionId, account } = JSON.parse(Taro.getStorageSync('userInfo') || '{}');
      this.setState({ userName, institutionName, institutionId, account }, () => {
        this.genQrcode()
      });
    }
  }

  getQrcodeUrl = async () => {
    const { doctorId } = this.state;
    const scene = `doctorId=${doctorId || ''}`;
    const gotoUrl = 'package2/pages/scancode/home/<USER>';

    const { code, data: qrcode } = await API.getQrcode({ scene, gotoUrl, subSource: 1 });
    if (code !== 0) return;
    this.setState({ qrcode });
  }

  genQrcode = () => {
    const { from = '', isPersonal } = this.props;
    const { institutionId, account, userName } = this.state;
    const msg = encodeURIComponent(`${$DOMAIN}/${isPersonal ? 'regist' : 'scancode'}?userName=${userName}&acount=${account}&allianceId=${institutionId}&from=${from || ''}`);
    const qrcode = `${$DOMAIN}/api/qrcode/withlogo?msg=${msg}&qrtype=2`;
    this.setState({ qrcode });
  }

  saveImg = (qrcode) => {
    Taro.getSetting({
      success: () => {
        Taro.authorize({
          scope:'scope.writePhotosAlbum',
          success: () => { 
              Taro.downloadFile({  //下载文件资源到本地，客户端直接发起一个HTTP GET 请求，返回文件的本地临时路径
                url: qrcode,
                success: res => {
                  Taro.saveImageToPhotosAlbum({
                    filePath: res.tempFilePath, //返回的临时文件路径，下载后的文件会存储到一个临时文件
                    success: () => {
                      Taro.showToast({
                        title: '成功保存到相册',
                        icon: 'success'
                      })
                    }
                  })
                }
              })
          }
        })
      }
    })
  }


  render() {
    const { qrcode = '', userName = '', institutionName = '' } = this.state;
    const { modalShow, setModalShow, isInvited = '', isPersonal } = this.props
    return (
      <View>
        {
          modalShow && (
            <View className={s.modalContainer}
              onClick={() => setModalShow(false)}
            >
              <View className={s.mask}></View>
              <View className={s.modalContent} onClick={e => e.stopPropagation()}>
                {/* <View className={s.header}>
                  <View className={s.doctorName}>{userName}</View>
                  <View className={s.institutionName}>{isInvited ? isInvited : institutionName }</View>
                </View> */}

                <View className={s.body}>
                  <View className={s.doctorName}>{userName}</View>
                  <Image className={s.image} src={qrcode} onLongPress={() => this.saveImg(qrcode)}></Image>
                  {
                    isPersonal ?
                    <View  className={s.tips}>扫码注册账号</View>
                    : 
                    <View className={s.institutionName}>{isInvited ? isInvited : institutionName }</View>
                  }
                  
                </View>
                {/* <View className={s.closeBtn} onClick={() => setModalShow(false)}>关闭</View> */}
              </View>
            </View>
          )
        }
      </View>
    )
  }
}
