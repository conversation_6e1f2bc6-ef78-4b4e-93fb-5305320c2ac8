import Taro, { Component } from '@tarojs/taro'
import { View, Text, Textarea } from "@tarojs/components";
import PropTypes from 'prop-types';
import s from './index.module.scss'

export default class WxTextarea extends Component {
  constructor(props) {
    super(props)
  }

  handleInput = e => {
    this.props.onSetValue(e.detail.value)
  }

  render() {
    const { label, value, placeholder, placeholderStyle, required, border, style, labelStyle,
      maxlength } = this.props
    return (
      <View className={[s.wx_textarea, required && s.required]} style={style}>
        <View className={[s.wx_textarea__body, 'f-col', 'f-c-stretch']}>
          { label ? <Text className={s.wx_textarea__label} style={{ ...labelStyle }}>{ label }</Text> : null }
          <Textarea
            className={[s.wx_textarea__input, border && s.border]}
            value={value}
            placeholder={placeholder}
            placeholder-style={placeholderStyle}
            maxlength={maxlength}
            onInput={this.handleInput}
          />
        </View>
      </View>
    )
  }
}

WxTextarea.propTypes = {
  onSetValue: PropTypes.func,
  label: PropTypes.string,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  placeholderStyle: PropTypes.object,
  required: PropTypes.bool,
  border: PropTypes.bool,
  labelStyle: PropTypes.object,
  style: PropTypes.object,
  maxlength: PropTypes.number,
};
WxTextarea.defaultProps = {
  placeholder: '请输入',
  placeholderStyle: {
    fontSize: '14px',
  },
  required: false,
  border: true,
  labelStyle: { color: '#000' },
  style: {},
  maxlength: 3000,
};
