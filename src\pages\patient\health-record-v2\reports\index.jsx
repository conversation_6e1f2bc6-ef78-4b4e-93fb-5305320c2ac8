import Taro, { Component } from '@tarojs/taro'
import { View, Image } from '@tarojs/components'
import * as API from '../api'
import s from './index.module.scss'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      patientPid: '',
      outHisReports: [],
    }
  }

  componentWillMount() {
    const { patientPid } = this.$router.params
    this.setState({ patientPid }, this.getDetail)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '外院报告',
    navigationBarTextStyle: 'black'
  };

  async getDetail() {
    const { code, data } = await API.getFamilyInfo({ patients: this.state.patientPid })
    if (code !== 0) return
    let { outHisReports = {} } = data || {}
    let reports = []
    try {
      outHisReports = JSON.parse(outHisReports)
      reports = JSON.parse(outHisReports.reports)
      reports = (reports || []).map(v => ({
        updateTime: v.updateTime,
        reportsPath: v.reportsPath ? JSON.parse(v.reportsPath).split(';') : [],
      }))
    } catch (error) {
      console.error(error)
    }
    this.setState({ outHisReports: reports })
  }

  previewImage(current, urls) {
    Taro.previewImage({ current, urls })
  }

  render() {
    const { outHisReports } = this.state
    return (
      <View className={s.page}>
        {
          outHisReports.map(v => (
            <View key={v.updateTime} className={s.page_card}>
              <View className={s.title}>{v.updateTime}({v.reportsPath.length})张</View>
              <View className={s.content}>
                {
                  v.reportsPath.map(url => (
                    <Image key={url} className={s.pic_inner} src={url} onClick={() => this.previewImage(url, v.reportsPath)} />
                  ))
                }
              </View>
            </View>
          ))
        }
      </View>
    )
  }
}
