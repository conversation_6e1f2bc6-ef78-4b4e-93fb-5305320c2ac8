{"name": "nicai", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": false, "css": "sass"}, "scripts": {"build:weapp": "cross-env RUN_ENV=prod taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "cross-env RUN_ENV=prod taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "cross-env RUN_ENV=dev taro build --type alipay --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "cross-env RUN_ENV=dev npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev": "cross-env RUN_ENV=dev taro build --type weapp --watch", "qa": "cross-env RUN_ENV=qa taro build --type weapp --watch", "prod": "cross-env RUN_ENV=prod taro build --type weapp --watch", "qa:alipay": "cross-env RUN_ENV=qa taro build --type alipay --watch", "prod:alipay": "cross-env RUN_ENV=prod npm run build:alipay -- --watch", "build:dev": "cross-env RUN_ENV=dev taro build --type weapp", "build:qa": "cross-env RUN_ENV=qa taro build --type weapp", "build:prod": "cross-env RUN_ENV=prod taro build --type weapp"}, "author": "", "license": "MIT", "dependencies": {"@tarojs/components": "2.2.11", "@tarojs/components-qa": "2.2.11", "@tarojs/router": "2.2.11", "@tarojs/taro": "2.2.11", "@tarojs/taro-alipay": "2.2.11", "@tarojs/taro-h5": "2.2.11", "@tarojs/taro-qq": "2.2.11", "@tarojs/taro-quickapp": "2.2.11", "@tarojs/taro-rn": "2.2.11", "@tarojs/taro-swan": "2.2.11", "@tarojs/taro-tt": "2.2.11", "@tarojs/taro-weapp": "2.2.11", "babel-runtime": "6.26.0", "dayjs": "^1.11.10", "mini-html-parser2": "0.2.0", "nerv-devtools": "1.5.5", "nervjs": "1.5.5", "regenerator-runtime": "0.11.1", "rich-text-parser": "1.0.2", "taro-navigationbar": "2.1.1", "taro-ui": "2.2.4"}, "devDependencies": {"@haici/request-filter": "1.1.5-beta.1", "@haici/utils": "1.1.7-beta.1", "@tarojs/cli": "2.2.11", "@tarojs/mini-runner": "2.2.11", "@tarojs/plugin-sass": "2.2.11", "@tarojs/plugin-terser": "2.2.11", "@tarojs/webpack-runner": "2.2.11", "@types/react": "16.4.6", "@types/webpack-env": "1.13.6", "babel-eslint": "8.2.3", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-jsx-stylesheet": "0.6.5", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-plugin-transform-runtime": "6.23.0", "babel-preset-env": "1.6.1", "cross-env": "6.0.3", "eslint": "5.16.0", "eslint-config-taro": "2.0.4", "eslint-plugin-import": "2.12.0", "eslint-plugin-react": "7.8.2", "eslint-plugin-react-hooks": "1.6.1", "eslint-plugin-taro": "2.2.11", "prettier": "2.0.5", "stylelint": "9.3.0", "stylelint-config-taro-rn": "2.2.11", "stylelint-taro-rn": "2.2.11"}}