import Taro, {Component} from '@tarojs/taro';
import { View, Button, Image } from '@tarojs/components';
import arrowRightPng from '@/resources/images/arrow-right.png'
import * as API from './api'
import s from './index.module.scss';


export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      patientPid: '',
      pid: '',

      career: '',
      education: '',
      nation: '',

      familyHistory: {},
      visitRecord: {},
      auxiliaryExamination: {},
      diseaseDescription: {},
      outHisReports: {},
    };
  }

  componentWillMount() {
    const { patientPid, pid } = this.$router.params
    this.setState({ patientPid, pid }, this.getDetail)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '健康档案',
    navigationBarTextStyle: 'black'
  };


  async getDetail() {
    const { patientPid } = this.state
    const { code: code1, data: data1 } = await API.getBasicInfo({ patients: patientPid })
    if (code1 !== 0) return
    const { career = '', education = '', nation = '', } = data1 || {}
    this.setState({ career, education, nation })
    const { code: code2, data: data2 } = await API.getFamilyInfo({ patients: patientPid })
    if (code2 !== 0) return

    let { familyHistory, visitRecord, auxiliaryExamination, diseaseDescription, outHisReports } = data2 || {}
    try {
      familyHistory = JSON.parse(familyHistory)
      visitRecord = JSON.parse(visitRecord)
      auxiliaryExamination = JSON.parse(auxiliaryExamination)
      diseaseDescription = JSON.parse(diseaseDescription)
      outHisReports = JSON.parse(outHisReports)
    } catch (error) {
      console.error(error)
    }
    this.setState({
      familyHistory: familyHistory || {},
      visitRecord: visitRecord || {},
      auxiliaryExamination: auxiliaryExamination || {},
      diseaseDescription: diseaseDescription|| {},
      outHisReports: outHisReports || {},
    })
  }

  toDetail(module, key = '') {
    const data = this.state[key]
    if (key && (!data || !data.info)) {
      return Taro.showToast({ title: '暂无数据', icon: 'none' })
    }
    Taro.navigateTo({ url: `/pages/patient/health-record-v2/${module}/index?patientPid=${this.state.patientPid}` })
  }

  toReports() {
    const outHisReports = this.state.outHisReports
    if (!outHisReports || !outHisReports.reports || !outHisReports.reports.length) {
      return Taro.showToast({ title: '暂无数据', icon: 'none' })
    }
    Taro.navigateTo({ url: `/pages/patient/health-record-v2/reports/index?patientPid=${this.state.patientPid}` })
  }

  getReports = (outHisReports) => {
    try {
      const reports = JSON.parse(outHisReports.reports)
      return reports.reduce((res, item) => (res + item.reportsPath.split(';').length), 0)
    } catch (error) {
      return 0
    }
  }

  render() {
    const { patientPid, pid, career, education, nation,
      familyHistory, visitRecord, auxiliaryExamination,
      diseaseDescription, outHisReports } = this.state;

    const uploadCount = this.getReports(outHisReports)

    return (
      <View className={s.page}>
        <View className={s.page_body}>

          <View className={[s.block, 'f-center']} onClick={() => this.toDetail('base-info')}>
            <View className={[s.block__left, 'f-1']}>
              <View className={s.block__left_title}>您的基本信息</View>
              <View className={s.block__left_line}>{career || '--'}/{education || '--'}/{nation || '--'}</View>
            </View>
            <Image className={s.block__right} src={arrowRightPng}></Image>
          </View>

          <View className={[s.block, 'f-center']} onClick={() => this.toDetail('family-history', 'familyHistory')}>
            <View className={[s.block__left, 'f-1']}>
              <View className={s.block__left_title}>家族病史</View>
              <View className={s.block__left_line}>{familyHistory.info || '--'}</View>
            </View>
            <Image className={s.block__right} src={arrowRightPng}></Image>
          </View>

          <View className={[s.block, 'f-center']} onClick={() => this.toDetail('visit-records', 'visitRecord')}>
            <View className={[s.block__left, 'f-1']}>
              <View className={s.block__left_title}>就诊记录</View>
              <View className={s.block__left_line}>{visitRecord.info || '--'}</View>
            </View>
            <Image className={s.block__right} src={arrowRightPng}></Image>
          </View>

          <View className={[s.block, 'f-center']} onClick={() => this.toDetail('examination', 'auxiliaryExamination')}>
            <View className={[s.block__left, 'f-1']}>
              <View className={s.block__left_title}>辅助检查</View>
              <View className={s.block__left_line}>{auxiliaryExamination.info || '--'}</View>
            </View>
            <Image className={s.block__right} src={arrowRightPng}></Image>
          </View>

          <View className={[s.block, 'f-center']} onClick={() => this.toDetail('disease-desc', 'diseaseDescription')}>
            <View className={[s.block__left, 'f-1']}>
              <View className={s.block__left_title}>病情描述</View>
              <View className={s.block__left_line}>{diseaseDescription.info || '--'}</View>
            </View>
            <Image className={s.block__right} src={arrowRightPng}></Image>
          </View>

          <View className={[s.block, 'f-center']} onClick={this.toReports}>
            <View className={[s.block__left, 'f-1']}>
              <View className={s.block__left_title}>外院报告上传</View>
              <View className={s.block__left_line}>已经上传{ uploadCount }张</View>
            </View>
            <Image className={s.block__right} src={arrowRightPng}></Image>
          </View>

        </View>
        <View className={s.page_footer}>
          {/* <Button
            className={s.btn}
            onClick={() => Taro.navigateTo({ url: `/pages/patient/bill/makebill/index?patientPid=${patientPid}` })}
          >开单缴费</Button> */}
          <Button
            className={s.btn}
            onClick={() => Taro.navigateTo({ url: `/pages/patient/health-record/visiting-record/consultation-application/index?patientPid=${patientPid}&pid=${pid}`})}
          >申请会诊</Button>
        </View>
      </View>
    );
  }
}
