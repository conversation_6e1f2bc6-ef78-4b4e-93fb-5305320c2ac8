import { post } from '@/utils/request';

export const authorize = (param) => post('/api/doctor/login/authorization', param, false);

export const login = (param) => post('/api/doctor/login', param);

export const sendMsgCode = (param) => post('/api/send/msg-code', param);

export const loginByPhone = (param) => post('/api/login/authCode', param);

export const changePwd = (param) => post('/api/doctor/changepassword', param);

export const changeNewPassWord = (param) => post('/api/doctor/changeNewPassWord', param);

export const checkMediSignPwd = (param) => post('/api/doctor/prescription/setSignSignature', param);
/**
 * 获取护士信息
 */
export const getNurse = (param) => post('/api/doctor/netNurse/getIndexNetNurse', param);

export const getUserInfo = (param) => post('/api/personal/getUserInfo', param);
