import Taro, { Component } from '@tarojs/taro';
import { View, CheckboxGroup, Checkbox, Textarea, Image, Button, Text } from '@tarojs/components';

import { openFile } from '../../../../utils/utils';
import s from './index.module.scss'
import * as API from './api'


export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      moreArray: [
        {
          value: 'FSH',
          name: 'FSH',
          checked: false
        },
        {
          value: 'LH',
          name: 'LH',
          checked: false
        },
        {
          value: '睾酮',
          name: '睾酮',
          checked: false
        },
        {
          value: 'E2',
          name: 'E2',
          checked: false
        },
        {
          value: 'PRL',
          name: 'PRL',
          checked: false
        },
        {
          value: 'INHB',
          name: 'INHB',
          checked: false
        }
      ],
      lessArray: [
        {
          value: 'FSH',
          name: 'FSH',
          checked: false
        },
        {
          value: 'LH',
          name: 'LH',
          checked: false
        },
        {
          value: '睾酮',
          name: '睾酮',
          checked: false
        },
        {
          value: 'E2',
          name: 'E2',
          checked: false
        },
        {
          value: 'PRL',
          name: 'PRL',
          checked: false
        },
        {
          value: 'INHB',
          name: 'INHB',
          checked: false
        }
      ],
      sampleNumber: '',
      tempFilePaths: [],
      patientName: '', 
      idNumber: '', 
      mobile: '',
      sampleDetail: {}
    }
  }

  componentWillMount() {
    const { patientName, idNumber, mobile, sampleNumber } = this.$router.params;
    this.setState({patientName, idNumber, mobile})
    if(sampleNumber){
      this.getPerfectInfo(sampleNumber)
    }
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '样本信息',
    navigationBarTextStyle: 'black',
  };
  
  getPerfectInfo = async(sampleNumber) => {
    const { code, data } = await API.getBySamplenumber({id: sampleNumber})
    if (code !== 0) return
   
    const informObj = JSON.parse(data.informedConsentForm);
    const field1 = informObj.field1;
    const field2 = informObj.field2.split(',');
    const field3 = informObj.field3.split(',');
    this.setState({ 
      sampleDetail: data,
      sampleNumber: data.sampleNumber,
      expressnumber: data.expressnumber,
      field1,
      tempFilePaths: data.files ? data.files.split(',') : []
    })
    field2.forEach(v => {
      this.state.moreArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
    field3.forEach(v => {
      this.state.lessArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
  }

  handleSetState = (key, val) => {
    this.setState({ [key]: val })
  }

  scanCode = async() => {
    const res = await Taro.scanCode({ scanType: ['barCode'], onlyFromCamera: true,  })
    Taro.showToast({ title: '扫描成功', icon: 'none' })
    this.setState({
      sampleNumber: res.result,
    }, this.handleSearch)
  }


  cancel = () => {
    Taro.navigateBack({
      delta: 1
    });
  }

  goReport = () => {
    openFile(this.state.sampleDetail.reportPath)
  }

  render() {
    const { moreArray, lessArray, sampleNumber, expressnumber, field1, tempFilePaths, patientName, idNumber, mobile, sampleDetail  } = this.state
    return (
      <View className={s.page}>
        <View className={s.page_header}>
          <View className={[s.title, s.header_titler]}>样本信息</View>
          <View className={s.list}>
            <Text className={s.list_title}>样本编号</Text>
            <Text className={s.list_value}>{sampleNumber}</Text>
          </View>
          <View className={s.list}>
            <Text className={s.list_title}>样本快递编号</Text>
            <Text className={s.list_value}>{expressnumber}</Text>
          </View>
        </View>
        <View className={s.page_body}>
          <View className={s.title}>受检者信息</View>
          <View className={s.list}>
            <Text className={s.list_title}>受检者姓名</Text>
            <Text className={s.list_value}>{patientName}</Text>
          </View>
          <View className={s.list}>
            <Text className={s.list_title}>受检者身份证</Text>
            <Text className={s.list_value}>{idNumber}</Text>
          </View>
          <View className={s.list}>
            <Text className={s.list_title}>受检者手机号</Text>
            <Text className={s.list_value}>{mobile}</Text>
          </View>
          <View className={[s.title, s.son_title]}>病史（请根据实际情况填写）</View>
          <Textarea
            className={s.text_area}
            value={field1}
            disabled
            placeholder='请输入病史'
          />
          <View className={[s.title, s.son_title]}>对异常参数打勾</View>
          <View className={s.little_title}>检测结果超过正常值的有：</View>
          <CheckboxGroup className='s.project_block__list' onChange={e => this.handleSetState('field2', e.detail.value)}>
            {
              moreArray.map(item => (
              <Checkbox
                disabled
                key={item.value}
                className={['f-1',s.check_lable]}
                value={item.value}
                checked={item.checked}
                color='#30A1A6'
              >{ item.name }</Checkbox>
              ))
            }
          </CheckboxGroup>
          <View className={[s.little_title, s.mt]}>检测结果低于正常值的有：</View>
          <CheckboxGroup className='s.project_block__list' onChange={e => {this.handleSetState('field3', e.detail.value); console.log(e, '=======250')}}>
            {
              lessArray.map(item => (
                <Checkbox
                  disabled
                  key={item.value}
                  className={['f-1',s.check_lable]}
                  value={item.value}
                  checked={item.checked}
                  color='#30A1A6'
                >{ item.name }</Checkbox>
              ))
            }
          </CheckboxGroup>
          <View className={[s.title, s.son_title, s.mt]}>知情同意书照片</View>
          {
            tempFilePaths.map(v => (
              <Image className={s.img} key={v} src={v} />
            ))
          }
          
        </View>
        
        {
          sampleDetail.reportPath && 
          <Button
            className={s.btn}
            onClick={this.goReport}
          >查看报告</Button>
        }
        
        
      </View>
      
    )
  }
}
