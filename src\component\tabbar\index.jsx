import Taro, { Component } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {};

    this.innerStyle = {};

    this.isIpx = (Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_hrd_ipx`) || '').toString() === '1'; // 1是2否

    if (this.isIpx) {
      this.innerStyle.bottom = Taro.pxTransform($IPX_BOTTOM_HEIGHT * 1);
    }

    this.tabList = [
      {
        key: 'home',
        url: '/pages/home/<USER>',
        icon: 'tab-home-off.png',
        activeIcon: 'tab-home-on.png',
        title: '首页',
      },
      {
        key: 'microsite',
        url: '/pages/microsite/home/<USER>',
        icon: 'tab-microsite-off.png',
        activeIcon: 'tab-microsite-on.png',
        title: '医院信息',
      },
      {
        key: 'usercenter',
        url: '/pages/usercenter/home/<USER>',
        icon: 'tab-usercenter-off.png',
        activeIcon: 'tab-usercenter-on.png',
        title: '个人中心',
      },
    ];
  }

  componentWillMount () { }

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  switchTab = (item = {}) => {
    const { currentKey = '' } = this.props;
    if (currentKey === item.key) {
      return false;
    }
    Taro.reLaunch({
      url: item.url
    });
  }

  render () {
    const { currentKey } = this.props;

    return (
      <View className='wgt-navtab-baffle'>
        <View className='wgt-navtab' style={this.innerStyle}>
          {
            this.tabList.map((item) => {
              return (
                <View className='wgt-navtab-item' onClick={() => this.switchTab(item)} key={item.key}>
                  <View className='wgt-navtab-item-icon'>
                    <Image mode='widthFix' src={`${$CDN_DOMAIN}/${currentKey === item.key ? item.activeIcon : item.icon}`} />
                  </View>
                  <View className={`wgt-navtab-item-name ${currentKey === item.key ? 'active' : ''}`}>{item.title}</View>
                </View>
              );
            })
          }
        </View>
      </View>
    )
  }
}
