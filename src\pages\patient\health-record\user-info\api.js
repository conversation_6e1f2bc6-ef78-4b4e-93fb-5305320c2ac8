import { post } from '@/utils/request';

//获取健康档案
export const queryHealthRecord = (param) => post('/api/healthRecord/queryHealthRecord', param);
// 查询病史
export const queryDiseaseList = (param) => post('/api/healthRecord/queryDiseaseList', param);
// 签约信息查询
export const getPatientSubscrip = (param) => post('/api/customize/getPatientSubscrip?_route=h242&k', param);

export const hasReferral = (param) => post('/api/consultation/hasReferral', param);

