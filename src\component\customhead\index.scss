.custom_nav {
  width: 100%;
  position: relative;
  z-index: 99999;
  @include linearBg;
}

.custom_nav_box {
  position: fixed;
  width: 100%;
  @include linearBg;
  z-index: 99999;

  .nav_title {
    font-size: 28px;
    color: #fff;
    text-align: center;
    position: absolute;
    max-width: 360px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 1;
  }
  .custom_nav_icon {
    position:absolute;
    z-index: 2;
    display: inline-block;
    border-radius: 50%;
    vertical-align: top;
    font-size:0;
    box-sizing: border-box;
  }

  .back-pre,.back-home {
    width: 35px;
    height: 35px;
    vertical-align: middle;
  }
}

.custom_nav_bar {
  position: relative;
  z-index: 9;
}

.icon-back {
  display: inline-block;
  width: 20px;
  padding-left: 20px;
  vertical-align: top;
  /* margin-top: 12px;
  vertical-align: top; */
  padding-right: 30px;
  height: 100%;
  position: relative;

  .arrow-top,.arrow-bottom{
    position: absolute;
    left: 20px;
    width: 20px;
    top: 50%;
    transform-origin: 0 0;
    transform: rotate(45deg) translateY(-50%);
    border-top: 1PX solid #fff;
    z-index: 100;
  }
  .arrow-top{
    transform: rotate(-45deg) translateY(-50%);
  }
}

.icon-home {
  display: inline-block;
  width: 80px;
  text-align: center;
  vertical-align: top;
  height: 100%;
}
