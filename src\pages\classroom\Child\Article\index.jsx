import Taro, { Component } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import { ScrollTabs } from '@/component/Tabs'
import Empty from '@/component/empty'
import s from './index.module.scss'
import * as API from '../../api'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      activeType: '',
      articleTypeList: [],
      articleList: [],
    }
  }

  componentWillMount() {
    this.getCategoryList()
  }

  handleSwitchType = (e) => {
    if (e === this.activeType) return
    this.setState({ activeType: e }, this.getArticleList)
  }

  getCategoryList = async () => {
    if (this.state.articleTypeList.length) return
    const { code, data } = await API.getArticleTypeList({})
    if (code !== 0) return
    const articleTypeList = (data || []).map(v => ({ value: v.typeId, label: v.typeName }))
    this.setState({ articleTypeList })
    if (!articleTypeList.length) return
    const activeType = articleTypeList[0].value
    this.setState({ activeType }, this.getArticleList)
  }

  getArticleList = async () => {
    const { code, data} = await API.getArticleList({ typeId: this.state.activeType })
    if (code !== 0) return
    const articleList = data && data.recordList ? data.recordList || [] : []
    this.setState({ articleList })
  }

  toDetail(id) {
    Taro.navigateTo({ url: `/pages/classroom/Child/Article/detail?id=${id}` })
  }

  render() {
    const { activeType, articleTypeList, articleList } = this.state
    return (
      <View className={s.block}>
        <View className={s.block_header}>
          <ScrollTabs
            list={articleTypeList}
            value={activeType}
            setValue={this.handleSwitchType}
          />
        </View>

        <View className={s.block_body}>
          {
            articleList.map(item => (
              <View
                key={item.articleId}
                className={[s.block_item, 'f-row', 'f-c-stretch', 'f-m-between']}
                onClick={() => this.toDetail(item.articleId)}
              >
                <View className={['f-1', 'f-col', 'f-m-between']}>
                  <Text className={s.block_item_title}>{item.title}</Text>
                  <Text className={s.block_item_time}>{item.publishTime}</Text>
                </View>
                <Image className={s.block_item_img} src={item.picUrl} mode='aspectFill' />
              </View>
            ))
          }
          {
            (!articleList || !articleList.length) ? <Empty text='暂无数据' /> : null
          }
        </View>

      </View>
    )
  }
}
