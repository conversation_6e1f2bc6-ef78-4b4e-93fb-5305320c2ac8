.nav-top {
  position: relative;
  .nav-box{
    display: flex;
    position: fixed;
    font-size: 30px;
    align-items: flex-end;
    width: 100%;
    z-index: 99999;
    .capsule{
      height: 56px;
      border: 1px solid #ffffff;
      border-radius: 100px;
      margin: 16px;
      display:flex;
      align-items:center;
      width: 180px;
      .nav-back{
        font-size: 48px;
        width: 84px;
        height: 56px;
        position: relative;
        .back-confont{
          &:after {
            content: ' ';
            position: absolute;
            left: 34px;
            top: 50%;
            width: 20px;
            height: 20px;
            border-right: 5px solid #ffffff;
            border-bottom: 5px solid #ffffff;
            transform: translate(-8px, -50%) rotate(-225deg);
          }
        }
      }
      .back-home{
        flex: 1;
        height: 56px;
        position: relative;
        display: flex;
        align-items: center;
        image{
          margin-left: 22px;
          width: 48px;
          height: 44px;
        }
        &:before {
          content: ' ';
          height: 40px;
          width: 1px;
          background-color: #ffffff;
          position: absolute;
          left: 0;
          top: 10px;
          bottom: 10px;
        }
      }
    }
    .nav-title{
      flex:1;
      text-align: center;
      padding-bottom: 20px;
    }
  }
}