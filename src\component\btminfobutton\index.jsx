import Taro, { Component } from '@tarojs/taro';
import { View, Button, Form } from '@tarojs/components';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.innerStyle = {};

    this.isIpx = (Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_hrd_ipx`) || '').toString() === '1'; // 1是2否

    if (this.isIpx) {
      this.innerStyle.bottom = Taro.pxTransform($IPX_BOTTOM_HEIGHT * 1);
    }
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  render () {
    const {
      text,
      onSubmit,
      disabled = false,
      children
    } = this.props;

    return (
      <View className='wgt-button_btminfo'>
        <View className='button-box'>
          <View className='pay-body'>
            {children}
          </View>
          <View className='pay-foot'>
            <View className={`pay-btn-box ${disabled ? 'disabled' : ''}`}>
              <View className='pay-btn'>{text}</View>
              <Form onSubmit={onSubmit} reportSubmit>
                <Button
                  formType='submit'
                  className='pay-btn-real'
                  disabled={disabled}
                ></Button>
              </Form>
            </View>
          </View>
        </View>
      </View>
    )
  }
}
