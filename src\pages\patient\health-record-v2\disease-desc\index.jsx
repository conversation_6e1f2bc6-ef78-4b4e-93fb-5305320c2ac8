import Taro, { Component } from '@tarojs/taro'
import { View } from '@tarojs/components'
import * as API from '../api'
import s from './index.module.scss'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      patientPid: '',
      diseaseDescription: [],
    }
  }

  componentWillMount() {
    const { patientPid } = this.$router.params
    this.setState({ patientPid }, this.getDetail)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '病情描述',
    navigationBarTextStyle: 'black'
  };

  async getDetail() {
    const { code, data } = await API.getFamilyInfo({ patients: this.state.patientPid })
    if (code !== 0) return
    let { diseaseDescription } = data || {}
    try {
      diseaseDescription = JSON.parse(diseaseDescription)
    } catch (error) {
      console.error(error)
    }
    this.setState({
      diseaseDescription: [diseaseDescription || {}]
    })
  }

  render() {
    const { diseaseDescription } = this.state
    return (
      <View className={s.page}>
        {
          diseaseDescription.map(v => (
            <View key={v.info} className={s.page_card}>
              <View className={s.title}>{v.updateTime} {v.updateName}保存</View>
              <View className={s.content}>{v.info}</View>
            </View>
          ))
        }
      </View>
    )
  }
}
