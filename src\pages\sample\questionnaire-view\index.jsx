import Taro, { Component } from '@tarojs/taro';
import { View, Text, ScrollView, Image } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';

export default class QuestionnaireView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      quesInfo: {}, // 问卷基本信息
      titleList: [], // 问题列表
      scrollHeight: 0, // 滚动区域高度
      hideHeader: false, // 是否隐藏头部
      id: '', // 问卷ID
      orderId: '', // 订单ID
      questionUserId: '', // 问卷用户ID
      record: '1', // 查看模式，固定为1
      loading: true,
      error: false
    };
  }

  config = {
    navigationBarTitleText: '问卷详情',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black'
  };

  componentDidMount() {
    const { id, orderId, questionUserId, questionnaireUrl } = this.$router.params;

    // questionUserId 优先使用 questionnaireUrl，其次使用传入的 questionUserId
    const finalQuestionUserId = questionnaireUrl || questionUserId || '';
    const finalOrderId = orderId || '';

    // 设置页面参数
    this.setState({
      id: id || '',
      orderId: finalOrderId,
      questionUserId: finalQuestionUserId,
      record: '1' // 查看模式
    });

    // 获取系统信息设置滚动高度
    Taro.getSystemInfo({
      success: (res) => {
        this.setState({
          scrollHeight: res.windowHeight
        });
      }
    });

    // 加载问卷数据
    if (id) {
      this.loadQuestionnaireData(id, finalQuestionUserId, finalOrderId);
    } else {
      this.setState({
        error: true,
        loading: false
      });
    }
  }

  // 加载问卷数据
  loadQuestionnaireData = async (surveyId, questionUserId, orderId) => {
    try {
      Taro.showLoading({ title: '加载中...' });

      // 使用医生端的问卷详情接口
      const response = await Api.getQuestionDetailById({
        id: surveyId,
        questionUserId: questionUserId,
        orderId: orderId,
        examId: surveyId,
        checkSubmit: 'yes'
      });

      const { code, data = {} } = response || {};

      if (code === 0) {
        // 处理问卷描述
        try {
          data.examDesc = (data.examDesc || '').split('\n');
        } catch (error) {
          data.examDesc = [data.examDesc || ''];
        }

        const quesInfo = data || {};
        const titleList = data.titleList || [];

        // 处理问卷数据，参考用户端实现
        this.processQuestionnaireData(titleList);



        this.setState({
          quesInfo,
          titleList,
          loading: false
        });
      } else {
        this.setState({
          error: true,
          loading: false
        });
        Taro.showToast({
          title: '获取问卷数据失败',
          icon: 'none'
        });
      }
    } catch (error) {
      this.setState({
        error: true,
        loading: false
      });
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      Taro.hideLoading();
    }
  }

  // 处理问卷数据，参考用户端实现
  processQuestionnaireData = (titleList) => {
    titleList.forEach((item) => {
      // 如果是打分题，倒序
      if (item.questionsType == 3) {
        item.optionList.reverse();
      }

      // 如果是下拉单选和多选，增加是否折叠参数isHide
      if (item.questionsType == 7 || item.questionsType == 8) {
        item.isHide = true;
      }

      // 处理单选题和多选题的二级选项
      if (item.questionsType == 0 || item.questionsType == 1) {
        (item.optionList || []).forEach(opt => {
          if (opt.frameDefaultText) {
            const extInputList = opt.frameDefaultText.split(";");
            let extData = (extInputList || []).map(ext => {
              return {
                label: ext,
                value: ""
              };
            });
            opt.extInputList = extData;
          }

          // 处理二级复选框
          if (opt.optionType === '2' && opt.secondOptionContent) {
            opt.secondOptionList = opt.secondOptionContent.split(',');
            opt.secondCheckedMap = {};
          }

          // 处理二级多值填空
          if (opt.optionType === '3' && opt.secondOptionContent) {
            opt.secondContentParts = this.parseSecondFillBlankContent(opt.secondOptionContent);
          }
        });
      }

      // 处理答案数据
      if (item.answerContentList && item.optionList) {
        // 处理单选和多选题的选中状态和二级选项
        if (item.questionsType === '0' || item.questionsType === '1') {
          item.optionList.forEach(opt => {
            opt.checked = false;
            
            // 查找对应的答案
            const answer = item.answerContentList.find(ans => ans.optionNum == opt.optionNum);
            if (answer) {
              opt.checked = true;
              
              // 处理二级内容
              if (answer.secondAnswerContent) {
                // 如果是二级文本框
                if (opt.optionType === '1') {
                  opt.secondAnswerContent = answer.secondAnswerContent;
                }
                
                // 如果是二级复选框
                if (opt.optionType === '2') {
                  const checkedValues = answer.secondAnswerContent.split(',');
                  opt.secondCheckedMap = {};
                  checkedValues.forEach(val => {
                    opt.secondCheckedMap[val] = true;
                  });
                }
                
                // 如果是二级多值填空
                if (opt.optionType === '3') {
                  const answerValues = answer.secondAnswerContent.split(',');
                  if (opt.secondContentParts) {
                    let valueIndex = 0;
                    opt.secondContentParts.forEach(part => {
                      if (part.isInput && valueIndex < answerValues.length) {
                        part.value = answerValues[valueIndex];
                        valueIndex++;
                      }
                    });
                  }
                }
              }
            }
          });
        }
      }
    });
  }

  // 解析二级多值填空内容
  parseSecondFillBlankContent = (content) => {
    if (!content) return [];

    const parts = [];
    let currentIndex = 0;
    let inputIndex = 0;

    const regex = /\{val\}/g;
    let match;
    let lastIndex = 0;

    while ((match = regex.exec(content)) !== null) {
      if (match.index > lastIndex) {
        const textPart = content.substring(lastIndex, match.index);
        if (textPart) {
          parts.push({
            text: textPart,
            isInput: false,
            index: currentIndex++
          });
        }
      }

      parts.push({
        text: '',
        isInput: true,
        index: inputIndex,
        uniqueId: `second_input_${Date.now()}_${inputIndex}`,
        value: ''
      });

      inputIndex++;
      currentIndex++;
      lastIndex = regex.lastIndex;
    }

    if (lastIndex < content.length) {
      const textPart = content.substring(lastIndex);
      if (textPart) {
        parts.push({
          text: textPart,
          isInput: false,
          index: currentIndex++
        });
      }
    }

    return parts;
  }

  // 查看文件
  viewFile = (e) => {
    const { url } = e.currentTarget.dataset;
    if (!url) return;

    if (url.toLowerCase().includes('.pdf')) {
      // PDF文件，跳转到webview查看
      Taro.navigateTo({
        url: `/package1/webview/index?url=${encodeURIComponent(url)}&title=查看PDF`
      });
    } else {
      // 图片文件，使用预览
      Taro.previewImage({
        current: url,
        urls: [url]
      });
    }
  }

  // 渲染附件列表
  renderAttachments = (question) => {
    // 只有当 fileFlg 为 '1' 时才可能有附件
    if (question.fileFlg !== '1') {
      return null;
    }

    // 优先从答案中获取 filePath，如果没有再从题目级别获取
    let filePath = null;
    if (question.answerContentList && question.answerContentList.length > 0) {
      // 查找第一个有 filePath 的答案
      const answerWithFile = question.answerContentList.find(answer => answer.filePath);
      if (answerWithFile) {
        filePath = answerWithFile.filePath;
      }
    }

    // 如果答案中没有，尝试从题目级别获取
    if (!filePath && question.filePath) {
      filePath = question.filePath;
    }

    // 如果还是没有文件路径，则不显示附件
    if (!filePath) {
      return null;
    }

    // 解析文件路径，支持多个文件用逗号分隔
    const filePaths = filePath.split(',').filter(path => path.trim());

    if (filePaths.length === 0) {
      return null;
    }

    return (
      <View className={s.attachmentSection}>
        <View className={s.attachmentTitle}>
          <Text className={s.attachmentTitleText}>附件</Text>
          {question.fileUploadDescribe && (
            <Text className={s.attachmentDesc}>({question.fileUploadDescribe})</Text>
          )}
        </View>
        <View className={s.attachmentList}>
          {filePaths.map((filePath, index) => {
            const fileName = filePath.split('/').pop() || `附件${index + 1}`;
            const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(filePath);
            const isPdf = /\.pdf$/i.test(filePath);

            return (
              <View key={index} className={s.attachmentItem}>
                {isImage ? (
                  <View className={s.imageAttachment} data-url={filePath} onClick={this.viewFile}>
                    <Image
                      className={s.attachmentImage}
                      src={filePath}
                      mode="aspectFill"
                    />
                    <Text className={s.attachmentLabel}>图片{index + 1}</Text>
                  </View>
                ) : isPdf ? (
                  <View className={s.pdfAttachment} data-url={filePath} onClick={this.viewFile}>
                    <View className={s.pdfIcon}>PDF</View>
                    <Text className={s.pdfName}>{fileName}</Text>
                  </View>
                ) : (
                  <View className={s.otherAttachment} data-url={filePath} onClick={this.viewFile}>
                    <View className={s.fileIcon}>文件</View>
                    <Text className={s.fileName}>{fileName}</Text>
                  </View>
                )}
              </View>
            );
          })}
        </View>
      </View>
    );
  }

  render() {
    const { loading, error, scrollHeight, quesInfo, titleList } = this.state;

    if (loading) {
        return (
          <View className={s.container}>
            <View className={s.loading}>加载中...</View>
          </View>
        );
      }

      if (error) {
        return (
          <View className={s.container}>
            <View className={s.error}>加载失败，请重试</View>
          </View>
        );
      }

      return (
        <View className={s.container}>
        <ScrollView
          className={s.scrollView}
          scrollY
          style={{ height: `${scrollHeight}px` }}
        >
          {/* 问卷头部信息 */}
          {!hideHeader && (
            <View className={s.surveyHead}>
              <View className={s.surveyTitle}>{quesInfo.examTitle}</View>
              <View className={s.surveyTime}>
                有效期：{quesInfo.beginTime} 至 {quesInfo.endTime}
              </View>
              <View className={s.surveyDes}>
                {quesInfo.examDesc && quesInfo.examDesc.map((item, index) => (
                  <View key={index} className={s.descLine}>{item}</View>
                ))}
                <View className={s.publishUnit}>发布单位：{quesInfo.releaseCompany}</View>
              </View>
            </View>
          )}

          {/* 问卷内容 */}
          <View className={s.surveyBody}>
            {titleList && titleList.length > 0 ? titleList.map((question) => (
              <View key={question.titleId} className={s.questionItem}>
                {/* 单选题 */}
                {question.questionsType === '0' && (
                  <View className={s.questionContainer}>
                    <View className={s.questionTitle}>
                      {question.titleNum}、{question.questionsTitle}
                      {question.required === '1' && <Text className={s.required}>*</Text>}
                    </View>
                    <View className={s.optionList}>
                      {question.optionList && question.optionList.map((option) => (
                        <View key={option.optionNum} className={s.optionItem}>
                          <View className={s.optionContent}>
                            <View className={`${s.radioButton} ${option.checked ? s.checked : ''}`}>
                              {option.checked && <View className={s.radioInner}></View>}
                            </View>
                            <Text className={s.optionText}>{option.optionContent}</Text>
                          </View>

                          {/* 二级内容 */}
                          {option.checked && (
                            <View>
                              {option.optionType === '1' && option.secondAnswerContent && (
                                <View className={s.secondaryContent}>
                                  <Text className={s.secondaryText}>{option.secondAnswerContent}</Text>
                                </View>
                              )}
                              {option.optionType === '2' && option.secondCheckedMap && (
                                <View className={s.secondaryContent}>
                                  <View className={s.secondaryTitle}>已选择：</View>
                                  <View className={s.secondaryCheckboxList}>
                                    {Object.keys(option.secondCheckedMap).filter(key => option.secondCheckedMap[key]).map((selectedOption, idx) => (
                                      <View key={idx} className={s.secondaryCheckboxItem}>
                                        <View className={s.secondaryCheckbox}>✓</View>
                                        <Text className={s.secondaryCheckboxText}>{selectedOption}</Text>
                                      </View>
                                    ))}
                                  </View>
                                </View>
                              )}
                              {option.optionType === '3' && option.secondContentParts && option.secondContentParts.length > 0 && (
                                <View className={s.secondaryContent}>
                                  <View className={s.secondaryFillBlankContainer}>
                                    {option.secondContentParts.map((part, partIndex) => (
                                      <View key={part.uniqueId || partIndex} className={s.secondaryFillBlankPart}>
                                        {!part.isInput ? (
                                          <Text className={s.secondaryFillBlankText}>{part.text}</Text>
                                        ) : (
                                          <View className={s.secondaryFillBlankAnswer}>
                                            <Text className={s.secondaryFillBlankValue}>{part.value || '___'}</Text>
                                          </View>
                                        )}
                                      </View>
                                    ))}
                                  </View>
                                </View>
                              )}
                            </View>
                          )}
                        </View>
                      ))}
                    </View>
                  </View>
                )}

                {/* 单选题附件 */}
                {question.questionsType === '0' && this.renderAttachments(question)}



                {/* 多选题 */}
                {question.questionsType === '1' && (
                  <View className={s.questionContainer}>
                    <View className={s.questionTitle}>
                      {question.titleNum}、{question.questionsTitle}
                      {question.required === '1' && <Text className={s.required}>*</Text>}
                    </View>
                    <View className={s.optionList}>
                      {question.optionList && question.optionList.map((option) => (
                        <View key={option.optionNum} className={s.optionItem}>
                          <View className={s.optionContent}>
                            <View className={`${s.checkboxButton} ${option.checked ? s.checked : ''}`}>
                              {option.checked && <View className={s.checkboxInner}>✓</View>}
                            </View>
                            <Text className={s.optionText}>{option.optionContent}</Text>
                          </View>

                          {/* 二级内容 */}
                          {option.checked && (
                            <View>
                              {option.optionType === '1' && option.secondAnswerContent && (
                                <View className={s.secondaryContent}>
                                  <Text className={s.secondaryText}>{option.secondAnswerContent}</Text>
                                </View>
                              )}
                              {option.optionType === '2' && option.secondCheckedMap && (
                                <View className={s.secondaryContent}>
                                  <View className={s.secondaryTitle}>已选择：</View>
                                  <View className={s.secondaryCheckboxList}>
                                    {Object.keys(option.secondCheckedMap).filter(key => option.secondCheckedMap[key]).map((selectedOption, idx) => (
                                      <View key={idx} className={s.secondaryCheckboxItem}>
                                        <View className={s.secondaryCheckbox}>✓</View>
                                        <Text className={s.secondaryCheckboxText}>{selectedOption}</Text>
                                      </View>
                                    ))}
                                  </View>
                                </View>
                              )}
                              {option.optionType === '3' && option.secondContentParts && option.secondContentParts.length > 0 && (
                                <View className={s.secondaryContent}>
                                  <View className={s.secondaryFillBlankContainer}>
                                    {option.secondContentParts.map((part, partIndex) => (
                                      <View key={part.uniqueId || partIndex} className={s.secondaryFillBlankPart}>
                                        {!part.isInput ? (
                                          <Text className={s.secondaryFillBlankText}>{part.text}</Text>
                                        ) : (
                                          <View className={s.secondaryFillBlankAnswer}>
                                            <Text className={s.secondaryFillBlankValue}>{part.value || '___'}</Text>
                                          </View>
                                        )}
                                      </View>
                                    ))}
                                  </View>
                                </View>
                              )}
                            </View>
                          )}
                        </View>
                      ))}
                    </View>
                  </View>
                )}

                {/* 多选题附件 */}
                {question.questionsType === '1' && this.renderAttachments(question)}

                {/* 填空题 */}
                {question.questionsType === '18' && (
                  <View className={s.questionContainer}>
                    <View className={s.questionTitle}>
                      {question.titleNum}、{question.questionsTitle}
                      {question.required === '1' && <Text className={s.required}>*</Text>}
                    </View>
                    <View className={s.fillBlankContainer}>
                      {question.blankValues && question.blankValues.length > 0 ? (
                        <View className={s.fillBlankAnswers}>
                          {question.blankValues.map((value, idx) => (
                            <View key={idx} className={s.fillBlankAnswer}>
                              <Text className={s.fillBlankLabel}>填空{idx + 1}：</Text>
                              <Text className={s.fillBlankValue}>{value || '___'}</Text>
                            </View>
                          ))}
                        </View>
                      ) : (
                        <View className={s.noAnswer}>
                          <Text>暂无答案</Text>
                        </View>
                      )}
                    </View>
                  </View>
                )}

                {/* 填空题附件 */}
                {question.questionsType === '18' && this.renderAttachments(question)}

                {/* 不支持的题型 */}
                {question.questionsType !== '0' && question.questionsType !== '1' && question.questionsType !== '18' && (
                  <View className={s.questionContainer}>
                    <View className={s.questionTitle}>
                      {question.titleNum}、{question.questionsTitle}
                    </View>
                    <View className={s.unsupportedType}>
                      <Text>暂不支持此题型的查看（类型：{question.questionsType}）</Text>
                    </View>
                  </View>
                )}

                {/* 其他题型附件 */}
                {question.questionsType !== '0' && question.questionsType !== '1' && question.questionsType !== '18' && this.renderAttachments(question)}
              </View>
            )) : (
              <View className={s.noQuestions}>
                <Text>暂无题目数据</Text>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    );
  }
}
