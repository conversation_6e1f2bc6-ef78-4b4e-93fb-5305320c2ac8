page {
  height: 100%;
  background: $color-bg;
}
.colorRed{
  color: red;
}
.patientPage {
  &Header{
    padding: 0 24px;
    height: 92px;
    background-color: #FFF;
    &Item{
      text-align: center;
      color: #000000;
      font-size: 30px;
      font-weight: 600;
      &Icon{
        margin-right: 24px;
        width: 36px;
        height: 36px;
      }
    }
    &Split{
      margin: 0 24px;
      height: 30px;
      width: 2px;
      background-color: #D9D9D9;
    }
  }

  &Body{
    padding: 24px;
    box-sizing: border-box;
    height: calc(100vh - 92px);
  }

  .search{
    padding: 0 32px;
    height: 72px;
    border: 1px solid $color-primary;
    border-radius: 8px;
  }

  .patient {
    margin-top: 24px;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 0 20px;
    // border-bottom: 1px solid $color-border;
    border-radius: 8px;
    image {
      width: 96px;
      height: 96px;
    }

    text {
      color: #989898;
      font-size: 30px;
      padding-right: 13px;
    }

    .rowModule {
      flex: 1;
      padding: 25px 30px;

      .rowTop {
        justify-content: space-between;
        display: flex;
        padding-bottom: 5px;

        .name {
          font-size: '34px';
          color: #2D2D2D
        }
      }
    }

    .tagInfo {
      color: #989898;
      font-size: 30px;
    }
  }

  .noData {
    width: 100%;
    text-align: center;
    padding-top: 25%;
    font-size: 30px;
    color: $color-text;

    image {
      width: 400px;
      height: 190px;
      margin-bottom: 22px;
    }
  }
}
