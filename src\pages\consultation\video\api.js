import { post } from '@/utils/request';

/**
 * 获取状态
 */
export const getStatus = (param) => post('/api/doctor/liveStream/getStatus', param, false);

/**
 * 获取拉流地址
 */
export const getUsers = (param) => post('/api/docChat/queryGroupMember', param, false, false);
/**
 * 获取拉流地址
 */
export const pullStream = (param) => post('/api/doctor/liveStream/pullStream', param, false, false);

/**
 * 开始视频
 * 
 * 
 * 
 */
export const startLive = (param) => post('/api/doctor/liveStream/startLive', param);

/**
 * 获取拉流地址
 */
export const endLive = (param) => post('/api/doctor/liveStream/endLive', param, false, false);
