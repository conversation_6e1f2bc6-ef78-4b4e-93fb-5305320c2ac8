import Taro, {Component} from '@tarojs/taro';
import {Image, Navigator, ScrollView, Text, Textarea, View, Block} from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import * as Utils from '@/utils/utils';
import {ChatType} from '@/utils/config';
import ChatContent from './component/chat-content';
import iconOpinion from '@/static/image/icon-consultation-opinion.png';
import iconReport from '@/static/image/icon-consultation-report.png';
import iconEnd from '@/static/image/icon-consultation-end.png';

const app = Taro.getApp();
export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '问诊详情',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);

    this.state = {
      id: '',
      pid: '',
      chatType: '', // 聊天类型
      patName: '',
      consultationId: '',
      list: [],
      msgText: null,
      isShow: false,
      showPlus: false,
      interval: null,
      isLoading: false,
      bottomItem: [],
      bottomHeight: '230',
      videoIsMin: false,
      viewId: "",
      hasAdminAuth: false,
      isEndFeeType: '',
      expressionList: (() => {
        let n = 1;
        return Array.from({ length: 43 }, () => {
          const scaleObj = {
            14: "1.3",
            16: "1.15"
          };
          const item = {
            scale: scaleObj[n] || 1,
            url: `https://hlwyy.zxxyyy.cn/hospital/his-miniapp/p242/emijo/emijo-${n++}.png`
          };
          return item;
        });
      })(),
      isShowExpression: false,
      intervalStatus: '',
      showReceiveModal: true,
    };
  }

  async componentWillMount() {
    const options = this.$router.params;
    const { id = '', pid = '', isEndFeeType = '', chatType = '', patName = '', consultationId = '' } = options;
    const title = chatType === ChatType.clinic ? '网络问诊' : '会诊';
    Taro.setNavigationBarTitle({ title });
    await this.setState({id, pid, isEndFeeType, chatType, patName, consultationId});
    this.initBottomItem();
  }

  componentDidMount() {
    const {chatType} = this.state;
    if (chatType === ChatType.consultation) {
      this.getEndConsultationAuth();
    }

    this.getChat();
  }

  componentDidShow() {
    const interval = setInterval(() => this.getChat("next", true), 2000);
    const intervalStatus = setInterval(() => this.getStatus(this.state.id), 2000);
    this.setState({interval, intervalStatus, videoIsMin: app.globalData.videoIsMin});
  }

  componentWillUnmount() {
    clearInterval(this.state.interval);
    clearInterval(this.state.intervalStatus);
    app.globalData.videoIsMin = false;
    app.globalData.hasEnterVideoRoomAgain = false;
  }

  componentDidHide() {
    clearInterval(this.state.interval);
    clearInterval(this.state.intervalStatus);
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '问诊详情',
    navigationBarTextStyle: 'black'
  };

  bottomTap(event, index) {
    // 通过底栏操作出现modal时，需要将底栏隐藏，避免textarea产生的层级问题
    this.setState({showPlus: false}, this.initBottomItem);
    this[event]();
  }
  openModal() {
    this.setState({isShow: true});
  }
  cancel() {
    this.setState({isShow: false});
  }
  sure() {
    this.cancel();
    this.closure();
  }
  showPlus() {
    this.setState({showPlus: !this.state.showPlus, isShowExpression: false}, this.initBottomItem);
  }
  hidePlus() {
    this.cancel();
    this.initBottomItem();
  }
  inputMsg(e) {
    console.log(e);
    this.setState({msgText: e.detail.value.replace(/(^\s*)/g,"")});
  }
  sendMsg(e) {
    // const msg = e.detail.value;
    const {msgText} = this.state;
    this.setState({focus: false});
    if (!msgText) {
      Taro.showToast({
        title: '请先输入回复信息',
        icon: 'none',
        duration: 800
      });
      return;
    }
    this.send({
      groupId: this.state.id,
      content: msgText,
      type: '1'
    });
    const that = this;
    setTimeout(() => {
      that.setState({focus: true});
    }, 500);
  }
  hidden() {
    this.setState({showId: ''});
  }
  blurText() {
    this.getBottomHeight();
  }
  scrollTop() {
    // 滚动到顶部加载会话
    this.getChat("prev");
  }

  async showExpression() {
    const {isShowExpression, showPlus} = this.state;
    this.setState({isShowExpression: !isShowExpression, showPlus: !showPlus});
  }

  async getEndConsultationAuth() {
    const {consultationId} = this.state;
    const {code, data} = await Api.hasAdminAuth({consultationId});
    if (code === 0) {
      this.setState({hasAdminAuth: data});
    }
  }

  async getChat(type = "", isScrollToBottom) {
    let {isLoading, list = [], id} = this.state;
    if (isLoading) {
      return false;
    }
    const param = {};

    if (type === "next") {
      if (list[list.length - 1] && list[list.length - 1].createTime) {
        param.endTime = list[list.length - 1].createTime;
      }
    } else if (type === "prev") {
      if (list[0] && list[0].createTime) {
        param.startTime = list[0].createTime;
      }
    }
    await this.setState({isLoading: true});
    const { code, data = [] } = await Api.getChat({
      groupId: id,
      ...param
    });
    await this.setState({isLoading: false});
    if (code == 0) {
      // this.image = data.image;
      data.recordList = (data.recordList || []).reverse().map(item => {
        // 获取显示时间 当天仅显示时间 非当天显示日期 非本年显示年份
        item.time = Utils.getChatShowTime(item.createTime || "");
        return item;
      });
      let firstItemId;
      if (type === "next" || !type) {
        // 往下查询或初次查询
        list = list.concat(data.recordList);
      } else if (type === 'prev') {
        Taro.showLoading();
        if (list.length > 0) {
          // 查询时的第一个元素
          firstItemId = (list[0] || {}).id;
        }
        list = data.recordList.concat(list);
        Taro.hideLoading();
      }
      await this.setState({list}, async () => {
        let viewId;
        if (data.recordList.length > 0 && list.length > 0) {
          // 滚动到的元素id
          viewId = `id-${
            !type || isScrollToBottom
              ? (list[list.length - 1] || {}).id
              : firstItemId
            }`;
        } else if (type === "next") {
        }
        await this.setState({viewId}, ()=>console.log(viewId));
      });

    }
  }

  async send(param) {
    const chatlist = await Api.sendMsg(param);
    if (chatlist.code == 0) {
      await this.setState({msgText: ''});
      this.getChat('next',true);
    }
  }
  async closure() {
    Taro.showLoading({ title: '操作中...', mask: true });
    const closure = await Api.closure({
      id: this.state.id,
    });
    if (closure.code == 0) {
      Taro.hideLoading();
      Taro.switchTab({
        url: '/pages/msg/index'
      })
    }
  }
  async sendPicture1() {
    const chooseRes = await Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['camera']
    });
    if (chooseRes.errMsg == 'chooseImage:ok') {
      const tempFilePath = chooseRes.tempFilePaths[0];
      Taro.showLoading({ title: '上传中...', mask: true });
      let url = '';
      if (tempFilePath.length > 0) {
        url = await Api.uploadImages(tempFilePath);
      }
      Taro.hideLoading();
      if (url) {
        this.send({ groupId: this.state.id, content: url ,type: '3' });
      }
    }
  }
  async sendPicture2(type) {
    const chooseRes = await Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album']
    });
    if (chooseRes.errMsg == 'chooseImage:ok') {
      const tempFilePath = chooseRes.tempFilePaths[0];
      Taro.showLoading({ title: '上传中...', mask: true });
      let url = '';
      if (tempFilePath.length > 0) {
        url = await Api.uploadImages(tempFilePath);
      }
      Taro.hideLoading();
      if (url) {
        this.send({groupId: this.state.id, content: url ,type: '3' });
      }
    }
  }
  async sendExpression(e) {
    const { url: item = '' } = e.currentTarget.dataset;
    // 发送表情
    if (!item) {
      return false;
    }
    this.setState({showPlus: false, isShowExpression: false}, this.initBottomItem);
    this.send({
      type: 5,
      groupId: this.state.id,
      content: item || "",
    });
  }
  endConsultation() {
    Taro.showModal({
      title: "提示",
      content: "是否结束会诊？",
      showCancel: true,
      confirmText: "确定",
      confirmColor: '#3ECDB5',
      success: async (res) => {
        if (res.confirm) {
          const {consultationId} = this.state;
          const {code} = await Api.endConsultation({consultationId, cancelType: 0});
          if (code === 0) {
            Taro.showToast({
              title: '结束成功',
              icon: 'success',
              duration: 2000
            });
            setTimeout(() => {
              Taro.navigateBack({
                delta: 1
              });
            }, 2000);
          }
        }
      }
    });
  }
  async getStatus(groupId) {
    const { data, code } = await Api.getStatus({ groupId });
    const {showReceiveModal, intervalStatus, id} = this.state;
    if (data.status == 'live') {
      if (showReceiveModal) {
        clearInterval(intervalStatus);
        await Taro.showModal({
          title: "提示",
          content: "医生发起视频问诊，是否进入问诊",
          showCancel: true,
          confirmText: "确定",
          confirmColor: '#3ECDB5',
          success: (res) => {
            if (res.confirm) {
              Taro.navigateTo({
                url: `/pages/consultation/video/index?id=${id}`,
              })
            } else {
              this.setState({showReceiveModal: !showReceiveModal});
            }
          }
        });
      }
      return;
    }
  }
  initBottomItem() {
    const {chatType, hasAdminAuth} = this.state;
    let bottomItem = [];
    if (chatType === ChatType.clinic) {
      bottomItem = [
        {
          name: '拍照',
          url: '',
          icon: `${$CDN_DOMAIN}/ih-miniapp/camera.png`,
          event: 'sendPicture1',
          show: true
        },
        {
          name: '图片',
          url: '',
          icon: `${$CDN_DOMAIN}/ih-miniapp/tp.png`,
          event: 'sendPicture2',
          show: true
        },
        {
          name: '进入视频',
          url: `/pages/consultation/video/index?id=${this.state.id}`,
          icon: `${$CDN_DOMAIN}/ih-miniapp/chat-video.png`,
          event: '',
          show: false,
        },
        {
          name: '表情',
          url: '',
          icon: `${$CDN_DOMAIN}/ih-miniapp/expression.png`,
          event: 'showExpression',
          show: true,
        },
      ];
    } else if (chatType === ChatType.consultation) {
      bottomItem = [
        {
          name: '图片',
          url: '',
          icon: `${$CDN_DOMAIN}/ih-miniapp/tp.png`,
          event: 'sendPicture2',
          show: true
        },
        {
          name: '进入视频',
          url: `/pages/consultation/video/index?id=${this.state.id}`,
          icon: `${$CDN_DOMAIN}/ih-miniapp/chat-video.png`,
          event: '',
          show: true,
        },
        {
          name: '会诊意见',
          url: `/pages/consultation-opinion/index?consultationId=${this.state.consultationId}&type=0`,
          icon: iconOpinion,
          event: '',
          show: true,
        },
        {
          name: '会诊报告',
          url: `/pages/consultation-opinion/index?consultationId=${this.state.consultationId}&type=1`,
          icon: iconReport,
          event: '',
          show: hasAdminAuth,
        },
        {
          name: '结束会诊',
          url: ``,
          icon: iconEnd,
          event: 'endConsultation',
          show: hasAdminAuth,
        },
      ];
    }

    this.setState({bottomItem})
    this.getBottomHeight();
  }
  /**
   * 根据显示的按钮的数量计算高度
   */
  getBottomHeight() {
    const {showPlus, bottomItem} = this.state;
    if (!showPlus) {
      this.setState({bottomHeight: '100'});
      return;
    }
    const height = ['484', '648', '848'];
    const newItem = bottomItem.filter(item => {
      return item.show;
    });
    const len = newItem.length;
    const index = len % 4 == 0 ? parseInt(len / 4) - 1 : parseInt(len / 4);
    this.setState({bottomHeight: height[index]});
  }

  render() {
    const {
      isEndFeeType,
      chatType,
      patName,
      consultationId,
      viewId,
      bottomHeight,
      videoIsMin,
      msgText,
      bottomItem,
      id,
      pid,
      list,
      showPlus,
      isShowExpression,
      expressionList,
      isShow
    } = this.state;

    return (
      <View className={`${s.container} ${chatType===ChatType.consultation&&s.pt140}`} onClick={this.hidden}>
        {
          chatType === ChatType.consultation ? (
            <View className={s.header}>
              <View className={s.headerContent}>
                <View className={s.patName}>
                  {patName}
                </View>
                <View className={s.document}>
                  <Navigator url={`/pages/personal-center/record-detail/index?id=${consultationId}`}>会诊单详情</Navigator>
                  <View className={s.divide}>|</View>
                  <Navigator url={`/pages/patient/health-record-v2/index?patientPid=${pid}`}>健康档案</Navigator>
                </View>
              </View>
            </View>
          ) : (
            isEndFeeType == '0' && (<View className={s.endChatBtn}>
          <Text onClick={this.openModal}>结束问诊</Text>
          </View>)
          )
        }
        <ScrollView scrollIntoView={viewId ? viewId : ''}
          scrollY
          style={{height: isEndFeeType == '1' ? '100%' : 'calc(100% - ' + bottomHeight + 'rpx)'}}
          onClick={this.hidePlus}
          onScrollToUpper={this.scrollTop}
        >
          <View className={s.content}>
            {
              list.map((item, index) => {
                return (<ChatContent
                  key={item.id}
                  id={`id-${item.id}`}
                  content={(item.type === '1' || item.type === '7')&&item.content}
                  image={(item.type === '3'||item.type === '5')&&item.content}
                  logo={item.headImg}
                  cid={item.id}
                  consultationId={consultationId}
                  direction={item.messageUserType == '0' ? 'left' : 'right'}
                  name={item.sendUserName}
                  type={item.type}
                  time={(item.createTime&&item.type !== '1') ? item.createTime : ''}
                />)
              })
            }
            {
              isEndFeeType == '1' && (<View style={{
                width: '90%',
                background: '#ccc',
                borderRadius: '20px',
                textAlign: 'center',
                color: '#fff',
                margin: '0 auto'
              }}
              >该笔问诊订单已结束</View>)
            }
          </View>
        </ScrollView>
        {
          isEndFeeType == '0' && (
            <View className={s.operationBox}>
              {
                videoIsMin && <Navigator url={`/pages/consultation/video/index?id=${id}`}
                  className={`${s.videoMinBox} f-center`}
                >视频中</Navigator>
              }

              <View className={s.top}>
                <Textarea className={s.input} autoHeight cursorSpacing='14' value={msgText} onInput={this.inputMsg} onBlur={this.blurText} fixed maxlength={500}></Textarea>
                <Image src={`${$CDN_DOMAIN}/ih-miniapp/plus.png`} onClick={this.showPlus} />
                {
                  msgText ?
                    <Image src={`${$CDN_DOMAIN}/ih-miniapp/icon-send-primary.png`} onClick={this.sendMsg} />
                    : <Image src={`${$CDN_DOMAIN}/ih-miniapp/icon-send-disabled.png`} />
                }
              </View>
              {
                showPlus && (
                  <View className={s.bottom}>
                    {
                      bottomItem.map((item, index) => {
                        return (
                          item.show ? (
                            <Block key={index}>
                              {
                                item.event && (
                                  <View onClick={() => this.bottomTap(item.event, index)}>
                                    <Image src={item.icon} />
                                    <View>{item.name}</View>
                                  </View>
                                )
                              }
                              {item.url && (
                                <Navigator url={item.url}>
                                  <image src={item.icon} />
                                  <View>{item.name}</View>
                                </Navigator>
                              )
                              }
                            </Block>
                          ) : null
                        )
                      })
                    }
                  </View>
                )
              }
              {
                isShowExpression && (
                  <View className={s.expressionBlock}>
                    {
                      expressionList.map((item, index) => {
                        return (
                          <View key={index} className={s.expressionItem} onClick={this.sendExpression} data-url={item.url}>
                            <Image src={item.url} mode='widthFix' style={{transform: `scale${item.scale}`}} />
                          </View>
                        )
                      })
                    }
                  </View>
                )
              }
            </View>
          )
        }

        {
          isShow && (
            <View className={s.modal}>
              <View className={s.modalBody}>
                <View className={s.modalTitle}>确定结束咨询吗？</View>
                <View className={s.modalContent}>结束本次咨询后，患者将无法再补充本次咨询。为避免医患纠纷，建议您与患者达成一致。</View>
                <View className={s.modalFooter}>
                  <Text onClick={this.cancel}>取消</Text>
                  <Text onClick={this.sure}>确定</Text>
                </View>
              </View>
            </View>
          )
        }
      </View>
    )
  }
}
