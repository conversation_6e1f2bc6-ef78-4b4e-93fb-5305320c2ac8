import Taro, { Component } from '@tarojs/taro';
import { View, CheckboxGroup, Checkbox, Textarea, Image, Button } from '@tarojs/components';
import Field from '@/component/field';
import Upload from '@/component/upload';
import { Tabs } from '@/component/Tabs'

import camera from '@/resources/images/camera.png'
import s from './index.module.scss'
import * as API from './api'


export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      moreArray: [
        {
          value: 'FSH',
          name: 'FSH',
          checked: false
        },
        {
          value: 'LH',
          name: 'LH',
          checked: false
        },
        {
          value: '睾酮',
          name: '睾酮',
          checked: false
        },
        {
          value: 'E2',
          name: 'E2',
          checked: false
        },
        {
          value: 'PRL',
          name: 'PRL',
          checked: false
        },
        {
          value: 'INHB',
          name: 'INHB',
          checked: false
        }
      ],
      lessArray: [
        {
          value: 'FSH',
          name: 'FSH',
          checked: false
        },
        {
          value: 'LH',
          name: '<PERSON><PERSON>',
          checked: false
        },
        {
          value: '睾酮',
          name: '睾酮',
          checked: false
        },
        {
          value: 'E2',
          name: 'E2',
          checked: false
        },
        {
          value: 'PRL',
          name: 'PRL',
          checked: false
        },
        {
          value: 'INHB',
          name: 'INHB',
          checked: false
        }
      ],
      sampleNumber: '',
      tempFilePaths: [],
      patientName: '', 
      idNumber: '', 
      mobile: '',
      orderId: '',
      productId: '',
      deptId: '', 
      deptName: '',
      sampleDetail: {},
      field2: [],
      field3: [],
      field1: `生育史:孕 (0) 次，产 (0) 次，流产 (0) 次。
      双方不良孕产详述 (含孕周、是否做过遗传学检测、病理等) :            
      现存子女详述(含年龄、健康状况、遗传学检测等) :                    
      有无腮腺炎：       手术情况：     自身免疫药：      药物服用史：       睾丸大小:             
      有无B超检测结果：       ` 
    }
  }

  componentWillMount() {
    const { patientName, idNumber, mobile, orderId, productId, deptId, deptName, sampleNumber } = this.$router.params;
    this.setState({patientName, idNumber, mobile, orderId, productId, deptId, deptName})
    if(sampleNumber){
      this.getPerfectInfo(sampleNumber)
    }
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '完善样本信息',
    navigationBarTextStyle: 'black',
  };
  
  getPerfectInfo = async(sampleNumber) => {
    const { code, data } = await API.getBySamplenumber({id: sampleNumber})
    if (code !== 0) return
    const informObj = data.informedConsentForm ? JSON.parse(data.informedConsentForm) : {};
    const field1 = informObj.field1 ? informObj.field1 : this.state.field1;
    const field2 = informObj.field2.split(',');
    const field3 = informObj.field3.split(',');
    this.setState({ 
      sampleDetail: data,
      sampleNumber: data.sampleNumber,
      expressnumber: data.expressnumber || '',
      field1,
      field2,
      field3,
      tempFilePaths: data.files ? data.files.split(',') : []
    })
    field2.forEach(v => {
      this.state.moreArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
    field3.forEach(v => {
      this.state.lessArray.forEach(array => {
        if(v === array.value){
          array.checked = true;
        }
      })
    })
  }

  handleSetState = (key, val) => {
    this.setState({ [key]: val })
  }

  scanCode = async() => {
    const res = await Taro.scanCode({ scanType: ['barCode'], onlyFromCamera: true,  })
    Taro.showToast({ title: '扫描成功', icon: 'none' })
    this.setState({
      sampleNumber: res.result,
    }, this.handleSearch)
  }

  checkunique = async(sampleNumber) => {
    const { code } = await API.checkunique({sampleNumber})
    if(code == 0) {
      this.perfectInfo()
    }
  }

  perfectInfo = async() => {
    const { sampleNumber, expressnumber, field1, field2, field3, tempFilePaths, orderId, productId, deptId, deptName, sampleDetail = {}, patientName, idNumber, mobile } = this.state
    const params = {
      orderId,
      productId,
      sampleNumber,
      expressnumber,
      institutionId: deptId,
      submitHospital: deptId,
      submitDoctor: deptName,
      sjzName: patientName,
      sjzIdNum: idNumber,
      sjzPhone: mobile,
      informedConsentForm: JSON.stringify({
        field1,
        field2: field2.length ? field2.join(',') : '',
        field3: field3.length ? field3.join(',') : ''
      }),
      files: tempFilePaths.length ? tempFilePaths.join(',') : ''
    }
    if(sampleDetail.id){
      params.id = sampleDetail.id;
    }
    const { code } = await API.prefectSample(params)
    if (code !== 0) return
    Taro.navigateBack({
      delta: 1
    });
  }

  confirm = () => {
    const { sampleNumber, sampleDetail = {} } = this.state
    if(!sampleDetail.id){
      this.checkunique(sampleNumber)
      return
    }
    this.perfectInfo()
   
  }

  cancel = () => {
    Taro.navigateBack({
      delta: 1
    });
  }

  render() {
    const { moreArray, lessArray, sampleNumber, expressnumber, field1, field2, field3, tempFilePaths, patientName, idNumber, mobile, sampleDetail  } = this.state
    return (
      <View className={s.page}>
        <View className={s.page_header}>
          <View className={[s.title, s.header_titler]}>样本信息</View>
          {sampleDetail.id ? <Field
            label='样本编号'
            labelWidth='200rpx'
            placeholder='请输入样本编号'
            required
            disabled
            value={sampleNumber}
            onSetValue={v => this.handleSetState('sampleNumber', v)}
          />
            :
          <Field
            label='样本编号'
            labelWidth='200rpx'
            placeholder='请输入样本编号'
            required
            value={sampleNumber}
            onSetValue={v => this.handleSetState('sampleNumber', v)}
            renderSuffix={() => <Image className={s.field_right} src={camera} onClick={this.scanCode} />}
          />
          }
          <Field
            label='样本快递编号'
            labelWidth='200rpx'
            placeholder='请输入样本快递编号'
            value={expressnumber}
            border={false}
            onSetValue={v => this.handleSetState('expressnumber', v)}
          />
        </View>
        <View className={s.page_body}>
          <View className={s.title}>受检者信息</View>
          <View className={s.patient_content}>
            <View className={s.name}>{patientName}</View>
            <View className={s.text}>身份证号：{idNumber}</View>
            <View className={s.text}>手机号：{mobile}</View>
          </View>
          <View className={s.title}>病史（请根据实际情况填写）</View>
          <Textarea
            className={s.text_area}
            value={field1}
            placeholder='请输入病史'
            maxlength={10000}
            onInput={e => this.handleSetState('field1', e.detail.value)}
          />
          <View className={s.title}>对异常参数打勾</View>
          <View className={s.little_title}>检测结果超过正常值的有：</View>
          <CheckboxGroup className='s.project_block__list' onChange={e => this.handleSetState('field2', e.detail.value)}>
            {
              moreArray.map(item => (
              <Checkbox
                key={item.value}
                className={['f-1',s.check_lable]}
                value={item.value}
                checked={item.checked}
                color='#30A1A6'
              >{ item.name }</Checkbox>
              ))
            }
          </CheckboxGroup>
          <View className={[s.little_title, s.mt]}>检测结果低于正常值的有：</View>
          <CheckboxGroup className='s.project_block__list' onChange={e => {this.handleSetState('field3', e.detail.value); }}>
            {
              lessArray.map(item => (
                <Checkbox
                  key={item.value}
                  className={['f-1',s.check_lable]}
                  value={item.value}
                  checked={item.checked}
                  color='#30A1A6'
                >{ item.name }</Checkbox>
              ))
            }
          </CheckboxGroup>
        </View>
        <Upload
          className={s.img}
          title='知情同意书照片'
          fileList={tempFilePaths}
          sizeType={['original']}
          performation='1'
          limit={9}
          updateFileList={list => this.setState({ tempFilePaths: list })}
        />
        <Button
          className={s.btn}
          onClick={this.confirm}
        >确认</Button>
        <Button
          className={[s.btn, s.cancel]}
          onClick={this.cancel}
        >取消</Button>
      </View>
      
    )
  }
}
