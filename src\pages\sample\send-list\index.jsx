import Taro, { Component } from '@tarojs/taro';
import { View, Text, Image, Input, Picker, Block, Icon } from '@tarojs/components';
import dayjs from "dayjs";
import Empty from '@/component/empty';

import copyPng from '@/resources/images/sample/copy.png'
import arrowPng from '@/resources/images/sample/arrow.png'

import s from './index.module.scss'
import * as API from '../api'

export const STATUS_MAP = {
  S: '已寄件',
  C: '已取消',
  P: '异常',
  G: '已签收'
}

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      records: [],
      endTime: '',
      startTime: '',
      searchData: ''
    }
  }

  componentWillMount() {


  }

  componentDidShow() {
    const currentDate = new Date();

    currentDate.setMonth(currentDate.getMonth() - 1);
    currentDate.setFullYear(currentDate.getFullYear() - 1);

    const currentYear = new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)) + '-' + (new Date().getDate() < 10 ? '0' + (new Date().getDate()) : new Date().getDate());

    const oneMonthAgo = new Date().getFullYear() + '-' + ((currentDate.getMonth() + 1) < 10 ? '0' + (currentDate.getMonth() + 1) : (currentDate.getMonth() + 1)) + '-' + (new Date().getDate() < 10 ? '0' + (new Date().getDate()) : new Date().getDate());
    this.setState(() => ({
      startTime: oneMonthAgo,
      endTime: currentYear
    }), () => {
      this.getOrderList()
    });
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '样本寄送记录',
    navigationBarTextStyle: 'black',
  };

  /** 样本寄送记录 */
  getOrderList = async () => {
    const { endTime, startTime, searchData } = this.state;
    const { code, data } = await API.getOrderList({
      mailStartTime: startTime,
      mailEndTime: endTime,
      searchData
    })
    if (code !== 0) return
    const records = data || []
    this.setState({ records })
  }

  toDetail = (id) => {
    Taro.navigateTo({ url: `/pages/sample/send-detail/index?id=${id}` })
  }

  bindSearchInput = (e) => {
    const { value } = e.detail;
    this.setState({ searchData: value });

    return value;
  }

  onDateChange = (e, key) => {
    const value = e.detail.value
    this.setState(() => ({
      [key]: value
    }), () => {
      this.getOrderList()
    });
  }

  reset = () => {
    this.setState({
      startTime: '',
      endTime: '',
    })
  }

  /**
   * 复制运单号
   * @param {*} waybillNo 
   * @param {*} e 
   */
  copyWaybillNo = (waybillNo, e) => {
    e.stopPropagation()
    Taro.setClipboardData({
      data: waybillNo,
    })
  }

  /**
   * 取消寄件
   * @param {*} id 
   */
  handleCancel = (id, e) => {
    e.stopPropagation()
    Taro.showModal({
      title: '提示',
      content: '确认取消寄送？',
      confirmColor: '#30A1A6',
      success: res => {
        if (res.confirm) {
          this.cancel(id)
        }
      }
    })
  }

  cancel = async (id) => {
    //console.log('订单id？',id)
    const { code, data } = await API.cancelOrder({ orderId: id })
    if (code !== 0) return
    if (data.resultCode !== '0') {
      Taro.showModal({
        title: '提示',
        content: data.resultMessage || '取消失败',
        showCancel: false,
      });
      return
    }
    Taro.showToast({ title: '取消成功', icon: 'none' })
    setTimeout(this.getOrderList, 1500)
  }



  render() {
    const { records, startTime, endTime, searchData } = this.state
    return (
      <View className={s.page}>
        <View className={s.page_header}>
          <View className={[s.search, 'f-center']}>
            <icon style='margin-right: 8px;' type='search' size='16' color='rgba(0, 0, 0, 0.4)' />
            <Input
              className='f-1'
              placeholder='请输入姓名/电话/运单号'
              confirm-type='search'
              value={searchData}
              onInput={this.bindSearchInput}
              onConfirm={this.getOrderList}
            />

          </View>
          <View className={s.header_daterange}>
            <Picker
              className={s.header_daterange__picker}
              mode='date'
              value={startTime}
              onChange={e => this.onDateChange(e, 'startTime')}
            >
              <View className={s.picker_content}>{startTime || '开始日期'}</View>
            </Picker>
            <Picker
              className={s.header_daterange__picker}
              mode='date'
              value={endTime}
              onChange={e => this.onDateChange(e, 'endTime')}
            >
              <View className={s.picker_content}>{endTime || '结束日期'}</View>
            </Picker>
            {startTime || endTime ?
              <Text className={s.header_daterange__clear} onClick={this.reset}>清空</Text> :
              <Text className={s.header_daterange__clear}></Text>}
          </View>
        </View>

        <View className={s.page_body}>
          {
            records.length ? records.map(v => (
              <View key={v.id} className={s.list_item} onClick={() => this.toDetail(v.id)}>
                <View className={s.list_item_waybill} onClick={this.copyWaybillNo.bind(this, v.mailNo)}>
                  <Text>运单号：{v.mailNo}</Text>
                  <Image src={copyPng} className={s.list_item_copy}></Image>
                </View>
                <View className={s.list_item_content}>
                  <View className={s.list_item_contact}>
                    <Text className={s.list_item_contact_city}>{v.senderCityName}</Text>
                    <Text className={s.list_item_contact_name}>{v.senderName}</Text>
                  </View>
                  <View className={s.list_item_contact}>
                    <View className={['flex', 'f-c-center']}>
                      <Icon
                        size={16}
                        style={{ marginRight: '3px' }}
                        type={(v.status === 'S' || v.status === 'G') ? 'success' : v.status === 'C' ? 'cancel' : 'info'}
                        color={(v.status === 'S' || v.status === 'G') ? '#30A1A6' : v.status === 'C' ? '#C55D5D' : '#E7AA35'}
                      />
                      <Text className={s.list_item_contact_status}>{STATUS_MAP[v.status]}</Text>
                    </View>
                    <Image src={arrowPng} className={s.list_item_arrow}></Image>
                  </View>
                  <View className={s.list_item_contact}>
                    <Text className={s.list_item_contact_city}>{v.receiverCityName}</Text>
                    <Text className={s.list_item_contact_name}>{v.receiverName}</Text>
                  </View>
                </View>
                <Text className={s.list_item_time}>{v.status === 'C' ? '取消时间：' + v.cancelTime : '寄件时间：' + dayjs(v.createTime).format('YYYY-MM-DD HH:mm:ss')}</Text>
                {
                  v.status === 'S' ?
                    <Block>
                      <View className={s.list_item_line}>
                      </View>
                      <View className={s.list_item_btngroup}>
                        <View className={s.list_item_btn} onClick={this.handleCancel.bind(this, v.id)}>
                          <Text>取消寄送</Text>
                        </View>
                      </View>
                    </Block> : null
                }
              </View>
            ))
              :
              <Empty text='暂无记录' />
          }
        </View>
      </View>
    )
  }
}
