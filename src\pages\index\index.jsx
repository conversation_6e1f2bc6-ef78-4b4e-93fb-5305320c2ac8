import Taro, {Component} from '@tarojs/taro';
import {Block, Button, Image, Input, Text, View, Checkbox, CheckboxGroup } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import logoPng from '../../static/image/logo.png'
import {_config} from '../../utils/config'
import { openFile } from '../../utils/utils';

const TIMER_DURATION = 60

const app = Taro.getApp();
export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      phone: '',
      code: '',
      doctorSn: '',
      password: '',
      newPassword: '',
      newCheckPassword: '',
      isRe: true,
      // num: 0,
      aggree: false,
      canLogin: false,
      userType: '',
      /** 0: 手机号登录 1: 账号密码登录 */
      loginType: 0,
      // islogin: true
      timer: null,
      timerDuration: TIMER_DURATION,
    };
  }

  componentWillMount() {
    const username = Taro.getStorageSync('username') || '';
    const password = Taro.getStorageSync('password') || '';
    const token = Taro.getStorageSync('login_access_token') || '';
    const loginType = Taro.getStorageSync('loginType') || '';
    const phone = Taro.getStorageSync('phone') || '';

    if (
      (
        (loginType == 0 && phone) || (loginType == 1 &&  username && password)
      ) && token
    ) {
      Taro.switchTab({
        url: '/pages/patient/index'
      })
      return
    };
    this.authorize();
  }

  componentDidMount() {}

  componentWillUnmount() {
  }
  componentDidShow() {
  }

  componentDidHide() {
  }
  config = {
    navigationBarTitleText: '登录'
  };

  onShareAppMessage() {
    return {
      title: `湖南家辉遗传专科医院`,
      complete: res => {
        console.log(res);
      }
    };
  }

  setCanLogin() {
    const { loginType, doctorSn, password, phone, code } = this.state
    this.setState({
      canLogin: loginType ? (doctorSn && password) : (phone && code)
    })
  }

  changeInput(e, key) {
    this.setState({
      [key]: e.detail.value
    }, this.setCanLogin)
  }

  changeNPwd(e) {
    this.setState({newPassword: e.detail.value});
  }

  checkNewPwd(e) {
    this.setState({newCheckPassword: e.detail.value});
  }


  rePwd() {
    const {newPassword, newCheckPassword} = this.state;
    const reg =
      '^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,}$';
    if (newPassword !== newCheckPassword) {
      Taro.showToast({
        title: '两次密码不一致', //提示的内容,
        icon: 'none', //图标,
        duration: 1400, //延迟时间,
        mask: true, //显示透明蒙层，防止触摸穿透,
        success: () => {
        }
      });
    } else if (!RegExp(reg).test(newPassword)) {
      Taro.showToast({
        title: '请输入符合要求的密码', //提示的内容,
        icon: 'none', //图标,
        duration: 1400, //延迟时间,
        mask: true, //显示透明蒙层，防止触摸穿透,
        success: () => {
        }
      });
    } else {
      this.change({newPassword});
    }
  }

  async getWxUserinfo() {
    return new Promise((resolve, reject) => {
      Taro.getUserInfo({
        success: resolve,
        fail: reject,
      })
    })
  }

  async authorize() {
    Taro.showLoading({ title: '加载中', mask: true });

    try {
      const { code } = await Taro.login();
      const { iv, encryptedData } = await this.getWxUserinfo()
      const res = await Api.authorize({ code, encryptedData, iv });
      if (res.code !== 0) return
      Taro.setStorageSync('login_access_token', res.data.login_access_token);

    } finally {
      Taro.hideLoading();
    }
    console.log()
  }

  async getUserInfo () {
    const { code, data } = await Api.getUserInfo();
    if (code !== 0) return Promise.reject()
    this.setState({ userType: data.userType })
    Taro.setStorageSync('userInfo', JSON.stringify(data));
    Promise.resolve()
  }
  cacheUserAccount() {
    const { loginType, phone, doctorSn: username, password } = this.state
    Taro.setStorageSync('loginType', loginType);
    if (loginType === 0) {
      Taro.setStorageSync('phone', phone);
    } else {
      Taro.setStorageSync('username', username);
      Taro.setStorageSync('password', password);
    }
  }

  async loginSuccess(res) {
    app.globalData.isPharmacist = false; // 先置空
    this.cacheUserAccount()
    this.setState({userType: res.userType});
    if (
      res.changePassword &&
      res.userType != 'pharmacist_platform' &&
      res.userType != 'net_nurse_platform'
    ) {
      this.setState({isRe: false});
      app.globalData.inspectNurseInfo = {
        dept: res.dept,
        name: res.name,
        image: res.image
      };
    }
  }
  

  async change(param) {
    Taro.showLoading({title: '加载中', mask: true});
    const info = await Api.changeNewPassWord(param);
    if (info.code == 0) {
      Taro.hideLoading();
      Taro.setStorageSync('password', param.newPassword);
      this.cacheUserAccount()
      await this.getUserInfo()
      const url = `/pages/patient/index`;
      Taro.switchTab({url});
      const flag = true
      if (flag) return
      const { userType } = this.state;
      if (userType == 'nurse_platform') {
        Taro.reLaunch({url: '/pages/nurse/home'});
      } else if (userType == 'inspect_nurse_platform') {
        // app.globalData.inspectNurseInfo = {
        //   dept: this.dept,
        //   name: this.name,
        //   image: this.image,
        // }
        Taro.reLaunch({url: '/pages/nurseorder/news'});
      } else if (userType == 'pharmacist_platform') {
        Taro.reLaunch({url: '/pages/apothecary/home'});
      } else if (userType == 'net_nurse_platform') {
        Taro.reLaunch({url: '/pages/netnurse/orderlist/index'});
      } else {
        // Taro.switchTab({ url: '/pages/msg/msg' });
        if (_config.prescribe.signWay == '') {
          this.checkTrustSignPwd(); // 信任度方案
        } else if (_config.prescribe.signWay == 'mediSign') {
          this.checkMediSignPwd(); // 医信签方案
        }
      }
    }
  }

  async checkMediSignPwd() {
    Taro.showLoading({
      title: '加载中...', //提示的内容,
      mask: true //显示透明蒙层，防止触摸穿透,
    });

    const params = {doctorId: JSON.parse(Taro.getStorageSync('userInfo') || '{}').userId};
    const {code, data} = await Api.checkMediSignPwd(params);
    Taro.hideLoading();

    if (code == 0 && data && data != '') {
      app.globalData.mediSigning = true;
      // 需要设置签名
      const url = `plugin://Signplug-in/collectSign?code=${data}`;
      Taro.navigateTo({url});
    } else if (code == 1) {
      const url = `/pages/patient/index`;
      Taro.switchTab({url});
    } else if (code == 0 && (!data || data == '' || data == null)) {
      Taro.showToast({
        title: '账号未同步至CA',
        icon: 'none',
        duration: 2000
      });
    } else {
      Taro.showToast({
        title: '采集签名失败',
        icon: 'none',
        duration: 2000
      });
    }
    // this.$apply();
  }

  async checkTrustSignPwd() {
    const url = `/pages/patient/index`;
    Taro.switchTab({url});
  }

  async reLaunchUrl() {
    let url = {
      normal: `/pages/netnurse/orderlist/index`,
      abnormal: `/pages/netnurse/usercenter/index`
    };
    let status = await this.getNetNurseStatus();
    if (status == 1) { // 账号正常使用
      return url.normal;
    } else {
      return url.abnormal;
    }
  }

  async getNetNurseStatus() {
    const mobile = Taro.getStorageSync('account');
    Taro.showLoading({title: '加载中', mask: false,});
    const {code, data = {}} = await Api.getNurse({mobile});
    Taro.hideLoading();
    if (code == 0) {
      return data.status;
    }
  }

  showError(title, duration = 1500) {
    Taro.showToast({ title, icon: 'none', duration });
  }

  handleSwitchLoginType() {
    this.setState(state => ({
      loginType: (++state.loginType) % 2
    }))
  }

  /** type 0: 登录 1: 发送验证码 */
  async validate(type = 0) {
    const { loginType, phone, code, doctorSn, password } = this.state
    if (type === 1) {
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        this.showError('请输入正确的手机号')
        return Promise.reject()
      }
      return Promise.resolve()
    }
    if (loginType === 0) {
      if (!phone || !code) {
        this.showError('请输入手机号和验证码再登录！')
        return Promise.reject()
      }
      return Promise.resolve()
    }
    if (!doctorSn || !password) {
      this.showError('请输入账号密码后再登录！')
      return Promise.reject()
    }
    return Promise.resolve()
  }

  async handleTapTimer() {
    await this.validate(1)
    if (this.state.timer) return
    const { code } = await Api.sendMsgCode({ phone: this.state.phone })
    if (code !== 0) return
    const timer = setInterval(() => {
      const timerDuration = this.state.timerDuration - 1
      this.setState({ timerDuration })
      if (timerDuration <= 0) {
        clearInterval(this.state.timer)
        this.setState({
          timerDuration: TIMER_DURATION,
          timer: null,
        })
      }
    }, 1000)
    this.setState({ timer })
  }

  async handleLogin() {
    Taro.showLoading({ title: '加载中', mask: true });
    try {
      await this.validate()
      const { loginType, doctorSn: username, password, phone, code } = this.state
      const api = loginType === 0 ? Api.loginByPhone : Api.login
      const params = loginType === 0 ? { phone, code, } : { username, password, }
      const res = await api(params)
      if (res.code !== 0) return
      await this.getUserInfo()
      this.loginSuccess(res.data)
      Taro.reLaunch({ url: '/pages/patient/index' })
    } finally {
      Taro.hideLoading();
    }
  }

  aggreeChange(e) {
    const value = e.detail.value
    this.setState({ aggree: value && value.length })
  }
  aggree1(e) {
    e.stopPropagation()
    // Taro.navigateTo({ url: `/package1/webview/index?url=${encodeURIComponent(`${$DOMAIN}/yhfwxx.pdf`)}&title=用户服务协议` })
    openFile(`${$DOMAIN}/yhfwxx.pdf`)
  }
  aggree2(e) {
    e.stopPropagation()
    // Taro.navigateTo({ url: `/package1/webview/index?url=${encodeURIComponent(`${$DOMAIN}/xcxysxy.pdf`)}&title=隐私政策` })
    openFile(`${$DOMAIN}/xcxysxy.pdf`)
  }

  regist = () => {
    Taro.navigateTo({ url: `/package1/regist/index` })
  }


  render() {
    const {canLogin, isRe, newPassword, newCheckPassword, aggree} = this.state;
    return (
      <View className={s.container}>
        <View className={s.loginPageNotice}>请使用管理员给您的账号密码登录或通过手机验证码登录。</View>
        <View className={s.header}>
          <Image src={logoPng} />
          <View>{_config.name}</View>
        </View>
        {
          isRe ?
            <Block>
              {this.loginType === 1 ?
              <Block>
                <View className={s.inputItem}>
                  <Input placeholder='请输入您的账号' onInput={e => this.changeInput(e, 'doctorSn')} />
                </View>
                <View className={s.inputItem}>
                  <Input password placeholder='请输入密码' onInput={e => this.changeInput(e, 'password')} />
                </View>
              </Block> :
              <Block>
                <View className={s.inputItem}>
                  <Input placeholder='请输入您的手机号' type='number' maxLength={11} onInput={e => this.changeInput(e, 'phone')} />
                </View>
                <View className={[s.inputItem, 'f-row', 'f-c-center']}>
                  <Input className='f-1' type='number' maxLength={6} placeholder='请输入验证码' onInput={e => this.changeInput(e, 'code')} />
                  <View className={s.inputItemSplit}></View>
                    { !this.state.timer ? <View className={s.inputItemCode} onClick={this.handleTapTimer}>发送验证码</View> :
                    <View className={s.inputItemCode}>({ this.state.timerDuration })s</View> }
                  </View>
              </Block>}
              <View className={s.loginPageFooter}>
                <CheckboxGroup onChange={this.aggreeChange}>
                  <Checkbox value='1' checked={aggree} className={s.aggree}>
                    已阅读并同意
                    <Text className={s.aggree_link} onClick={this.aggree1}>《用户服务协议》</Text>及
                    <Text  className={s.aggree_link} onClick={this.aggree2}>《隐私政策》</Text>
                  </Checkbox>
                </CheckboxGroup>
                <Button
                  openType='getUserInfo'
                  className={[s.btn, s.loginBtn, (!canLogin || !aggree) && s.disabled]}
                  onGetUserInfo={this.handleLogin}
                  disabled={(!canLogin || !aggree)}
                >
                  登 录
                </Button>
                {/* <Button className={[s.btn, s.switchBtn]} onClick={this.handleSwitchLoginType}>{ this.state.loginType === 1 ? '手机号登录' : '账号密码登录' }</Button> */}
              </View>
              <View className={s.register} onClick={this.regist}>申请注册账号</View>
              <View className={s.rePwd}>忘记密码？请<Text>联系管理员</Text>进行重置操作</View>
            </Block>
            :
            <Block>
              <View className={s.inputItem}>
                {/* <Image src={`${$CDN_DOMAIN}/ih-miniapp-doc/pwd.png`} /> */}
                <Input
                  password
                  placeholder='请输入新密码'
                  value={newPassword}
                  onInput={this.changeNPwd}
                  maxLength='18'
                />
              </View>
              <View className={s.inputItem}>
                {/* <Image src={`${$CDN_DOMAIN}/ih-miniapp-doc/pwd.png`} /> */}
                <Input
                  password
                  placeholder='请再次输入新密码'
                  value={newCheckPassword}
                  onInput={this.checkNewPwd}
                  maxLength='18'
                />
              </View>
              <View className={s.rePwd}>请输入密码，8~18位字母、数字、特殊字符组合。</View>
              <View className={s.loginPageFooter}>
                <Button className={s.loginBtn} onClick={this.rePwd}>
                  确 认
                </Button>
              </View>
            </Block>
        }
      </View>
    );
  }
}
