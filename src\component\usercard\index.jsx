import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  render () {
    const { patient = {}, extra = [], switchTitle = '切换就诊人', onSwitch = () => {}, titleKey = '' } = this.props;
    return (
      <View className='wgt-user-box'>
        <View className='wgt-user-main'>
          <View className='wgt-user-main-info'>
            <View className='wgt-user-main-info-tit'>{patient[titleKey]}</View>
            <View className='wgt-user-main-info-label'></View>
          </View>
          <View className='wgt-user-main-btn'>
            <View className='wgt-user-main-btn-label' onClick={onSwitch}>{switchTitle}</View>
            <View className='wgt-user-main-btn-arrow'></View>
          </View>
        </View>
        {
          extra.map((item) => {
            return (
              patient[item.key] ?
                <View className='wgt-user-extra' key={item.key}>{item.value}：{patient[item.key]}</View>
                : null
            );
          })
        }
      </View>
    )
  }
}
