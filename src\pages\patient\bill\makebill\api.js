import { post, thirdPost } from '@/utils/request';

export const getProductList = (param) => post('/api/customize/getProductList?_route=h242&k', param);

export const saveOrder = (param) => post('/api/ext/saveorder', param);

export const getPatientInfoByPid = (param) => post('/api/consultation/getPatientInfoByPid', param);

export const prePayOrder = (param) => post('/api/ext/extpayorder', param);

export const cashier = (param) => thirdPost(`${$PAY_DOMAIN}/api/pay/cashier`, param);

// 选择支付方式
export const choosePayMode = (param) => thirdPost(`${$PAY_DOMAIN}/api/pay/choosepaymode`, param);
