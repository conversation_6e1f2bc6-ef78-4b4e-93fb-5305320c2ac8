import Taro, { Component } from '@tarojs/taro'
import { View, Button, Text, Checkbox, CheckboxGroup, Switch, Radio, RadioGroup } from '@tarojs/components'
import { AtIcon } from 'taro-ui'
import Empty from '@/component/empty'
import s from './index.module.scss'
import * as API from './api'


export default class Address extends Component {

    constructor(props) {
        super(props)
        this.state = {
            list: [],
            leftNum: 0,
            mode: ''
        }
    }

    componentWillMount() {
        //是否选择地址模式
        const { mode } = this.$router.params
        if (mode)
            this.setState({
                mode: mode
            })
        this.getList()
    }

    componentDidShow() {
        this.getList()
    }

    config = {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTitleText: '地址簿',
        navigationBarTextStyle: 'black'
    };

    async getList() {
        const { code, data } = await API.getAddressList()
        if (code !== 0) return
        this.setState({ list: data.addressList || [], leftNum: data.leftNum })
    }

    // handleSetDefault = async (id) => {
    //     console.log(id)
    //     const { code } = await API.setDefaultAddress({ id })
    //     if (code !== 0) return
    //     this.getList()
    // }

    handleSetDefault = async (e) => {
        console.log(e)
        const { code } = await API.setDefaultAddress({ id: e.detail.value })
        if (code !== 0) return
        this.getList()
    }

    goToDetail(id) {
        Taro.navigateTo({ url: `/package1/address/edit/index?id=${id}` });
    }

    handleDelete = (id) => {
        Taro.showModal({
            title: '提示',
            content: '确认删除地址？',
            confirmColor: '#30A1A6',
            success: res => {
                if (res.confirm) {
                    this.deleteAddress(id)
                }
            }
        })
    }

    async deleteAddress(id) {
        const { code } = await API.delAddress({ id })
        if (code !== 0) return
        this.getList()
        Taro.showToast({ title: '删除成功', icon: 'none' })
    }

    /**
     * 选择地址，返回上一页
     * @param {*} obj 
     */
    selectAddress = (obj) => {
        let pages = Taro.getCurrentPages(); // 获取当前的页面栈 
        let prevPage = pages[pages.length - 2]; // 获取上一页面
        prevPage.setData({
            currentAddress: obj
        });
        Taro.navigateBack();
    }

    render() {
        const { list, leftNum, mode } = this.state
        return (
            <View className={s.page}>
                <RadioGroup onChange={(e) => this.handleSetDefault(e)}>
                    {list.map(v => (
                        <View key={v.id} className={s.list_item}>
                            {
                                mode === 'choose' ?
                                    <View className={s.list_item_info} onClick={() => this.selectAddress(v)}>
                                        <View className={['flex', 'f-c-center']}>
                                            <View className={s.list_item_name}>{v.userName}</View>
                                            <View>{v.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}</View>
                                        </View>
                                        <View>{v.address}</View>
                                    </View>
                                    : <View className={s.list_item_info} >
                                        <View className={['flex', 'f-c-center']}>
                                            <View className={s.list_item_name}>{v.userName}</View>
                                            <View>{v.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}</View>
                                        </View>
                                        <View>{v.address}</View>
                                    </View>
                            }
                            <View className={s.list_item_oper}>
                                <View>
                                    {/* <CheckboxGroup onChange={() => this.handleSetDefault(v.id)}>
                                    <Checkbox value={v.isDefault} checked={v.isDefault === 1 ? true : false} color='#3F969D' className={s.list_item_checkbox}></Checkbox>
                                </CheckboxGroup> */}
                                    {/* <Switch color='#3F969D' disabled={v.isDefault === 1 ? true : false} className={s.list_item_switch} checked={v.isDefault === 1 ? true : false} onChange={() => this.handleSetDefault(v.id)}></Switch> */}
                                    <Radio color='#3F969D' className={s.list_item_switch} checked={v.isDefault === 1 ? true : false} value={v.id}></Radio>
                                    <Text style='margin-left:-16rpx'>默认寄件地址</Text>
                                </View>
                                <View className='flex' style='color:#666'>
                                    <View className={s.list_item_btn} onClick={() => this.goToDetail(v.id)}>
                                        <AtIcon value='edit' size='15' className={s.list_item_icon}></AtIcon>
                                        <View>编辑</View>
                                    </View>
                                    <View className={s.list_item_btn} onClick={() => this.handleDelete(v.id)}>
                                        <AtIcon value='trash' size='15' className={s.list_item_icon}></AtIcon>
                                        <View>删除</View>
                                    </View>
                                </View>
                            </View>
                        </View>
                    ))}
                </RadioGroup>
                {
                    (!list || !list.length) ? <Empty text='暂无地址' /> : null
                }
                {
                    leftNum > 0 ? <View className={s.bottom}>
                        <Button className={s.bottom_btn} onClick={() => this.goToDetail(0)}>新增地址</Button>
                    </View> : null
                }

            </View>
        )
    }
}