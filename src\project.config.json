{"miniprogramRoot": "", "projectname": "%E4%B8%AD%E4%BF%A1%E6%B9%98%E9%9B%85%E7%94%9F%E6%AE%96%E5%8C%BB%E9%99%A2", "srcMiniprogramRoot": "./src", "description": "湖南家辉遗传专科医院", "appid": "wx1284eebebb89f7d0", "setting": {"urlCheck": false, "es6": false, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "compileHotReLoad": false, "useIsolateContext": true, "packNpmManually": false, "packNpmRelationList": [], "compileWorklet": false, "uglifyFileName": false, "enhance": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "plugin": {"current": -1, "list": []}, "game": {"list": []}, "gamePlugin": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": [{"id": -1, "name": "pages/patient/index", "pathName": "pages/patient/index", "query": "", "scene": null}, {"id": -1, "name": "pages/index/index", "pathName": "pages/index/index", "query": "", "scene": null}, {"id": 2, "name": "pages/consultation-opinion/consultation-report/index", "pathName": "pages/consultation-opinion/consultation-report/index", "query": "consultationId=44", "scene": null}, {"id": -1, "name": "pages/personal-center/record-detail/index", "pathName": "pages/personal-center/record-detail/index", "query": "", "scene": null}, {"id": 4, "name": "pages/consultation/chat/index", "pathName": "pages/consultation/chat/index", "query": "id=717806609161519104&isEndFeeType=0&chatType=1&patName=张三", "scene": null}, {"id": -1, "name": "pages/patient/health-record/visiting-record/consultation-application/index", "pathName": "pages/patient/health-record/visiting-record/consultation-application/index", "query": "patientPid=10140540", "scene": null}]}}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}