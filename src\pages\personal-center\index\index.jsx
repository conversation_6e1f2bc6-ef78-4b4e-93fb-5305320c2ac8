import Taro, { Component } from '@tarojs/taro';
import { Button, Image, Text, View } from '@tarojs/components';
import Modal from '@/component/modal'
import Chat from '@/component/chat'
import DocHheaderMan from '@/static/image/avatar.png'
import arrowRightPng from '@/resources/images/arrow-right.png'
import qrcodePng from '@/static/image/qrcode.png'
import QrcodeModal from '../qrcode/index'
import s from './index.module.scss';
import * as Api from './api';
import { openFile } from '../../../utils/utils';

export default class Index extends Component {

  constructor(props) {
    super(props);
    this.state = {
      userInfo: null,
      modalShow: false,
      toolModalShow: false,
      personal: true,
      permissions: {} // 权限配置
    };
  }

  componentWillMount() {
  }

  componentDidMount() {
    const userInfo = Taro.getStorageSync('userInfo');
    userInfo && this.setState({ userInfo: JSON.parse(userInfo) });
  }

  componentWillUnmount() {
  }

  componentDidShow() {
    // 获取用户配置
    this.getUserConfig();
  }

  componentDidHide() {
  }
  
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#F6F7F9',
    navigationBarTitleText: '个人中心',
    navigationBarTextStyle: 'black'
  };

  // 获取用户配置
  getUserConfig = async () => {
    try {
      Taro.showLoading({ title: '加载中' });
      const res = await Api.getUserConfig();
      Taro.hideLoading();
      
      let permissions = {};
      
      if (res && res.code === 0 && res.data) {
        // 解析权限字符串
        if (res.data.permission) {
          try {
            permissions = JSON.parse(res.data.permission);
            console.log('权限配置:', permissions);
          } catch (error) {
            console.error('权限解析失败:', error);
            // 解析失败时设置为空对象，会显示所有菜单
            permissions = {};
          }
        } else {
          console.log('权限字段为空，显示所有菜单');
          // 权限字段为空，设置为空对象，会显示所有菜单
          permissions = {};
        }
      } else {
        console.log('获取用户配置失败或返回数据异常，显示所有菜单');
        // 接口请求失败或返回异常，设置为空对象，会显示所有菜单
        permissions = {};
      }
      
      this.setState({ permissions });
    } catch (error) {
      Taro.hideLoading();
      console.error('获取用户配置失败:', error);
      // 异常时设置为空对象，会显示所有菜单
      this.setState({ permissions: {} });
    }
  }

  // 检查权限
  hasPermission = (permKey) => {
    const { permissions } = this.state;
    // 如果permissions为空对象或permission字段为空，所有菜单都显示
    if (!permissions || Object.keys(permissions).length === 0) {
      return true;
    }
    return permissions[permKey] === '1';
  }

  jumpToRecord(url) {
    Taro.navigateTo({ url: url });
  }

  logOut = async () => {
    Taro.showModal({
      title: '温馨提示',
      content: '确认退出登录？',
      success: async function (res) {
        if (res.confirm) {
          Taro.showLoading({ title: '加载中', mask: false, });
          const change = await Api.logout();
          if (change.code === 0) {
            Taro.hideLoading();
            Taro.clearStorageSync();
            await Taro.redirectTo({ url: '/pages/index/index' });
          }
        }
      }
    });
  };

  setModalShow = (modalShow) => {
    this.setState({ modalShow })
  }

  test = () => {
    Taro.navigateTo({ url: `/pages/sample-upload/index?scene=SA435345432` })
  }

  aggree1(e) {
    e.stopPropagation()
    // Taro.navigateTo({ url: `/package1/webview/index?url=${encodeURIComponent(`${$DOMAIN}/yhfwxx.pdf`)}&title=用户服务协议` })
    openFile(`${$DOMAIN}/yhfwxx.pdf`)
  }
  aggree2(e) {
    e.stopPropagation()
    // Taro.navigateTo({ url: `/package1/webview/index?url=${encodeURIComponent(`${$DOMAIN}/xcxysxy.pdf`)}&title=隐私政策` })
    openFile(`${$DOMAIN}/xcxysxy.pdf`)
  }

  getToolUrl = () => `https://yc.jiahuiyiyuan.com/familyTree/drawImage/index.jsp?timestamp=${Date.now()}`

  openTool = () => {
    const url = this.getToolUrl()
    Taro.navigateTo({
      url: `/package1/webview/index?url=${encodeURIComponent(url)}&title=家系图绘制工具`
    })
  }


  render() {
    const { userInfo, modalShow, toolModalShow, personal } = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={`${s.card} flex f-m-between`}>
          <Image className={s.avatar} src={userInfo.accountImg || DocHheaderMan} />
          <View className='f-1 flex f-col f-m-between'>
            <View className='flex f-m-between'>
              <View>
                <Text className={s.doctorName}>{userInfo.userName}</Text>
              </View>
            </View>
            <View className={s.header_line}>
              {userInfo.allianceName}
            </View>
            <View className={s.header_line}>{userInfo.institutionName}</View>
          </View>
          <View className={[s.card_right, 'f-col', 'f-m-center']}>
            <View className={s.box} onClick={() => this.setModalShow(true)}>
              <Image className={s.card_right_image} src={qrcodePng} />
              <Text className={s.code_name}>推广二维码</Text>
            </View>
          </View>
        </View>

        {/* <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/personal-center/record-list/index')}>
          <Image src={recordPng} />
          会诊记录
        </View> */}
        <View className={s.page_body}>
          
          {/* 新增会诊 - 需要xzhz权限 */}
          {this.hasPermission('xzhz') && (
            <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/patient/health-record/visiting-record/consultation-application/index')}>
              新增会诊
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          
          {/* 会诊记录 - 需要xzhz权限 */}
          {this.hasPermission('xzhz') && (
            <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/personal-center/record-list/index')}>
              会诊记录
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          
          {/* 开单记录 - 需要kdjl权限 */}
          {this.hasPermission('kdjl') && (
            <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/patient/bill/makebill-list/index')}>
              开单记录
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          
          {/* 家系图绘制工具 - 需要jxthjgj权限 */}
          {this.hasPermission('jxthjgj') && (
            <View className={s.record} onClick={() => this.setState({ toolModalShow: true })}>
              家系图绘制工具
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          
          {/* 活动报名记录 - 需要hdbmjl权限 */}
          {this.hasPermission('hdbmjl') && (
            <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/classroom/records/index')}>
              活动报名记录
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          
          {/* 修改登录密码 - 无需权限控制 */}
          <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/personal-center/update-pass/index')}>
            修改登录密码
            <Image className={s.record_arrow} src={arrowRightPng} />
          </View>

          {/* 样本录入 - 需要yblr权限 */}
          {this.hasPermission('yblr') && (
            <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/sample/index')}>
              样本录入
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          
          {/* 样本寄送和记录 - 需要ybcx权限 */}
          {this.hasPermission('ybcx') && (
            <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/sample/send-out/index')}>
              样本寄送
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          
          {this.hasPermission('ybcx') && (
            <View className={s.record} onClick={this.jumpToRecord.bind(this, '/pages/sample/send-list/index')}>
              样本寄送记录
              <Image className={s.record_arrow} src={arrowRightPng} />
            </View>
          )}
          {/* 地址簿 - 无需权限控制 */}
          <View className={s.record} onClick={this.jumpToRecord.bind(this, '/package1/address/index')}>
            地址簿
            <Image className={s.record_arrow} src={arrowRightPng} />
          </View>
          
          {/* 用户服务协议 - 无需权限控制 */}
          <View className={s.record} onClick={this.aggree1}>
            用户服务协议
            <Image className={s.record_arrow} src={arrowRightPng} />
          </View>
          
          {/* 隐私政策 - 无需权限控制 */}
          <View className={s.record} onClick={this.aggree2}>
            隐私政策
            <Image className={s.record_arrow} src={arrowRightPng} />
          </View>
        </View>

        <View>
          <Button className={s.logoutBtn} onClick={this.logOut}>退出登录</Button>
        </View>

        <QrcodeModal modalShow={modalShow} setModalShow={this.setModalShow} isPersonal={personal} />
        <Modal
          show={toolModalShow}
          title='家系图绘制工具'
          confirmText='打开'
          onCancel={() => this.setState({ toolModalShow: false })}
          onOk={this.openTool}
        >
          <View className={s.tool_modal}>
            <View className={s.tool_tips}>您可以直接打开体验，也可以点击复制获取链接在电脑浏览器上打开完整使用。复制链接有时效性，失效后请重新获取</View>
            <View
              className={s.tool_btn}
              onClick={() => {
                Taro.setClipboardData({ 
                  data: this.getToolUrl(),
                  success: () => {
                    Taro.showToast({ title: '链接已复制', icon: 'success' });
                  }
                });
              }}
            >复制链接</View>
          </View>
        </Modal>

        <Chat />
      </View>
    );
  }
}
