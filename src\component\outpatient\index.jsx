import Taro, { Component } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import * as Api from './api';
import styles from './index.module.scss';

export default class Index extends Component {
  
  constructor(props) {
    super(props);
    this.state = {
      patient: {
        cardList: [],
        activePatient: {},
      },
      show: false
    }
  }

  componentWillMount () {}

  componentDidMount () { }

  componentWillUnmount () { }

  componentDidShow () {
    this.getOutpatient();
  }

  componentDidHide () { }

  getOutpatient = async () => {
    this.setState({ patient: {cardList: [], activePatient: {}} });
    const { data = {} } = await Api.getOutpatient();
    
    const { cardList = [] } = data;

    if (!cardList.length) {
      Taro.showModal({
        title: '提示',
        content: '您还没有绑定任何就诊人',
        cancelText: '取消',
        confirmText: '去绑定',
        cancelColor: '#989898',
        confirmColor: $PRIMARY_COLOR,
        success: (pram) => {
          if(pram.cancel) {
            Taro.reLaunch({
              url: '/pages/home/<USER>'
            });
          } else if(pram.confirm) {
            Taro.navigateTo({
              url: '/pages/usercenter/bindscan/index'
            });
          }
        },
      });
      return false;
    }

    const activePatient = cardList.length > 0 ? cardList.filter(item => item.isDefault === 1)[0] : {}
    const patient = { ...data, activePatient };
    this.setState({ 
      patient
    });
    
    const { changeUser = () => {} } = this.props;
    changeUser(activePatient);
  }

  toggleShow = () => {
    const { show } = this.state;
    this.setState({ show: !show })
  }

  localChangeUser = async (item) => {
    const { patient: { activePatient = {} } } = this.state;
    if (item.patientId === activePatient.patientId) {
      return;
    }
    const { code } = await Api.setDefaultOutpatient(item);
    if (code !== 0) {
      return false;
    }
    
    this.getOutpatient();
  }

  render () {
    const { patient = {}, show } = this.state;

    const { cardList = [] } = patient;

    return (
      <View className={styles.wgtUserBox}>
        <View className={styles.wgtUserMain}>
          <View className={styles.wgtUserMainInfo}>
            <View className={styles.wgtUserMainInfoTxt}>{patient.activePatient.patientName}</View>
            <View className={styles.wgtUserMainInfoLabel}></View>
          </View>
          <View className={styles.wgtUserMainBtn} onClick={() => this.toggleShow()}>切换就诊人</View>
        </View>
        <View className={styles.wgtUserExtra}>
          {patient.activePatient.healthCardFlag == 2 ? '电子健康卡' : '就诊卡'}：{patient.activePatient.patCardNo}
        </View>
        <View className={`${styles.wgtUserPopBox} ${show ? styles.active : ''}`}>
          <View className={styles.wgtUserPopMask} onClick={() => this.toggleShow()}></View>
          <View className={styles.wgtUserPop}>
            <View className={styles.wgtUserPopTitle}>切换就诊人</View>
            <View className={styles.wgtUserPopList}>
              {
                cardList.map(item => {
                  return (
                    <View className={styles.wgtUserPopListItem} key={item.patientId} onClick={() => this.localChangeUser(item)}>
                      <View className={styles.wgtUserPopListItemMain}>
                        <View className={styles.wgtUserPopListItemName}>{item.patientName}</View>
                        <View className={styles.wgtUserPopListItemLabel}></View>
                      </View>
                      <View className={styles.wgtUserPopListItemNum}>
                        {item.healthCardFlag == 2 ? '电子健康卡' : '就诊卡'}：{item.patCardNo}</View>
                      <View
                        className={`${styles.wgtUserPopListItemIpt} ${item.patientId === patient.activePatient.patientId ? styles.active : ''}`}
                      >
                        <Image className={styles.image} src={`${$CDN_DOMAIN}/select-single.png`}></Image>
                      </View>
                    </View>
                  )
                })
              }
            </View>
            <View className={styles.wgtUserPopOpt}>
              {
                patient.leftBindNum > 0 ?
                  <View
                    className={styles.wgtUserPopOptItem}
                    onClick={() => this.navTo('/pages/usercenter/bindscan/index')}
                  >
                    添加就诊人
                  </View> : null
              }
              <View
                className={styles.wgtUserPopOptItem}
                onClick={() => this.navTo('/pages/usercenter/userlist/index')}
              >
                管理就诊人
              </View>
            </View>
            <View className={styles.wgtUserPopClose} onClick={() => this.toggleShow()}></View>
          </View>
        </View>
      </View>
    )
  }
}
