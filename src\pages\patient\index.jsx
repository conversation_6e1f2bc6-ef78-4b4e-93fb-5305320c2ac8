import Taro, { Component } from '@tarojs/taro';
import { Image, Input, Text, View } from '@tarojs/components';
import Dayjs from 'dayjs';
import Empty from '@/component/empty'
import Chat from '@/component/chat'
import QrcodeModal from '@/pages/personal-center/qrcode'
import s from './index.module.scss';
import * as Api from './api';

export default class Index extends Component {

  constructor(props) {
    super(props);
    this.state = {
      doctorId: '',
      patientList: [],
      searchValue: '',
      // searchFocus: false,
      modalShow: false,
      isMounted: false,
      codeFrom: '',
      isInvited: ''
    };
  }

  componentDidMount() {
    const {userId: doctorId = ''} = JSON.parse(Taro.getStorageSync('userInfo') || '{}');
    this.setState({ doctorId, isMounted: true }, this.getPatientList);
  }

  componentWillUnmount() {
  }

  componentDidShow() {
    // 下次进来需要能刷新患者列表
    const { isMounted } = this.state
    isMounted && this.getPatientList();
  }

  componentDidHide() {
  }
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '我的患者',
    navigationBarTextStyle: 'black'
  };

  getPatientList = async () => {
    const {doctorId} = this.state;
    let param = {
      pageNum: 1,
      numPerPage: 10,
      doctorId
    }

    const { code, data } = await Api.queryPatientList(param);
    if(code == 0){
      const { recordList } = data;
        this.setState({
          patientList: recordList
        })
    }
  }

  bindSearchInput = (e) => {
    const { value } = e.detail;
    this.setState({ searchValue: value });

    return value;
  }

  handleSearch = async () => {
    const { searchValue, doctorId } = this.state;
    let param = {
      pageNum: 1,
      numPerPage: 10,
      doctorId,
      extFields: searchValue,
    }
    const { code, data } = await Api.queryPatientList(param);
    if (code != 0 ) {
      // 搜索结果与当前输入框不等，数据不录入
      return false;
    }
    const { recordList } = data;
    this.setState({ patientList:recordList });
  }

  toInfoPage = (data) => {
    Taro.navigateTo({ url: `/pages/patient/health-record-v2/index?patientPid=${data.patientId}&pid=${data.pid}`})
  }

  toMakeBillPage = () => {
    Taro.navigateTo({ url: `/pages/patient/bill/makebill/index`})
  }

  setModalShow = (modalShow) => {
    this.setState({ 
      modalShow,
    })
  }

  render() {
    const {
      patientList = [],
      modalShow = false,
      codeFrom = '',
      isInvited = ''
    } = this.state;
    return (
      <View className={`${s.patientPage}`}>
        <View className={[s.patientPageHeader, 'f-row', 'f-c-center']}>
          <View className={[s.patientPageHeaderItem, 'f-1', 'f-row', 'f-center']} onClick={() => {this.setModalShow(true); this.setState({ 
            codeFrom: 'doctor',
            isInvited: ''
          }) }}
          >
            <Image className={s.patientPageHeaderItemIcon} src={require('@/resources/images/patient/add.png')} />
            <Text>邀请患者</Text>
          </View>
          <View className={s.patientPageHeaderSplit}></View>
          <View className={[s.patientPageHeaderItem, 'f-1', 'f-row', 'f-center']} onClick={() => {this.setModalShow(true); this.setState({ 
            codeFrom: '',
            isInvited: '开单缴费'
          }) }}
          >
            <Image className={s.patientPageHeaderItemIcon} src={require('@/resources/images/patient/list.png')} />
            <Text>开单缴费</Text>
          </View>
        </View>

        <View className={[s.patientPageBody]}>

          <View className={[s.search, 'f-center']}>
            <icon style='margin-right: 24px;' type='search' size='16' color='#30A1A6' />
            <Input
              className='f-1'
              placeholder={modalShow ? '' : '请输入PID或姓名搜索'}
              onInput={this.bindSearchInput}
              onConfirm={this.handleSearch}
            />
          </View>
          {
            patientList.length && (
              patientList.map((patient, index) => {
                return (
                  <View className={s.patient} key={index} onClick={() => this.toInfoPage(patient)}>
                    <Image mode='widthFix' src={patient.headImage} />
                    <View className={`${s.rowModule}`}>
                      <View className={`${s.rowTop}`}>
                        <Text className={`${s.name}`} >{patient.patientName}</Text>
                        <Text className={`${s.tagInfo}`}>{Dayjs(patient.createTime).format('YYYY-MM-DD')}</Text>
                      </View>
                      <Text>{patient.patientSex == 'M' ?'男':'女'}</Text>
                      <Text>{patient.patientAge}岁</Text>
                      <Text>PID:{patient.pid}</Text>
                    </View>
                  </View>
                )
              })
            )
          }

          {
            (!patientList || !patientList.length) ? <Empty text='当前没有关联患者' /> : null
          }
        </View>
        <QrcodeModal modalShow={modalShow} setModalShow={this.setModalShow} from={codeFrom} isInvited={isInvited} />
        <Chat />
      </View>
    );
  }
}
