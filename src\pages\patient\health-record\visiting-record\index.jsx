import Taro, { Component } from '@tarojs/taro';
import { Picker, View } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import { TimeAxis } from './time-axis/index';


export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '健康档案',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);
    this.state = {
      status: true,
      recordList: [],
      rangeList: [
        { key: this.getFormatTime(new Date().setMonth(new Date().getMonth() - 3)), value: '三个月' },
        { key: this.getFormatTime(new Date().setMonth(new Date().getMonth() - 6)), value: '六个月' },
        { key: this.getFormatTime(new Date().setFullYear(new Date().getFullYear() - 1)), value: '一年' },
        { key: this.getFormatTime(new Date().setFullYear(new Date().getFullYear() - 2)), value: '两年' },
      ],
      rangeListIndex: 0,
      endTime: this.getFormatTime(new Date()),
    };
  }

  componentDidShow() {
    this.getConsultationRecord();
  }

  componentDidMount() {
    this.getConsultationRecord();
  }

  getFormatTime(time) {
    const date = new Date(time);
    let fmt = 'yyyy-MM-dd hh:mm:ss';
    var o = {
      "M+": date.getMonth() + 1,                 //月份
      "d+": date.getDate(),                    //日
      "h+": date.getHours(),                   //小时
      "m+": date.getMinutes(),                 //分
      "s+": date.getSeconds(),                 //秒
      "q+": Math.floor((date.getMonth() + 3) / 3), //季度
      "S": date.getMilliseconds()             //毫秒
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (let k in o)
      if (new RegExp("(" + k + ")").test(fmt))
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
  }

  getConsultationRecord = async () => {
    const { endTime, rangeList, rangeListIndex } = this.state;
    const { patientPid: pid } = this.props;
    const param = {
      pid,
      // startTime: rangeList[rangeListIndex].key,
      // endTime,
      type: 1
    };
    const { code, data = [], msg = '' } = await Api.getConsultationRecord(param);
    if (code == 0) {
      this.setState({
        recordList: data,
      })
    }
  }

  onChangeTopBar(index) {
    this.setState({ topbarSelected: index });
    this.changeTopBar(index);
  }

  changeTopBar(index) {
    const i = String(index || this.state.topbarSelected);
    this.setState({ activeIndex: i });
    switch (i) {
      case '0':
        break;
      case '1':
        break;
      default:
        break;
    }
  }

  addRecord = () => {
    Taro.navigateTo({ url: `/pages/patient/health-record/visiting-record/add-visiting-record/index?patientPid=${this.props.patientPid}` })
  }

  applyConsultation = () => {
    Taro.navigateTo({ url: `/pages/patient/health-record/visiting-record/consultation-application/index?patientPid=${this.props.patientPid}` })
  }

  selectRange = (e) => {
    this.setState({ rangeListIndex: e.detail.value }, this.getConsultationRecord);
  }

  render() {
    const {
      status,
      recordList,
      rangeList,
      rangeListIndex
    } = this.state;
    return (
      <View>
        {/* <View className={`${s.topModule}`}>
          <View className={`${s.selectRange}`}>
            <Picker mode='selector' range={rangeList} rangeKey='value' value={rangeListIndex} onChange={this.selectRange}>
              <View className={`${s.selectRange}`}>
                查询范围：<Text style={{ color: '#3ECEB6' }} >{rangeList[rangeListIndex].value} &gt;</Text>
              </View>
            </Picker>
          </View>
        </View> */}
        <View className={`${s.allRecord}`}>
          { recordList.length > 0 &&
            recordList.map((item, index) => {
              return (
                <TimeAxis key={index} status={status} item={item} />
              )
            })
          }
          {
            recordList.length <= 0 && 
            <View className={`${s.isNo}`}>暂无内容</View>
          }
        </View>
        <View className={`${s.buttomStyle}`}  >
          <View className={`${s.applyRecord}`} onClick={this.applyConsultation}>申请会诊</View>
          <View className={`${s.applyRecord}`} onClick={this.addRecord}>添加记录</View>
        </View>
      </View>
    );
  }
}
