.g-list{
  position: relative;
  flex: 1;
  -webkit-overflow-scrolling: touch;
}

.m-list{
  -webkit-overflow-scrolling: touch;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  &.active{
    display: block;
  }
  .list-box{
    display: flex;
    flex-direction: row;
    height: 100%;
  }
  .list-lt-box{
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    font-size: 30px;
    background-color: #f1f3f6;
  }
  .list-lt{
    position: relative;
    margin-bottom: 30px;
  }
  .lt-item{
    position: relative;
    max-width: 7em;
    min-width: 4em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 88px;
    line-height: 88px;
    padding: 0 45px 0 20px;
    color: $color-text;
    
    &.active{
      color: $color-title;
      background-color: #fff;
      z-index: 2;
    }
  }
  .list-rt-box{
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;
    font-size: 30px;
  }
  .list-rt{
    position: relative;
    overflow-x: hidden;
    padding-bottom: 30px;
  }
  .rt-history-box{
    display: none;
    &.active{
      display: block;
    }
  }
  .rt-history{
    padding-left: 30px;
    padding-bottom: 20px;
    
    .his-tit{
      padding: 20px 0;
      font-size: 30px;
      color: $color-primary;
    }
    .his-item{
      display: flex;
      padding: 11px 30px 11px 0;
      border-bottom:1px solid $color-border;
      align-items: center;
    }
    .item-hd{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 90px;
      border-radius: 6px;
      overflow: hidden;
      image{
        vertical-align: top;
      }
    }
    .item-bd{
      flex: 1;
      margin-left: 30px;
      
      .unit-color-title {
        color:$color-title;
        font-size: 30px;
      }
      
      .unit-color-text {
        color:$color-text;
        font-size: 28px;
        margin-top: 5px;
      }
    }
    .item-ft{
      
    }
  }
  .rt-sec{
    display: none;
    &.active{
      display: block;
    }
    .sec-li{
      display: block;
      padding-left: 30px;
      color: $color-title;
    }
    .sec-li-wrap{
      display: flex;
      align-items: center;
      color: $color-title;
      border-bottom:1px solid $color-border;
      padding-right: 30px;
    }
    .sec-bd{
      flex: 1;
      line-height: 88px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .trd-arrow{
      font-size: 34px;
      line-height: 1;
      color: $color-primary;
      transition: transform .2s;
    }
    .trd-box{
      display: none;
      padding-left: 30px;
    }
    .trd-li{
      display: flex;
      align-items: center;
      line-height: 88px;
      padding-right: 30px;
      color: $color-title;
      border-bottom:1px solid $color-border;
    }
    .trd-bd{
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .sec-li.active{
      .trd-box{
        display: block;
      }
      .trd-arrow{
        transform: rotate(-180deg);
      }
    }
  }
}

.unit-arrow{
  width: 15px;
  height: 15px;
  border-right: 4px solid #C7C7CC;
  border-bottom: 4px solid #C7C7CC;
  transform: translateX(-8px) rotate(-45deg);
}

.m-suggest{
  background-color: #fff;
  padding: 20px 0;
  .suggest-title{
    color: $color-title;
    font-size: 28px;
    margin: 0 30px 0;
    position: relative;

    &:after{
      content: ' ';
      position: absolute;
      right: 0;
      top: 50%;
      transform-origin: center;
      width: 15px;
      height: 15px;
      border-right: 4px solid #C7C7CC;
      border-bottom: 4px solid #C7C7CC;
      transform: translateY(-50%) rotate(45deg);
    }
  }

  .m-tags {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    width: 10000px;

    .tag-item {
      font-size: 30px;
      color: $color-text;
      display: block;
      margin: 20px 0 0 30px;
      width: 150px;
      height: 60px;
      line-height: 60px;
      text-align: center;
      overflow: hidden;
      border: 1px solid $color-border;
      border-radius: 30px;
    }
  }

  &.tagopen{
    .suggest-title:after{
      transform: translateY(-50%) rotate(-135deg);
    }

    .m-tags{
      width: 750px;
      flex-wrap: wrap;
    }
  }
}