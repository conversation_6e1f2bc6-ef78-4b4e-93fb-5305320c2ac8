import Taro, { Component } from '@tarojs/taro'
import { View, Image, ScrollView, Text, Textarea, Button, RichText } from '@tarojs/components'
import * as Utils from "@/utils/utils";
import { uploadFile } from "@/utils/request";

import * as Api from './api'

import './style.scss'

export default class Chat extends Component {

  constructor(props) {
    super(props)
    this.state = {
      showTopTip: true,
      showTips: false, // 显示小提示
      list: [],
      vipCode: "",
      msgText: null,
      isEnd: false,
      showPlus: false,
      groupId: "",
      showId: "",
      scrollTime: "",
      robotMessageList: [], // 智能提问列表
      isInputFocus: false,
      hasPrevInfo: true,
      viewId: "",
      type: "",
      vipEndTimes: "",
      isLoading: false,
      isShowExpression: false,
      expressionList: (() => {
        let n = 1;
        return Array.from({ length: 43 }, () => {
          const scaleObj = {
            14: "1.3",
            16: "1.15"
          };
          const item = {
            scale: scaleObj[n] || 1,
            url: `https://hlwyy.zxxyyy.cn/hospital/his-miniapp/p242/emijo/emijo-${n++}.png`
          };
          return item;
        });
      })()
    }
  }

  componentDidMount() {
    const options = this.$router.params || {}
    if (options.status != 3 && options.type !== "5") {
      this.getNoteProfile();
    }
    if(options.vipEndDate){
      this.setvipEndTimes(options.vipEndDate);
    }
    this.getTime(options);
    this.setState({
      vipCode: options.vipCode,
      groupId: options.groupId,
      type: options.type,
      isEnd: options.status == "2" || options.status == "3",
    }, () => {
      this.getChat();
      this.getVideoInfo();
    })
    this.setTitle(options);
    setTimeout(() => {
      this.setState({ showTips: false })
    }, 15000);
  }

  componentWillUnmount() {
    clearInterval(this.chatInterval);
    clearInterval(this.videoInterval);
  }
  componentDidShow() {
    this.debounceFunc = this.debounce(this.queryChatRobotList.bind(this), 300);
    this.chatInterval = setInterval(() => this.getChat("next", true), 1000 * 2);
    this.videoInterval = setInterval(() => this.getVideoInfo(), 1000 * 3);
  }
  componentDidHide() {
    clearInterval(this.chatInterval);
    clearInterval(this.videoInterval);
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '家辉咨询',
    navigationBarTextStyle: 'black'
  };

  get scrollStyle() {
    let offsetHeight = 108;
    const { showTips, showPlus } = this.state
    showTips && (offsetHeight += 88)
    showPlus && (offsetHeight += 192)
    return `height:calc(100vh - ${offsetHeight}rpx)`;
  }

  /** TODOS:
   * 1. watch list
  */

  //获取提示语
  async getNoteProfile() {
    const { code, data = {} } = await Api.getNoteProfile({
      profileKey: "doctor_getAlertNoteProfile_chatStart"
    });
    if (code == 0) {
      Taro.showModal({
        title: "",
        content: data.profileValue || "",
        showCancel: false,
        confirmText: "确定",
      });
    }
  }

  /** methods */
  closeTopTip() {
    this.setState({ showTopTip: false })
  }
  openModal() {
    this.setState({ showPlus: false })
  }
  cancel() {
  }
  sure() {
    // 确认结束咨询
    this.closure();
  }
  showPlus() {
    this.setState({ showPlus: !this.state.showPlus, isShowExpression: false })
  }
  hidePlus() {
    this.setState({ showPlus: false, isShowExpression: false })
  }
  inputMsg(e) {
    const value = e.detail.value || "";
    this.setState({ msgText: value }, () => {
      if (value.length > 0 && value.length <= 10) {
        this.debounceFunc();
      }
    })
  }
  sendMsg(e) {
    console.log(e)
    const msgText = e.detail.value || this.state.msgText
    if (e.detail.value) {
      this.setState({ msgText })
    }
    if (!msgText || msgText == null) {
      Taro.showToast({
        title: "请先输入回复信息",
        icon: "none",
        duration: 800
      });
      return;
    }
    this.send({
      groupId: this.state.groupId,
      operator: "user",
      content: Utils.utf16toEntities(msgText),
      type: 1
    });
  }
  async callCamera() {
    // 调用相机
    const ctx = Taro.createCameraContext();
    ctx.takePhoto({
      quality: "high",
      success: res => {
        this.setData({
          src: res.tempImagePath
        });
      }
    });
  }
  showExpression() {
    // 表情
    this.setState({ showPlus: false, isShowExpression: true, })
  }
  sendExpression(item) {
    // 发送表情
    if (!item) {
      return false;
    }
    this.setState({ showPlus: false, isShowExpression: false })
    this.send({
      type: 5,
      operator: "user",
      groupId: this.state.groupId,
      content: item || "",
      token: ""
    });
  }
  async picture(type) {
    let sourceType = ["album", "camera"];
    if (type === 2) {
      sourceType = sourceType.slice(1);
    }
    const chooseRes = await Taro.chooseImage({
      count: 1,
      sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
      sourceType
    });
    if (chooseRes.errMsg == "chooseImage:ok") {
      const tempFilePath = chooseRes.tempFilePaths[0];
      Taro.showLoading({ title: "发送中...", mask: true });
      const data = await uploadFile(tempFilePath);
      this.send({
        type: 3,
        operator: "user",
        groupId: this.state.groupId,
        content: data,
      });

      Taro.hideLoading();
    }
  }
  ToMenu() {
    Taro.navigateTo({
      url: `/pages/consultservice/vipchatRecordlist/index?vipCode=${
        this.state.vipCode
      }`
    });
  }
  previewImg(e) {
    const arr = [];
    this.state.list.map(item => {
      if (item.type == 3 && item.content) {
        arr.push(item.content);
      }
    });
    const imgUrl = e.currentTarget.dataset.preurl;
    Taro.previewImage({
      current: imgUrl, // 当前显示图片的http链接
      urls: arr // 需要预览的图片http链接列表
    });
  }
  longtap(id) {
    console.log(id, this.state.isEnd, 99)
    if (!this.state.isEnd) {
      this.setState({ showId: id })
    }
  }
  hidden() {
    this.setState({ showId: '' })
  }
  copy(data) {
    Taro.setClipboardData({ data });
    this.setState({ showId: '' })
  }
  async delOrRe(item, type) {
    const { code } = await Api.change({
      groupId: this.state.groupId,
      operator: "user",
      messageId: item.id,
      operType: type
    });
    if (code == 0) {
      this.revokeInfo([item]);
    }
  }
  focusTest() {
    this.setState({
      isInputFocus: true,
    })
  }
  blurTest() {
    this.setState({
      isInputFocus: false,
      robotMessageList: [],
    })
  }
  scrollTop() {
    // 滚动到顶部加载会话
    if (this.state.hasPrevInfo) {
      this.getChat("prev");
    }
  }
  scroll() {
    // 滚动时保存时间
    this.setState({
      scrollTime: new Date().getTime()
    })
  }
  sendRobotMessage(item) {
    // 发送智能信息
    this.setState({ msgText: '' })
    this.send({
      groupId: this.state.groupId,
      operator: "user",
      content: item.questionContent,
      chatRobotId: item.chatRobotId,
      type: 1
    });
    // 关闭键盘
    this.setState({
      isInputFocus: false,
      robotMessageList: [],
    })
  }
  queryVideoStatus() {
    // 查看视频状态
    this.getVideoInfo(true);
  }
  toRegister() {
    // const scheduleDate = Utils.getFormatDate(0, "-");
    // const { deptId = "", doctorId = "" } = this.$router.params || {};
    Taro.reLaunch({
      url: `/pages/index/index`
    });
  }

  async closure() {
    // 结束咨询
    const closure = await Api.closure({
      groupId: this.state.groupId,
      operator: "user"
    });
    if (closure.code == 0) {
      this.getChat("next");
    }
  }

  setvipEndTimes(value) {
    const vipEndTimes = value
      .slice(0, 10)
      .replace(/-/, "年")
      .replace(/-/, "日");
    this.setState({ vipEndTimes })
  }
  setTitle(options) {
    if (!options.type) {
      return;
    }
    const titleObj = {
      1: "在线咨询",
      2: "机构咨询",
    };
    Taro.setNavigationBarTitle({
      title: titleObj[options.type] || "在线咨询"
    });
  }

  //获取当前时间
  getNowTime(value) {
    let dd = new Date();
    dd.setDate(dd.getDate()); //获取AddDayCount天后的日期
    let yyyy = dd.getFullYear();
    let mm =
      dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1; //获取当前月份的日期，不足10补0
    let cc = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate(); //获取当前几号，不足10补0
    const currentDate = yyyy + "-" + mm + "-" + cc;
    if (new Date(value).getTime() === new Date(currentDate).getTime()) {
      Taro.showModal({
        title: "温馨提示",
        content:
          "感谢您选择龚斐团队全病程健康管理咨询服务，我们是龚教授团队医疗助理，在服务期限内我们将为您提供全程连续健康管理服务，服务内容包括：一对一专属问答咨询、精准规划全流程诊疗，主动治疗/健康指导、动态管理随访提醒、院内诊疗预约。您可以在此聊天界面与我们留言沟通，我们持续关注您的动态治疗情况。祝您好孕。温馨提示：团队助理咨询回复时间：周一至周日8：00-12:00，14:00-17:00。我院病友专享服务热线：0731-84372980 【周一至周日07:30-20:00】遗传咨询热线：13975129070 非工作时间/急诊热线：0731-82355406",
        showCancel: false,
        confirmText: "确定",
        confirmColor: "#3CC51F"
      });
    }
  }
  getTime(options) {
    if (options.type !== "5") {
      return;
    }
    const date = new Date();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const times = String(this.addZero(hour)) + String(this.addZero(minute));
    if (
      Number(times) <= 800 ||
      (1200 <= Number(times) && Number(times) <= 1400) ||
      Number(times) >= 1700
    ) {
      Taro.showModal({
        title: "温馨提示",
        content:
          "尊敬的病友：您好！目前为非工作时间，团队助理将在正常上班时间尽快回复。如果您有紧急情况，建议您尽快来我院或就近就诊或拨打非工作时间/急诊热线：0731-82355406。感谢您的理解和配合，祝您好孕！",
        showCancel: false,
        confirmText: "确定",
        confirmColor: "#3CC51F"
      });
    } else {
      this.getNowTime(options.vipStartDate);
    }
  }
  addZero(s) {
    return s < 10 ? `0${s}` : s;
  }
  async getVideoInfo(action) {
    // 查询视频状态
    let { code, data = {} } = await Api.getVideoStatus({
      groupId: this.state.groupId
    });
    if (code === 0 && data && data.status === "live") {
      // 跳转视频
      clearInterval(this.videoInterval);
      if (!action) {
        // 间隔器查询 弹窗询问
        const showModalRes = await Taro.showModal({
          title: "提示",
          content: "医生发起视频问诊，是否进入问诊",
          showCancel: true,
          confirmText: "确定",
        });
        if (!showModalRes.confirm) {
          return;
        }
      }
      Taro.navigateTo({
        url: `/package1/consult/video/index?groupId=${this.groupId}&from=consult`
      });
    } else {
      if (action) {
        // 手动点击消息查询状态
        Taro.showModal({
          title: "温馨提示",
          content: "当前医生未开启视频问诊",
          showCancel: false
        });
      }
    }
  }
  async getChat(type = "", isScrollToBottom) {
    // 查询聊天
    if (this.state.isLoading) {
      return;
    }
    let list = this.state.list || [];
    const param = {};
    if (type === "next") {
      if (list[list.length - 1] && list[list.length - 1].createTime) {
        param.endTime = list[list.length - 1].createTime;
      }
    } else if (type === "prev") {
      if (list[0] && list[0].createTime) {
        param.startTime = list[0].createTime;
      }
    }
    this.setState({ isLoading: true })
    let { code, data = {} } = await Api.getChat({
      groupId: this.state.groupId,
      operator: "user",
      ...param
    });
    this.setState({ isLoading: false })
    if (code == 0) {
      data.recordList = (data.recordList || []).reverse().map(item => {
        item.time = Utils.getChatShowTime(item.createTime || "");
        return item;
      });
      let firstItemId;
      if (type === "next" || !type) {
        // 往下查询或初次查询
        list = list.concat(data.recordList);
      } else if (type === "prev") {
        if (list.length > 0) {
          // 查询时的第一个元素
          firstItemId = (list[0] || {}).id;
        }
        if (data.recordList.length < 15) {
          this.setState({ hasPrevInfo: false })
        }
        list = data.recordList.concat(list);
      }
      this.setState({ list })
      if (data.recordList.length > 0 && list.length > 0) {
        // 滚动到的元素id
        const viewId = `id-${
          !type || isScrollToBottom
            ? (list[list.length - 1] || {}).id
            : firstItemId
        }`;
        setTimeout(() => {
          this.setState({ viewId })
        }, 500);
      } else if (type === "next") {
        // 滚动后停留时间超过20s
        if (new Date().getTime() - this.state.scrollTime >= 20 * 1000) {
          const viewId = `id-${
            !type || isScrollToBottom
              ? (list[list.length - 1] || {}).id
              : firstItemId
          }`;
          this.setState({ viewId })
        }
      }
      if (data.revokeChatList && data.revokeChatList.length > 0) {
        this.revokeTimeout = setTimeout(() => {
          this.revokeInfo(data.revokeChatList);
        }, 100);
      }
    }
  }
  async send(param) {
    // 发送消息
    const chatlist = await Api.sendMsg(param);
    if (chatlist.code == 0) {
      this.setState({ msgText: '' })
      this.getChat("next", true);
    }
  }
  async queryChatRobotList() {
    // 智能提问列表关键字索引
    const content = Utils.utf16toEntities(this.state.msgText);
    if (!content) {
      return;
    }
    const param = { content };
    const data = await Api.queryChatRobotList(param);
    if (data.code == 0 && data.data.length > 0) {
      const list = data.data.map(item => {
        const regExp = new RegExp(`(${this.state.msgText})`, "g");
        item.lightContent = item.questionContent.replace(
          regExp,
          '<font style="color: #3ECDB5">$1</font>'
        );
        return item;
      });
      this.setState({ robotMessageList: list })
    } else {
      this.setState({ robotMessageList: [] })
    }
  }
  debounce(fn, delay) {
    let timer = null;
    return () => {
      if (timer) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn();
        }, delay);
      } else {
        timer = setTimeout(() => {
          fn();
        }, delay);
      }
    };
  }
  revokeInfo(arr = []) {
    // 撤回消息
    const list = this.state.list;
    if (arr.length === 0 || list.length === 0) {
      return;
    }
    const idList = arr.map(item => item.id);
    list.forEach((item, index) => {
      const idx = idList.indexOf(item.id);
      if (idx != -1) {
        const currentItem = arr[idx] || {};
        currentItem.isRevoke = true;
        list.splice(index, 1, currentItem);
      }
    });
    this.setState({ list })
  }

  dateIsRevoke (date) {
    if ((Date.now() - new Date((date.replace(/\-/g, '/'))).getTime()) > 120000) {
      return false;
    } else {
      return true;
    }
  }

  render() {
    const { type, showTopTip, vipEndTimes, isInputFocus, list, viewId, scrollStyle,
      showId, isEnd, isShowExpression, expressionList, showPlus, robotMessageList, msgText } = this.state
    return (
      <View class='p-page' onClick={this.hidden}>
        <View>
          { type==5 && showTopTip ?
          <View class='header-times'>
            <View class='header-topcontent'>
              <View class='times-left'>
                <Text>您的VIP服务将于{{vipEndTimes}}到期</Text>
              </View>
              <View class='times-right' onClick={this.ToMenu}>套餐详情</View>
              <View class='shu-icon' />
              <Image class='close-icon' src='/resources/images/close.png' onClick={this.closeTopTip} />
            </View>
          </View> : null }
          <ScrollView
            id='scroll-content'
            class={isInputFocus && list.length <= 3 ? 'scroll-bottom' : ''}
            scroll-into-view={viewId ? viewId : ''}
            enable-flex
            scroll-y
            style={scrollStyle}
            onClick={this.hidePlus}
            onScrollToUpper={this.scrollTop}
            onScroll={this.scroll}
          >
            <View class='content'>
              {/* { isLoading ?
              <View class='m-loading'>
                <Image
                  class='m-loading-image'
                  mode='widthFix'
                  src={`${$REPLACE_IMG_DOMAIN}/media/images/common/loading_img.gif`}
                />
                加载中...
              </View> : null } */}
              { list.map((item, index) =>
              <block key={index}>
                { item.type == 'SYSTEM' && item.userIsShow == 1 ?
                <block>
                  <View class='date'>{item.time}</View>
                  <View id={`id-${item.id}`} class='msg'>
                    <RichText nodes={item.content} />
                  </View>
                </block> : null }
                { item.isRevoke ?
                <block>
                  <View class='invoke-block'>{item.messageUserType == 0 ? item.sendUserName : '你'}撤回了一条信息</View>
                </block> :
                <block>
                  { item.time ?
                  <block>
                    <View class='date'>{item.time}</View>
                  </block> : null }
                  { item.messageUserType == 0 ?
                  <View
                    id={`id-${item.id}`}
                    class='left'
                  >
                    <View class='img'>
                      { item.headImg ? <Image src={item.headImg} /> :
                      <Image
                        mode='widthFix'
                        src={`${$REPLACE_IMG_DOMAIN}/his-miniapp/p242/defaultHeadImg.png`}
                      /> }
                    </View>
                    <View class='content-block'>
                      <View class='name'>{item.sendUserName}</View>
                      { item.type == 1 ?
                      <block>
                        <View class='text'>
                          <Text class='empty-i' />
                          <View class='content-box'>
                            <rich-text nodes={item.content} />
                          </View>
                        </View>
                      </block> : null }
                      { item.type == 3 ?
                      <block>
                        { item.content ?
                        <View
                          onClick={this.previewImg}
                          data-preurl={item.content}
                          class='text image-block'
                        >
                          <Text class='empty-i' />
                          <View class='content-box'>
                            <Image mode='widthFix' src={item.content} />
                          </View>
                        </View> : null }
                      </block> : null }
                      { item.type == 5 ?
                      <block>
                        { item.content ?
                        <View class='text expression'>
                          <Text class='empty-i' />
                          <View class='content-box'>
                            <Image class='expression-image' src={item.content} />
                          </View>
                        </View> : null }
                      </block> : null }
                      { item.type == 6 ?
                      <block>
                        { item.content ?
                        <View
                          onClick={this.queryVideoStatus}
                          class='text'
                        >
                          <Text class='empty-i' />
                          <View class='content-box'>
                            <Image class='video-image' src='https://zxxymp.cn/hospital/his-miniapp/p242/consult/video.png' />
                            <View>发起了视频问诊</View>
                          </View>
                        </View> : null }
                      </block> : null }
                    </View>
                  </View> : null }
                  { item.messageUserType == 1 ?
                  <View
                    id={`id-${item.id}`}
                    class='right'
                  >
                    { item.id == showId ?
                    <View class={`tip no ${this.dateIsRevoke(item.createTime) ? '' : 'nocopy'}`}>
                      <Text class='empty-i' />
                      <Text onClick={() => this.delOrRe(item, 'revoke')}>撤销</Text>
                    </View> : null }
                    <View class='chat-content' onLongPress={() => this.longtap(item.id)}>
                      { item.type == 1 ?
                      <block>
                        <View class='text'>
                          <Text class='empty-i' />
                          <View class='content-box'>
                            <View>
                              <View>{item.content}</View>
                            </View>
                          </View>
                        </View>
                      </block> : item.type == 3 ?
                      <block>
                        <View
                          onClick={this.previewImg}
                          data-preurl={item.content}
                          class='text image-block'
                        >
                          <Text class='empty-i' />
                          <View class='content-box'>
                            <Image mode='widthFix' src={item.content} />
                          </View>
                        </View>
                      </block> : item.type == 5 ?
                      <block>
                        { item.content ?
                        <View class='text expression'>
                          <Text class='empty-i' />
                          <View class='content-box'>
                            <Image class='expression-image' src={item.content} />
                          </View>
                        </View> : null }
                      </block> :
                      <View
                        class='text'
                        onLongPress={() => this.longtap(item.id)}
                      >
                        <Text class='empty-i' />
                        <Text>{item.content}</Text>
                        { item.id == showId ?
                        <View class={`tip no ${this.dateIsRevoke(item.createTime) ? '' : 'no'}`}>
                          <Text class='empty-i' />
                          <Text onClick={() => this.copy(item.content)}>复制</Text>
                          <Text onClick={() => this.delOrRe(item.id, 'revoke')}>撤销</Text>
                        </View> : null }
                      </View> }
                      <View class='img'>
                        <open-data type='userAvatarUrl' />
                      </View>
                    </View>
                  </View> : null }
                </block> }
              </block> )}
              { isEnd ?
              <View class='invoke-block'>本次咨询已结束，感谢您的使用。</View> : null }
            </View>
          </ScrollView>

        </View>

        { isEnd ?
        <View class='operation-box operation-btn-area'>
          <Button class='btn' onClick={this.toRegister}>再次咨询</Button>
        </View> : null }

        { !isEnd ?
        <View class='operation-box'>
          { robotMessageList.length > 0 && isInputFocus ?
          <View class='m-robot-message-list'>
            { robotMessageList.map((item, index) =>
            <block key={index}>
              <RichText
                className='m-robot-message-item'
                nodes={item.lightContent}
                onClick={() => this.sendRobotMessage(item)}
              />
            </block> )}
          </View> : null }
          <View class='operation-content'>
            <View class='top'>
              <Textarea
                type='text'
                class='input'
                cursor-spacing='14'
                value={msgText}
                show-confirm-bar={false}
                confirm-type='send'
                onInput={this.inputMsg}
                onConfirm={this.sendMsg}
                onFocus={this.focusTest}
                onBlur={this.blurTest}
                auto-height
                fixed
                maxlength='500'
              />
              <Image src={`${$REPLACE_IMG_DOMAIN}/ih-miniapp/plus.png`} onClick={this.showPlus} />
              { msgText ? <Image src={`${$REPLACE_IMG_DOMAIN}/ih-miniapp/icon-send-primary.png`} onClick={this.sendMsg} /> :
              <Image src={`${$REPLACE_IMG_DOMAIN}/ih-miniapp/icon-send-disabled.png`}></Image> }
            </View>
            { showPlus ?
            <View class='bottom'>
              <View class='picture' onClick={() => this.picture(1)}>
                <Image src={`${$REPLACE_IMG_DOMAIN}/ih-miniapp/tp.png`} />
                <View class='icon-des'>图片</View>
              </View>
              <View class='picture' onClick={() => this.picture(2)}>
                <Image src={`${$REPLACE_IMG_DOMAIN}/ih-miniapp/camera.png`} />
                <View class='icon-des'>拍照</View>
              </View>
              <View class='picture' onClick={this.showExpression}>
                <Image src={`${$REPLACE_IMG_DOMAIN}/ih-miniapp/expression.png`} />
                <View class='icon-des'>表情</View>
              </View>
            </View> : null }
          </View>
          { isShowExpression ?
          <View class='expression-block'>
            { expressionList.map((item, index) =>
            <View
              key={index}
              class='expression-item'
              onClick={() => this.sendExpression(item.url)}
            >
              <Image src={item.url} mode='widthFix' style={{ transform: `scale(${item.scale})` }} />
            </View>  )}
          </View> : null }
        </View> : null }
      </View>
    )
  }
}
