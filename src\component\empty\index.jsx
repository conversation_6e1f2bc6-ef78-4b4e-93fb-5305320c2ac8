import Taro, { Component } from '@tarojs/taro';
import { View, Image, RichText } from '@tarojs/components';
import EmptyPng from '@/static/image/empty.png'

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  render () {
    const { text } = this.props;
    return (
      <View className='wgt-empty-top'>
        <View className='wgt-empty-box'>
          <Image
            className='wgt-empty-img'
            src={EmptyPng}
          />
          <View className='wgt-empty-txt'>
            <RichText nodes={text} />
          </View>
        </View>
      </View>
    )
  }
}
