### 开发规范
1、[遵循Taro eslint代码规范](https://taro-docs.jd.com/taro/docs/spec-for-taro.html)，提交以及编译强制eslint代码检查
2、[智慧医疗中心前端标准产品质量规范](http://192.168.11.166/pages/viewpage.action?pageId=89096312)

## 相关文档
1、接口文档：http://192.168.11.166/pages/viewpage.action?pageId=12091406
2、UI地址：https://lanhuapp.com/web/#/item/project/board?pid=c65d90c2-440b-4b49-83a6-4156e4003a03
3、流程图：![](https://static.med.gzhc365.com/fss/publicfile/8b29eef9c3ecda11dc59474d15404affeb3600ffa7ebab10b6e024b33f170c25.png)
4、插件开发文档：
  - wxParser：https://mp.weixin.qq.com/wxopen/plugindevdoc?appid=wx9d4d4ffa781ff3ac&token=654050961&lang=zh_CN

5、项目结构：
```
  base
  ├── .tmp         // 编译临时目录
  ├── dist         // 编译后目录
  ├── node_modules // 项目依赖
  ├── src
  │    ├── components // 微信小程序自定义组件
  │    ├── config     // 配置文件
  │    │     ├── env       // 区别环境的配置项
  │    │     ├── api      // 接口 url 配置
  │    │     └── constant // 常量配置
  │    ├── images     // 页面中的图片和icon
  │    ├── pages      // 小程序page文件
  │    ├── styles     // ui框架，公共样式
  │    ├── template   // 模板
  │    ├── utils      // 公共js文件
  │    ├── app.js
  │    ├── app.json
  │    ├── app.less
  │    ├── project.config.json // 项目配置文件
  ├── .gitignore
  ├── .eslintrc.js
  ├── compile.json
  ├── package-lock.json
  ├── package.json
  └── README.md
```
6、调试方式：
微信小程序开发者工具调试

7、编译命令(在脚手架项目fe-his-twxapp-dev目录执行)
```
 // 微信小程序
 $ npm run dev --dir=p163 开发环境
 $ npm run qa --dir=p163 测试环境
 $ npm run prod --dir=p163 线上环境不压缩
 $ npm run build --dir=p163 线上环境压缩
```

8、静态资源部署方案：
  - 部署服务器路径(开发)：/app/oper/static/websites/miniprogram-static/fe-his-twxapp/p163/
    访问：https://sstatic.med.gzhc365.com/miniprogram-static/fe-his-twxapp/p163/xxx.png
  - 部署服务器路径(测试)：/app/stsweb/websites/miniprogram-static/fe-his-twxapp/p163/
    访问：https://ustatic.med.gzhc365.com/miniprogram-static/fe-his-twxapp/p163/xxx.png
  - 部署服务器路径(线上)：/app/stsweb/websites/miniprogram-static/fe-his-twxapp/p163/
    访问：https://static.med.gzhc365.com/miniprogram-static/fe-his-twxapp/p163/xxx.png

  运维上线：
  1. 项目管理系统提单选择对应的医院，
  然后运维系统前端投产选择 his-智慧医院4.0微信小程序，输入其他项点击提交，如下图
  ![运维投产示例](https://static.med.gzhc365.com/fss/publicfile/c5e1b777955a44a821727b960b968a277cd3c2752389a7cfa7f31fb53923ed50.png "运维投产示例图")

  2. 刷新页面点击 "编译"，等待一会然后 点击 日志 查看编辑进度，待日志显示成功后点击 申测 按钮，如下图
  ![运维投产示例](https://static.med.gzhc365.com/fss/publicfile/7c9e24ab99b8077f5bc2508ca25426cb613ece094cd9d7ed91f1168614cdf424.png "运维投产示例图")

  ![运维投产示例](https://static.med.gzhc365.com/fss/publicfile/374a44f45e322025d6191f47614106dd3c96d28c3c9de1231fbe3e4cb195973a.png "运维投产日志查看示例图")

  3. 测试通过后，点击运维系统左侧的审核清单，搜索到当前工单，点击 审核代码，等待3分钟。
  ![运维投产示例](https://static.med.gzhc365.com/fss/publicfile/40e2ee07578d35c01ae42c90fc78b03742de0a316bc9dbafc2bca031cc5bb6ad.png "运维投产示例图")

  4. 回到提测页面，点击按钮 灰，看到结果后点击按钮 上线，至此运维投产流程完成。
  ![运维投产示例](https://static.med.gzhc365.com/fss/publicfile/801584ad791c8c9ee69dd4839812105e8abcedd128dc3721f20c532f9c1aab9e.png "运维投产示例图")
