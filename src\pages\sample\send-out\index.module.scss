
.page {
    padding-bottom: 250rpx;
}

.header {
    padding: 24px;
    background-color: #F2F4F4;

    &_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba(0, 0, 0, 0.90);
        font-size: 28px;
        font-weight: 600;
        line-height: 42px;

        /* 150% */
        &__icon {
            width: 40px;
            height: 40px;
        }
    }

    &_camera {
        margin-top: 24px;
        width: 100%;
        height: 220px;
        border-radius: 11px;
        border: 1px solid #308B91;
    }
}

.list_item {
    margin: 16rpx 24rpx;
    box-sizing: border-box;
    width: calc(100% - 48rpx);  // 补偿左右margin
    padding: 32px;
    background-color: #FFF;
    border-radius: 8px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.12);
    font-size: 28px;
    position: relative; /* 确保不脱离文档流 */
    z-index: auto; /* 默认值，避免与按钮层叠冲突 */

    &_field {
        padding: 20rpx 0
    }
    &_field2 {
        flex-direction: column;  // 改为纵向布局
        align-items: flex-start; // 左对齐
    }
    
    &_remark_input {
        white-space: normal;  // 允许换行
        width: 100%;  // 修正显示宽度
        height: 1000%;
        min-height: 150rpx;
        padding: 20rpx 30rpx;
        border: 1rpx solid #000000;
        border-radius: 16rpx;
        font-size: 28rpx;
        line-height: 1.5;
        word-wrap: break-word;
        box-sizing: border-box;
        
        // 输入内容样式
        &[value] {
            color: #060606;
        }
    }

    &_field:first-child {
        border-bottom: 1rpx solid #F2F4F4;
    }

    &_placeholder {
        font-weight: 400;
        color: #989898;
    }

    &_icon {
        width: 48rpx;
        height: 48rpx;
    }

    &_info {
        width: 100%;
        padding: 30rpx 0;
        display: flex;
        align-items: center;
    }

    &_info:first-child {
        border-bottom: 1rpx solid #F2F4F4;
    }

    &_address {
        padding: 0 30rpx;
        flex: 1;
    }

    &_rtext {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #666;
        padding-left: 30rpx;
        border-left: 1rpx solid #F2F4F4;
    }

    &_name {
        color: rgba(0, 0, 0, 0.90);
        font-size: 36px;
        font-weight: bold;
        line-height: 54px;
        padding-right: 15rpx;
        @include ellipsisLn();
    }

    &_mobile {
        font-weight: bold;
    }

    &_newname {
        color: #999;
        font-size: 36px;
        font-weight: bold;
        line-height: 54px;
        padding-right: 15rpx;
        @include ellipsisLn();
    }

    &_oper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15rpx;
    }

    &_radio {
        transform-origin: 0 30%;
        transform: scale(.7);
    }

    &_checkbox {
        transform-origin: 0 30%;
        transform: scale(.7);
    }

    &_btn {
        display: flex;
        align-items: center;
        padding-left: 60rpx;
        line-height: 30rpx;
    }

    &_arrow {
        width: 32rpx;
        height: 32rpx;
    }

    &_content {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        margin-top: 20rpx;
        column-gap: 20rpx;
        max-width: 90vw;
    }

    &_tips {
        color: #000;
        font-weight: bold;
        font-size: 30rpx;

        &_red {
            color: #ff0000;
           
        }
    
    }

    &_red {
        color: #ff0000;
        content: left;
    }


    &_sample {
        display: flex;
        align-items: center;
        border: 1rpx solid $color-primary;
        // width: fit-content;
        width: auto;
        min-width: 300rpx;
        justify-content: space-evenly;
        border-radius: 50rpx;
        padding: 10rpx 20rpx;
        background: #d2f9fb;
        margin: 10rpx 15rpx;
        flex-grow: 1;

        &_text {
            padding-right: 10rpx;
            color: $color-primary;
            flex: 1;
            text-align: left;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
            // 增加行高
            line-height: 1.5;
            // 调整字体大小
            font-size: 26rpx;
            // 添加内边距
            padding: 0 20rpx;
        }

        &_img {
            width: 32rpx;
            height: 32rpx;
        }


    }

    &_paytype {
        color: #000;

        &_input {
            color: #000;
            text-align: end;
            margin-right: 10rpx;
            font-weight: bold;

        }

        text {

            font-size: 32rpx;
            margin-left: 10px;
            color: #f00;
          }


    }


    &_paytype2 {
        margin-bottom: 20rpx;  // 增加标题与输入框间距
        color: #000;
        font-weight: bold;

        &_input {
            color: #000;
            text-align: end;
            margin-right: 10rpx;
            font-weight: bold;
        }
    }

}
//下一步
.nextButton {
    height: 96rpx;
    line-height: 96rpx;
    background: #308B91;  // 与设计图按钮色一致
    color: #FFF;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 500;
    z-index: 1002;
    
    &::after {
      border: none;
    }
  }
.bottom {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    display: flex;
    z-index: 300; 
    //下单
    &_btn {
        margin: 40rpx 40rpx 70rpx 40rpx;
        background: $color-primary;
        width: 100%;
        color: $color-white;
    }
}

.layout {
    padding: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    margin: 20rpx 0;

    &_item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &_text {
            color: #000;
            font-size: 32rpx;
            line-height: 60rpx;
        }
    }

    &_btn {
        margin: 10rpx 0 30rpx 0rpx;
        background: #30A1A6;
        width: 100%;
        color: #fff;
    }

    &_monthly {
        color: #333;
        display: flex;
        padding: 30rpx 20rpx;
        background: #F7F7F7;
        margin: 30rpx 0rpx 10rpx 0rpx;
        border-radius: 10rpx;

        &_input {
            flex: 1;
            margin-left: 30rpx;
            color: #000;
        }
    }
}



// 弹窗样式调整
// 调整后的样本编号样式
.scanPopup {
    .formRow {
      margin-bottom: 12rpx;  // 缩小标签间距
    }
  
    .sampleNumber {
      font-size: 32rpx;
      color: #333;
      padding: 24rpx;
      background: #FFF;
      border-radius: 8rpx;
      border: 1rpx solid #F2F4F4;  // 与产品线选择器保持一致
      margin-bottom: 24rpx;  // 增加下边距
      display: block;  // 换行显示
    }
  
    // 产品线选择器同步样式
    .pickerTrigger {
      width: 100%;
      background: #FFF;
      border: 1rpx solid #F2F4F4;
      border-radius: 8rpx;
      padding: 24rpx;
    }
  }
   //产品线
  .formRow {
    width: 100%;
    margin-bottom: 48rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
  //样本编号、产品线
  .label {
    font-size: 32rpx;  // 与列表项字体一致
    color: rgba(0, 0, 0, 0.9);
    margin-bottom: 16rpx;
    font-weight: 600;

    text {
        
        font-size: 32rpx;
        margin-left: 10px;
        color: #f00;
      }
  }
  
  .sampleNumber {
    font-size: 32rpx;
    color: #333;
    padding: 24rpx;
    background: #F8F9FA;
    border-radius: 16rpx;
    border: 1rpx solid #F2F4F4;

  }
  
  .picker {
    position: relative;
    
    .input {
      width: 100%;
      height: 88rpx;
      padding: 0 24rpx;
      font-size: 28rpx;
      border: 1rpx solid #F2F4F4;
      border-radius: 8rpx;
      color: rgba(0, 0, 0, 0.9);
      background: #FFF;
    }
    
    .arrow {
      position: absolute;
      right: 24rpx;
      top: 50%;
      transform: translateY(-50%);
      border-left: 8rpx solid transparent;
      border-right: 8rpx solid transparent;
      border-top: 12rpx solid #308B91;  // 使用主题色
    }
  }
  

  
  .placeholder {
    color: rgba(0, 0, 0, 0.4);
    font-size: 28rpx;
  }
  .input:focus {
    border-color: #308B91;
    box-shadow: 0 0 8rpx rgba(48, 139, 145, 0.2);
  }
  // 
.pickerTrigger {
    width: 80%;
    padding: 24rpx;
    background: #fff;
    border: 1rpx solid #F2F4F4;
    border-radius: 16rpx;
    position: relative;
  }
  //请选择产品线
  .pickerText {
    color: rgba(0, 0, 0, 0.9);
    font-size: 32rpx;

  }
  .pickerText {
    // 默认样式
    color: #333;
    font-size: 32px;
    
    &.placeholder {
      color: #999;  // 提示文字颜色
    }
  }
  .sampleInfo {
    flex: 1;
    .sampleNo {
      font-size: 32rpx;
      color: #333;
    }
    .productLine {
      font-size: 24rpx;
      color: #666;
      margin-top: 8rpx;
    }
  }
  @media (max-width: 480px) {
    .list_item_sample_text {
        font-size: 24rpx;
        padding: 0 15rpx;
    }
}
.word_count {
    align-self: flex-end;
    color: #999;
    font-size: 24rpx;
    margin-top: 10rpx;
    align-self: flex-end;
}
// .remark_input {
//     white-space: normal;  // 允许换行
//     width: 100%;  // 修正显示宽度
//     min-height: 120rpx;  // 增加最小高度
//     padding: 20rpx 30rpx;
//     border: 1rpx solid #000000;
//     border-radius: 16rpx;
//     font-size: 28rpx;
//     line-height: 1.5;
//     word-wrap: break-word;
//     box-sizing: border-box;
    
//     // 输入内容样式
//     &[value] {
//         color: #ff1414;
//     }
// }
