.page{
  .loginPageNotice{
    padding: 32px 24px;
    background-color: #FFFAF1;
    color: #BE8014;
    font-size: 24px;
    font-weight: 400;
    line-height: 36px;
  }
  &_body{
    background-color: #FFF;
    .inputItemCode {
      margin-right: 24px;
      color: $color-primary;
      font-size: 32px;
      &::after{
        border: none;
      }
    }
  }
  &_footer {
    padding: 26px 24px;
    &_button{
      margin-top: 24px;
      height: 96rpx;
      width: 100%;
      line-height: 96rpx;
      font-size: 34px;
      border-radius: 76rpx;
      font-weight: 600;
      &.submit{
        color: #fff;
        background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
      }
      &.cancel{
        background: rgba(0, 0, 0, 0.04);
        color: rgba(0, 0, 0, 0.70);
      }
      &.disabled{
        opacity: 0.6;
        background-color: $color-primary !important;
        color: #FFF !important;
      }
      &::after{
        border: none;
      }
    }
    &_records {
      margin-top: 24px;
      padding: 12px;
      text-align: center;
      color: $color-primary;
      font-size: 32px;
    }
  }
}
