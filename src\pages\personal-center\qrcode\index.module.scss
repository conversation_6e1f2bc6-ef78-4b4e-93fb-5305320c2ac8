.mask {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.modalContainer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  .modalContent {
    position: absolute;
    z-index: 9999;
    left: 50%;
    top: 92px;
    padding: 40px 40px 24px 40px;
    transform: translate(-50%, 0);
    width: 702px;
    box-sizing: border-box;
    background-color: #fff;
    text-align: center;
    border-radius: 50px;
    background: linear-gradient(180deg, #2D666F 0%, #FFFFFF 82.39%);

    .header{
      // padding-bottom: 40px;
      // box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.06) inset;
      padding: 40px;
    }
    .doctorName {
      margin-bottom: 10px;
      color: #050505;
      /* 48B */
      font-family: PingFang SC;
      font-size: 48px;
      font-style: normal;
      font-weight: 600;
      line-height: 72px; /* 150% */
    }
    .institutionName{
      color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));
      text-align: center;
      font-family: PingFang SC;
      font-size: 28px;
      font-style: normal;
      font-weight: 500;
      line-height: 42px; /* 150% */
    }

    .body{
      padding-top: 32px;
      border-radius: 50px;
      background: #fff;
      .image{
        padding: 10px 0;
        width: 488px;
        height: 489px;
      }
      .tips{
        font-size: 22px;
        color: rgba(0, 0, 0, 0.4);
      }
    }
    .closeBtn{
      padding-top: 20px;
      font-size: 30px;
      line-height: 40px;
      border-top: 1px solid #e5e5e5;
    }
  }
}
