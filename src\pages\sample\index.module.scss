.page {
  padding: 24px;
  padding-bottom: 124px; // 增加底部间距，为按钮留出空间
  position: relative; // 添加相对定位，为底部按钮定位提供参考
}

.block{
  margin-bottom: 24px;
  display: flex;
  height: 400px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: #3F969D;
  &.block_2{
    background: #3F78AD;
  }
  &_wrapper{
    height: 290px;
    width: 290px;
    border-radius: 50%;
    border: 29px solid rgba(255, 255, 255, 0.05);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  &__icon{
    margin-bottom: 24px;
    width: 97px;
    height: 90px;
  }
  &__value{
    color: #FFF;
    font-size: 32px;
    font-weight: 400;
    line-height: 48px;
  }
}

.summary{
  padding: 24px 32px;
  background: #FFF;
  border-radius: 16px;
  &_title{
    padding-bottom: 24px;
    border-bottom: 1px solid #F2F4F4;
    color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
    font-size: 28px;
    font-weight: 600;
    line-height: 42px;
  }
  .list{
    padding: 30px 0;
    display: flex;
    align-items: center;
    &_item{
      flex: 1 1 33%;
      display: flex;
      flex-direction: column;
      align-items: center;
      &__top{
        margin-bottom: 12px;
        color: #3F969D;
        font-size: 40px;
        font-style: normal;
        font-weight: 600;
        line-height: 60px;
      }
      &__value{
        color: rgba(0, 0, 0, 0.70);
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: 36px; /* 150% */
      }
    }
  }
}

// 底部样本邮寄按钮样式
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.send_button {
  height: 96px;
  line-height: 96px;
  text-align: center;
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  border-radius: 76rpx;
  box-shadow: 0 4px 8px rgba(47, 132, 139, 0.2);
}
