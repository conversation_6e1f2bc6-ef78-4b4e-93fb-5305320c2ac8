import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.innerStyle = {};

    this.isIpx = (Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_hrd_ipx`) || '').toString() === '1'; // 1是2否

    if (this.isIpx) {
      this.innerStyle.paddingBottom = Taro.pxTransform($IPX_BOTTOM_HEIGHT * 1);
    }
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  render () {
    const { children, renderFooter = null, style = {}, bodyStyle = {}, footerStyle={}, renderHeader = null } = this.props;

    return (
      <View className='wgt-page' style={style}>
        <View className='wgt-page_header'>
          {renderHeader}
        </View>
        <View className='wgt-page_body' style={bodyStyle}>
          {children}
        </View>
        <View className='wgt-page_foot' style={{ ...this.innerStyle, ...footerStyle }}>
          {renderFooter}
        </View>
      </View>
    )
  }
}
