
.mcard{
  background-color: #fff;
  padding: 20px 30px;

  .amounthead{
    display: flex;

    .headlt{
      flex: 1;
      .amounttitle{
        font-size: 38px;
        color: $color-main;
        font-weight: bold;
      }
  
    }
  
    .headrt{

      .balancelabel{
        color: $color-text;
        font-size: 24px;
      }
      .balance{
        color: $color-primary;
        font-size: 34px;
      }
    }
  }

  .amountbody{

    .amountline{
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .amountitem{
        font-size: 28px;
        width: 150px;
        height: 80px;
        line-height: 80px;
        margin-top: 40px;
        border: 1px solid #EEEEEE;
        border-radius: 10px;
        position: relative;
        text-align: center;
        color: $color-title;
        background-color: #fff;
        box-sizing: border-box;

        &.activeamount{
          border: 0;
          box-sizing: border-box;
          position: relative;
          color: $color-primary;
          overflow: hidden;

          .activetop{
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: 1;
            border: 1px solid $color-primary;
            border-radius: 10px;
          }
  
          &:before{
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            border: 18px solid transparent;
            border-bottom: 18px solid $color-primary;
            border-right: 18px solid $color-primary;
            clip-path: polygon(100% 0, 100% 100%, 0 100%, 100% 0);
            z-index: 2;
          }
          &:after{
            content: '';
            width: 10px;
            height: 16px;
            position: absolute;
            right: 7px;
            bottom: 8px;
            border: 1px solid #fff;
            border-left: 0;
            border-top: 0;
            font-size: 14px;
            transform: rotate(30deg);
            z-index: 3;
          }
        }
        

        &:nth-child(3n+1){
          margin-left: 0;
        }
      }

      .amountinput{
        font-size: 30px;
        color: $color-primary;
        background-color: #fff;
        width: 100%;
        height: 80px;
        border: 1px solid #EEEEEE;
        border-radius: 15px;
        margin-top: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        .input{
          width: 100%;
          padding: 0 30px;
          color: $color-main;
        }
      }

    }

  }

}

