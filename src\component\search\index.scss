.g-search{
  position: relative;
  z-index: 10;
  height: 102px;
  box-sizing: border-box;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.02);

  &.noborder {
    box-shadow: none;
  }
}

.m-search{
  display: flex;
  width: 100%;
  height: 102px;
  flex-direction: row;
  padding: 16px 30px;
  background-color: #fff;
  align-items: center;
  box-sizing: border-box;
  &.active{
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
  }
  .search-ipt{
    flex: 1;
    height: 80px;
    padding-left: 60px;
    padding-top: 15px;
    padding-bottom: 15px;
    position: relative;
    background:rgba(249,249,249,1);
    border-radius: 45px;
    overflow: hidden;
    box-sizing: border-box;
  }
  .ipt{
    width: 100%;
    font-size: 30px;
    height: 52px;
    line-height: 52px;
  }
  .ipt-icon{
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    background: url(#{$cdn}/search.png) no-repeat center center / 100% 100%;
  }
  .search-extra{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 10px;
  }
  .extra-cancel{
    margin-left:30px;
    font-size: 30px;
    color: $color-primary;
  }
  .extra-itm{
    position: relative;
    margin-left: 20px;
    width: 40px;
    height: 40px;
    padding: 10px;
  }
  .itm-qr{
    width: 40px;
    height: 40px;
  }
  .itm-notice{
    width: 35px;
    height: 40px;
  }
  .itm-notice-num{
    position: absolute;
    right: 10px;
    top: 10px;
    padding: 0;
    background-color: red;
    color: #fff;
    width: 1.5em;
    height:1.5em;
    line-height: 1.5em;
    overflow: hidden;
    text-align: center;
    font-size: 24px;
    border-radius: 50%;
    transform: translate(40%,-40%);
  }
}

.m-search-content{
  position: fixed;
  left: 0;
  top: 102px;
  bottom: 0;
  width: 100%;
  z-index: 10;
  box-sizing: border-box;
  border-top:1px solid #efefef;
  overflow-y: auto;
  background-color: #f1f3f6;
  -webkit-overflow-scrolling: touch;
  .search-content{
    width: 100%;
    height: 100%;
  }
  .content{
    margin-bottom: 20px;
  }
  .content-title{
    color:$color-title;
    padding: 20px 30px;
    font-size: 34px;
    font-weight: 600;
  }
  .content-list{
    margin-bottom: 20px;
    background-color: #fff;
    padding-left: 30px;
  }
  .list-item{
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 30px 30px 30px 0;
    border-top:1px solid $color-border;
    &:first-child{
      border-top:none;
    }
  }
  .item-icon{
    display: flex;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    margin-right: 25px;
    .image{
      width: 80px;
      height: 80px;
      vertical-align: top;
    }
  }
  .item-image{
    display: flex;
    width: 119px;
    height: 133px;
    border-radius: 8px;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    margin-right: 25px;
    .image{
      vertical-align: top;
    }
  }
  .item-info{
    flex: 1;
    overflow: hidden;
  }
  .info-title{
    font-size: 34px;
    color: $color-title;
    .match{
      color: #FF613B;
    }
  }
  .info-text{
    margin-top: 5px;
    font-size: 30px;
    color:$color-text;
  }
  .info-more {
    font-size: 30px;
    color:$color-primary;
  }
  .info-des{
    margin-top: 5px;
    font-size: 24px;
    color:$color-text;
    @include ellipsisLn;
  }
  .item-arrow{
    width: 17px;
    height: 17px;
    border-right: 5px solid #C7C7CC;
    border-bottom: 5px solid #C7C7CC;
    transform: translateX(-8px) rotate(-45deg);
  }
}