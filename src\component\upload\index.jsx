import Taro, { Component } from '@tarojs/taro';
import { View, Image, Text } from '@tarojs/components';
import PropTypes from 'prop-types'
import uploadIcon from '@/static/image/upload.png'

import s from './index.module.scss'
import * as API from './api'

export default class Upload extends Component {
  constructor(props) {
    super(props)
  }

  deleteFile = (index) => {
    const { fileList, updateFileList } = this.props
    const list = fileList.filter((_, i) => i !== index)
    updateFileList(list)
  }

  uploadFile = async () => {
    const { fileList, limit, updateFileList, sourceType, sizeType } = this.props;
    const chooseRes = await Taro.chooseImage({
      count: limit - fileList.length,
      sizeType,
      sourceType,
    })
    const tempFilePath = chooseRes.tempFilePaths || [];
    Taro.showLoading({ title: "上传中...", mask: true });

    const list = await Promise.all(tempFilePath.map(file => API.uploadImages(file)))

    updateFileList([...fileList, ...list])

    Taro.hideLoading();
  }

  previewImage(current, urls) {
    Taro.previewImage({ current, urls })
  }

  render() {
    const { title, subTitle, fileList = [], limit, performation = '1' } = this.props
    // console.log(performation, this.props, '=====43')
    return (
      <View className={s.upload}>
        {title ?
          <View className={s.upload_title}>
            <Text>{title}</Text>
            <Text className={s.upload_title__sub}>{subTitle}</Text>
          </View> : null
        }

        <View className={[s.upload_body, 'flex']}>
          {fileList.map((v, i) => (
            <View key={v} className={[s.file_item, performation === '1' ? s.large_item : '']}>
              <Image
                className={s.file_item_img}
                mode='aspectFill'
                src={v}
                onClick={() => this.previewImage(v, fileList)}
              />
              <View className={s.file_item_close} onClick={() => this.deleteFile(i)}>删除</View>
            </View>
          ))}
          {fileList.length < limit ? <View className={s.file_item}>
            <Image
              className={s.file_item_img}
              mode='aspectFill'
              src={uploadIcon}
              onClick={this.uploadFile}
            />
          </View> : null}
        </View>
      </View>
    )
  }
}

Upload.propTypes = {
  title: PropTypes.string,
  fileList: PropTypes.array,
  limit: PropTypes.number,
  updateFileList: PropTypes.func,
  sourceType: PropTypes.array,
  sizeType: PropTypes.array,
  performation: PropTypes.string
};
Upload.defaultProps = {
  limit: 9,
  sourceType: ['camera', 'album'],
  sizeType: ['original', 'compressed'],
};
