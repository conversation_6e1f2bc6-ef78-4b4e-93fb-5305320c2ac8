page {
  height: 100%;
  background: $color-bg;
}

.container {
  color: #2D2D2D;

  &.white {
    height: 100%;
  }

  .pickerWrap {
    display: flex;
    width: 90%;
    margin: 20px auto;

    .picker {
      background: #fff;
      color: #000;
      padding: 0 10px;
      border-radius: 10px;
      display: flex;
      align-items: center;

      &::after {
        content: '';
        display: inline-block;
        background: #000;
        border-width: 10px;
        border-style: solid;
        border-color: #cccc #Fff #Fff #Fff;
        margin: 10px 0 0 10px;
      }
    }
  }

  .noData {
    width: 100%;
    text-align: center;
    padding-top: 25%;
    font-size: 30px;
    color: $color-text;

    image {
      width: 400px;
      height: 190px;
      margin-bottom: 22px;
    }
  }
}

.topbarBox {
  position: relative;
  padding-bottom: 100px;

  .topBar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: #fff;
    font-size: 34px;
    color: $color-title;
    box-shadow: 0px 4px 8px 2px rgba(0, 0, 0, 0.04);
    z-index: 999;

    .barItem {
      position: relative;
      font-size: 32px;
      &.barActive {
        color: #3eceb6;

        &::after {
          content: '';
          height: 4px;
          width: 120px;
          background: #3eceb6;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
}

.topModule {
  background-color: white;
  display: flex;
  justify-content: space-between;
  padding: 20px 20px 30px 20px;
  align-items: center;
  // position: fixed;
  // top: 96px;
}

.addRecord {
  color: #3ECEB6;
  border: 1px solid #3ECEB6;
  padding: 5px;
}

.allRecord {
  background-color: white;
  margin-bottom: 120px;
  // margin-top: 90px;
  z-index: 99;
}

.applyRecord{
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background-color: #3ECEB6;
  position: fixed;
  bottom: 30px;
  width: 80%;
  border-radius: 10px;
  margin: auto;
  right: 0;
  left: 0;
  color:#FFFFFF;
  font-size: 36px;
}
