.wgt-detailstatus{
  padding: 25px 25px 30px 25px;
  
  &.wgt-detailstatus-success{
    background: $color-primary;
  }
  &.wgt-detailstatus-fail{
    background: $color-error;
  }
  &.wgt-detailstatus-abnormal{
    background: $color-warn;
  }
  &.wgt-detailstatus-lock{
    background: $color-primary;
  }
  &.wgt-detailstatus-cancel{
    background: #989898;
  }
}
.wgt-detailstatus-bd{
  display: flex;
  align-items: center;
}
.wgt-detailstatus-bd-icon{
  width: 60px;
  height: 60px;
  image{
    vertical-align: top;
    width: 100%;
    height: 100%;
  }
}
.wgt-detailstatus-bd-tit{
  margin-left: 15px;
  font-size: 40px;
  color:#fff;
}
.wgt-detailstatus-bd-label{
  margin-left: 15px;
  display: inline-block;
  border: 1px solid #fff;
  color: #fff;
  font-size: 24px;
  border-radius: 4px;
  text-align: center;
  padding: 3px 5px;
  vertical-align: middle;
}
.wgt-detailstatus-bd-timer{
  flex: 1;
  color: #fff;
  font-size: 50px;
  text-align: right;
}
.wgt-detailstatus-ft{
  font-size: 30px;
  color:#fff;
  margin-top: 30px;
}
