import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';

import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    const { defaultIndex = 0 } = this.props;

    this.state = {
      index: defaultIndex * 1,
    };
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  changeTabIndex = (idx) => {
    const { index } = this.state;
    if (idx === index) {
      return false;
    }
    this.setState({ index: idx });
    const { onChange = () => {} } = this.props;
    onChange(idx);
  }

  render () {
    const { tabData = [] } = this.props;
    const { index } = this.state;
    return (
      <View className={styles.tab}>
        {
          tabData.map((item, idx) => {
            return (
              <View
                className={`${styles.tabLi} ${index === idx ? styles.active : ''}`}
                onClick={() => this.changeTabIndex(idx)}
                key={item.title}
              >
                <View className={styles.liText}>{item.title}</View>
              </View>
            );
          })
        }
      </View>
    )
  }
}
