import Taro, { Component } from "@tarojs/taro";
import {
  Text,
  Textarea,
  Input,
  View,
  Image,
  Switch,
  RadioGroup,
  Label,
  Radio,
  Checkbox,
} from "@tarojs/components";
import s from "./index.module.scss";
import * as Api from "./api";

export default class Index extends Component {
  config = {
    backgroundTextStyle: "light",
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "开单申请",
    navigationBarTextStyle: "black",
  };

  constructor(props) {
    super(props);
    this.state = {
      doctorId: "",
      patientName: "",
      relationPatientId: "",
      presentIll: "",
      mainSuit: "",
      pastIll: "",
      reason: "",
      purpose: "",
      existDiagnosis: "",
      fileList: [],

      fileUrlJsonArray: {
        medicalHistoryFile: [],
        otherFile: [],
      }, // 所有图片集合
      prescripList: [],
      diagnosisFemaleList: [
        {
          value: "原发不孕",
          text: "原发不孕",
          checked: false,
        },
        {
          value: "继发不孕",
          text: "继发不孕",
          checked: false,
        },
        {
          value: "多囊卵巢",
          text: "多囊卵巢",
          checked: false,
        },
        {
          value: "反复流产",
          text: "反复流产",
          checked: false,
        },
        {
          value: "不良孕史",
          text: "不良孕史",
          checked: false,
        },
      ],
      diagnosisMaleList: [
        {
          value: "原发不育",
          text: "原发不育",
          checked: false,
        },
        {
          value: "继发不育",
          text: "继发不育",
          checked: false,
        },
        {
          value: "少弱精症",
          text: "少弱精症",
          checked: false,
        },
        {
          value: "无精症",
          text: "无精症",
          checked: false,
        },
      ],
      // 处方集合
      prescriptionJsonArray: {
        female: [],
        male: [],
      },
    };
  }

  componentDidMount() {
    const { userId: doctorId = "" } = JSON.parse(
      Taro.getStorageSync("userInfo") || "{}"
    );
    this.setState({ doctorId });
    this.getPatientInfoByPid();
    // this.getFileTypeList();
    this.getPrescriptionList();
  }

  getPrescriptionList = async () => {
    const { prescriptionJsonArray = {} } = this.state;
    const { code, data } = await Api.getPrescriptionList({
      pageNum: 1,
      numPerPage: 100,
    });
    if (code === 0) {
      const { recordList = [] } = data;
      prescriptionJsonArray.female = JSON.parse(JSON.stringify(recordList));
      prescriptionJsonArray.male = JSON.parse(JSON.stringify(recordList));
      const prescripListF = recordList.filter((item, index) => {
        return (item.sex == 'F' || item.sex === '')
      });
      const prescripListM = recordList.filter((item, index) => {
        return (item.sex == 'M' || item.sex === '')
      });
      this.setState({ prescripList: recordList, prescripListF, prescripListM, prescriptionJsonArray: {...prescriptionJsonArray} });
    }
  };

  getPatientInfoByPid = async () => {
    const { patientPid: pid } = this.$router.params;
    const { code, data } = await Api.getPatientInfoByPid({ pid });
    if (code === 0) {
      const { patientName, id: relationPatientId } = data;
      this.setState({ patientName, relationPatientId });
    }
  };

  inputMainSuit = (keyValue, e) => {
    this.setState({
      [keyValue]: e.target.value.trim(),
    });
  };

  previewImage = (idx, keyValue) => {
    const { fileUrlJsonArray = {} } = this.state;
    const urls =
      fileUrlJsonArray[keyValue].length &&
      fileUrlJsonArray[keyValue].map((item) => item.fileUrl);
    console.log("urls", urls);
    Taro.previewImage({ current: urls[idx], urls });
  };

  addImage = async (keyValue) => {
    const { fileUrlJsonArray = {} } = this.state;
    const chooseRes = await Taro.chooseImage({
      count: 5 - fileUrlJsonArray[`${keyValue}File`].length,
      sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ["camera", "album"],
    });
    if (chooseRes.errMsg == "chooseImage:ok") {
      console.log("chooseRes", chooseRes);
      const tempFilePath = chooseRes.tempFilePaths || [];
      Taro.showLoading({ title: "上传中...", mask: true });
      console.log("tempFilePath", tempFilePath);
      let url = "";
      if (tempFilePath.length > 0) {
        const { patientPid } = this.$router.params;
        tempFilePath.map(async (item) => {
          url = await Api.uploadImages(item, {
            pid: patientPid,
            grid: patientPid,
          });
          Taro.hideLoading();
          if (url) {
            console.log("11");
            const { fileUrlJsonArray = {} } = this.state;
            const item = { fileUrl: url };
            fileUrlJsonArray[`${keyValue}File`].push(item);

            this.setState({ fileUrlJsonArray });
          }
        });
      }
    }
  };

  deleteImage = (index, keyValue) => {
    let { fileUrlJsonArray } = this.state;
    fileUrlJsonArray[`${keyValue}`].splice(index, 1);
    this.setState({ fileUrlJsonArray });
  };

  attachFile = async (consultationId) => {
    const { fileList } = this.state;
    const requestArr = fileList.map((url) => {
      return Api.addFile({ consultationId, url });
    });
    return Promise.all(requestArr);
  };

  validateData() {
    const { fileUrlJsonArray = {} } = this.state;
    const requireList = [{ key: "medicalHistory", value: "基本病史" }];
    for (let item of requireList) {
      if (
        !this.state[item.key] &&
        fileUrlJsonArray[`${item.key}File`].length <= 0
      ) {
        Taro.showModal({
          title: "系统提示",
          icon: "error",
          content: `${item.value}不能为空`,
          showCancel: false,
        });
        return false;
      }
    }
    return true;
  }

  alertModalFlag = async (flag = false, value) => {
    await Taro.showModal({
      title: "系统提示",
      icon: "error",
      content: `${value}不能为空`,
      showCancel: false,
    });
    return false;
  };

  submit = async () => {
    if (!this.validateData()) return;
    const { doctorId, patientName, relationPatientId } = this.state;
    const { patientPid } = this.$router.params;
    let param = {
      relationPatientId,
      patientPid,
      patientName,
      doctorId,
      consultationType: "prescription",
    };

    const {
      medicalHistory,
      prescriptionJsonArray,
      diagnosisFemaleList,
      diagnosisMaleList,
      fileUrlJsonArray,
    } = this.state;
    let { female = [], male = [] } = prescriptionJsonArray;
    let diagnosisFemale = [];
    let diagnosisMale = [];
    console.log('prescriptionJsonArray', prescriptionJsonArray);
    const femalet = female.length > 0 && female.filter((item) => item.checked) || [];
    const malet = male.length > 0 && male.filter((item) => item.checked) || [];


    if (femalet.length == 0 && malet.length == 0) {
      Taro.showModal({
        title: "系统提示",
        icon: "error",
        content: `开方项目不能为空`,
        showCancel: false,
      });
      return;
    }
    prescriptionJsonArray.female = femalet;
    prescriptionJsonArray.male = malet;

    diagnosisFemaleList.map((item) => {
      console.log("item", item);
      if (item.checked) {
        diagnosisFemale.push(item.value);
      }
    });
    diagnosisMaleList.map((item) => {
      if (item.checked) {
        diagnosisMale.push(item.value);
      }
    });
    // if (diagnosisFemale.length <= 0 || diagnosisMale.length <= 0) {
    //   this.alertModalFlag(true, "疾病诊断");
    //   return;
    // }
    console.log("prescriptionJsonArray", prescriptionJsonArray);
    if (diagnosisFemale.length === 0 && diagnosisMale.length === 0) {
      Taro.showModal({
        title: "系统提示",
        icon: "error",
        content: `男方和女方疾病诊断不能同时为空`,
        showCancel: false,
      });
      return false;
    }

    const { code, data } = await Api.createConsultation(param);
    if (code == 0) {
      const { orderNo, consultationId } = data;

      // await this.attachFile(consultationId);

      let applyParam = {
        orderNo,
        medicalHistory,
        prescriptionJsonArray: JSON.stringify(prescriptionJsonArray),
        diagnosisFemale: diagnosisFemale.join(","),
        diagnosisMale: diagnosisMale.join(","),
        fileUrlJsonArray: JSON.stringify(fileUrlJsonArray),
      };
      const { code: applyCode } = await Api.applyConsultation(applyParam);
      if (applyCode == 0) {
        Taro.showToast({
          title: "提交成功",
          icon: "success",
          duration: 1000,
        });
        setTimeout(function () {
          Taro.navigateTo({
            url: `/pages/patient/bill/billrecord/index?id=${consultationId}&isNew=1`,
          });
        }, 1000);
      }
    }
  };

  checkboxChange = (e, idx, keyValue) => {
    const { prescriptionJsonArray = {} } = this.state;
    console.log('idx', idx);
    console.log('keyValue', keyValue);
    console.log('prescriptionJsonArray', prescriptionJsonArray);
    console.log('this', this);
    let list = [];
    if (keyValue == "female" || keyValue == "male") {
      // 对开单项目这种json字段做特殊处理
      list = prescriptionJsonArray[keyValue];
    } else {
      list = this.state[keyValue];
    }
    console.log('list111', list);

    list &&
      list.length > 0 &&
      list.map((item, index) => {
        if (idx === index) {
          item.checked = !item.checked;
        }
      });
    if (keyValue == "female" || keyValue == "male") {
      console.log('list', list);
      prescriptionJsonArray[keyValue] = list;
      this.setState({ prescriptionJsonArray: {...prescriptionJsonArray} });
    } else {
      this.setState({
        [keyValue]: list,
      });
    }
  };
  getInput(item1 = {}) {
    const {
      tittle = "",
      keyValue = "",
      placeholder = "",
      value = "",
      fileList = [],
      required = ''
    } = item1;
    const { fileUrlJsonArray = {} } = this.state;
    return (
      <View className={s.inputModule}>
        <View className={`${s.between}`}>
          <View className={`${s.mainSuit}`}>{ required && <Text className={s.colorRed} style={{ color: "red" }}>*</Text>}{tittle}</View>
          {fileUrlJsonArray[`${fileList}`].length < 6 && (
            <View
              className={`${s.patientName} ${s.medicalImgs}`}
              onClick={this.addImage.bind(this, keyValue)}
            >
              添加照片
            </View>
          )}
        </View>
        <Textarea
          value={value}
          onInput={this.inputMainSuit.bind(this, keyValue)}
          placeholder={placeholder}
          maxlength={100}
          className={`${s.textarea}`}
          autoHeight
        />
      </View>
    );
  }

  showPicList(fileList = [], keyValue) {
    return (
      <View className={s.fileContainer}>
        {fileList &&
          fileList.length > 0 &&
          fileList.map((item, index) => {
            return (
              <View key={item.fileUrl} className={s.fileContent}>
                <Image
                  mode="aspectFit"
                  src={item.fileUrl}
                  onClick={this.previewImage.bind(this, index, keyValue)}
                />
                <View
                  className={s.deleteBtn}
                  onClick={this.deleteImage.bind(this, index, keyValue)}
                >
                  ×
                </View>
              </View>
            );
          })}
      </View>
    );
  }

  getOnlyInput({ tittle, placeholder, keyValue, value, required }) {
    return (
      <View className={`${s.rowModule}`}>
        <View className={`${s.mainSuit}`}>{ required && <Text className={s.colorRed} style={{ color: "red" }}>*</Text>}{tittle}</View>
        <View className={s.patientName}>
          <Input
            type="text"
            style={{ textAlign: "right" }}
            placeholder={placeholder}
            // focus
            onInput={(e) => this.inputMainSuit.bind(this, keyValue)}
          />
        </View>
      </View>
    );
  }

  render() {
    const {
      mainSuit,
      patientName,
      fileList,
      fileUrlJsonArray = {},
      prescripListF = [],
      prescripList = [],
      prescripListM = []
    } = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={`${s.tip}`}>
          注：为便于平台医生能提前掌握患者病情，更有效的提供服务，请详细描述患者相关信息。提交申请后，平台会审核，审核结果将发送给您，请您及时留意。
        </View>
        <View className={`${s.rowModule}`}>
          <View className={`${s.mainSuit}`}>患者</View>
          <View className={s.patientName}>{patientName}</View>
        </View>

        {this.getInput({
          tittle: "基本病史",
          keyValue: "medicalHistory",
          placeholder: "含现病史、婚育史、月经史、既往病史等",
          value: "",
          fileList: "medicalHistoryFile",
          required: true
        })}

        {this.showPicList(
          fileUrlJsonArray.medicalHistoryFile,
          "medicalHistoryFile"
        )}

        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>{<Text className={s.colorRed} style={{ color: "red" }}>*</Text>}疾病诊断:</View>
          <View className={`${s.mainSuit}`}>女方:</View>
          <View className={s.patientName}>
            <View className={s.radioList}>
              {/* <RadioGroup> */}
              {this.state.diagnosisFemaleList.map((item, i) => {
                return (
                  <Label className={s.radioListLabel} for={i} key={i}>
                    <Checkbox
                      className={s.radioListRadio}
                      value={item.value}
                      checked={item.checked}
                      onClick={(e) =>
                        this.checkboxChange(e, i, "diagnosisFemaleList")
                      }
                    >
                      {item.text}
                    </Checkbox>
                  </Label>
                );
              })}
              {/* </RadioGroup> */}
            </View>
          </View>
          <View className={`${s.mainSuit}`}>男方:</View>
          <View className={s.patientName}>
            <View className={s.radioList}>
              {/* <RadioGroup> */}
              {this.state.diagnosisMaleList.map((item, i) => {
                return (
                  <Label className={s.radioListLabel} for={i} key={i}>
                    <Checkbox
                      className={s.radioListRadio}
                      value={item.value}
                      checked={item.checked}
                      onClick={(e) =>
                        this.checkboxChange(e, i, "diagnosisMaleList")
                      }
                    >
                      {item.text}
                    </Checkbox>
                  </Label>
                );
              })}
              {/* </RadioGroup> */}
            </View>
          </View>
        </View>

        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}><Text className={s.colorRed} style={{ color: "red" }}>*</Text>开单项目（可多选）:</View>
          <View className={`${s.mainSuit}`}>女方:</View>
          <View className={s.patientName}>
            <View className={s.radioList}>
              {/* <RadioGroup> */}
              {prescripList && prescripList.length > 0 && prescripList.map((item, i) => {
                if (item.sex === 'F' || item.sex === '') {
                  return (
                    <Label className={s.radioListLabel} for={i} key={i}>
                      <Checkbox
                        className={s.radioListRadio}
                        value={item.value}
                        checked={item.checked}
                        onClick={(e) => this.checkboxChange(e, i, "female")}
                      >
                        {item.prescriptionName}
                      </Checkbox>
                    </Label>
                  );
                } else{
                  return '';
                }
                
              })}
              {/* </RadioGroup> */}
            </View>
          </View>
          <View className={`${s.mainSuit}`}>男方:</View>
          <View className={s.patientName}>
            <View className={s.radioList}>
              {/* <RadioGroup> */} 
              {prescripList && prescripList.length > 0 && prescripList.map((item, i) => {
                if (item.sex === 'M' || item.sex === '') {
                  return (
                    <Label className={s.radioListLabel} for={i} key={i}>
                      <Checkbox
                        className={s.radioListRadio}
                        value={item.value}
                        checked={item.checked}
                        onClick={(e) => this.checkboxChange(e, i, "male")}
                      >
                        {item.prescriptionName}
                      </Checkbox>
                    </Label>
                  );
                } else { 
                  return '';
                }
                
              })}
              {/* </RadioGroup> */}
            </View>
          </View>
        </View>

        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>其他附件</View>
          <View className={s.fileContainer}>
            {fileUrlJsonArray.otherFile.length > 0 &&
              fileUrlJsonArray.otherFile.map((item, index) => {
                return (
                  <View key={item.fileUrl} className={s.fileContent}>
                    <Image
                      mode="aspectFit"
                      src={item.fileUrl}
                      onClick={this.previewImage.bind(this, index, "otherFile")}
                    />
                    <View
                      className={s.deleteBtn}
                      onClick={this.deleteImage.bind(this, index, "otherFile")}
                    >
                      ×
                    </View>
                  </View>
                );
              })}
            {fileUrlJsonArray.otherFile.length < 6 && (
              <View
                className={s.addBtn}
                onClick={this.addImage.bind(this, "other")}
              >
                +
              </View>
            )}
          </View>
        </View>
        <View className={`${s.suggestion}`} onClick={this.submit}>
          <Text>提交</Text>
        </View>
      </View>
    );
  }
}
