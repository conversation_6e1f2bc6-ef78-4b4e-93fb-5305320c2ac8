page {
  height: 100%;
  background: $color-bg;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .infoModule {
    background-color: #fff;
    width: 87%;
    margin: 20px 20px 0;
    border-radius: 10px;
    padding: 20px 30px 0 30px;

    .infoTitle {
      font-size: 36px;
      padding: 20px 0;
      font-weight: 600;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .imgCss {
      width: 36px;
      height: 36px;
      padding: 0 10px;
    }

  }

}

.rowModule {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #E8E8E8;
  .mainTxt{
    font-size: 26rpx;
    color:#989898;
    margin-top: 10rpx;
    word-break: break-all;
  }

  text {
    color: #989898;
    font-size: 26px;
  }

  .rowTitle {
    font-size: 30px;
    font-weight: 600;
  }

}

.itemArrow{
  width: 17rpx;
  height: 17rpx;
  border-right: 5rpx solid #C7C7CC;
  border-bottom: 5rpx solid #C7C7CC;
  transform: translateX(-8rpx) rotate(-45deg);
}

.illnessDescription {
  background-color: #fff;
  width: 87%;
  margin: 40px 20px;
  border-radius: 10px;
  padding: 20px 30px;

  .illText {
    background-color: #fff;
    font-size: 26px;
    border-top: 1px solid #E5E5E5;
    padding-top: 30px;
  }
}

.reportItems {
  display: flex;
  background-color: #fff;
  width: 90%;
  justify-content: space-around;
  padding: 30px 0 20px 0;
  text-align: center;
  margin: auto;
  border-radius: 15px;
}

.imgCss {
  width: 36px;
  height: 36px;
  padding: 0 10px;
}

.reportName {
  font-size: 30px;
  font-weight: 550;
}
