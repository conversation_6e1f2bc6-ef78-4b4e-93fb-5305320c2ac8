.notice {
    padding: 20rpx 24rpx;
    background: #FFFAF1;
    color: #BE8014;
    font-size: 24px;
    font-weight: 400;
    white-space: nowrap;
    display: flex;
    justify-content: center;
}

.page {
    background: #FFF;
    margin: 16rpx 24rpx;
    padding: 0 32rpx;
    border-radius: 20rpx;
    box-shadow: 0 1rpx 4rpx 0 rgba(0, 0, 0, 0.12);
}

.item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 40rpx;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #e5e5e5;
    font-size: 32rpx;

    &_title {
        color: #000;
        font-weight: 400;
    }

    &_wxaddress {
        font-size: 24rpx;
        color: #333;
        border: 1rpx solid #999;
        border-radius: 30rpx;
        width: 140rpx;
        text-align: center;
        padding: 5rpx;
    }

    &_label {
        color: #000;
        font-weight: 400;
        width: 150rpx;
    }

    &_input {
        flex: 1 1 auto;
        color: #333;
    }

    &_arrow {
        width: 32rpx;
        height: 32rpx;
    }
}

.btn {
    background: #FFF;
    margin: 16rpx 24rpx;
    padding: 32rpx;
    border-radius: 20rpx;
    box-shadow: 0 1rpx 4rpx 0 rgba(0, 0, 0, 0.12);

    &_del {
        color: $color-primary;
        display: flex;
        justify-content: center;
    }
}

.foot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 40rpx;
    padding: 30rpx 0;

    &_switch {
        transform-origin: 0 30%;
        transform: scale(.7);
    }

    &_text {
        font-weight: 400;
        color: #000;
        margin-left: -20rpx;
    }
}

.bottom {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    display: flex;

    &_btn {
        margin: 40rpx 40rpx 70rpx 40rpx;
        background: $color-primary;
        width: 100%;
        color: $color-white;
    }
}