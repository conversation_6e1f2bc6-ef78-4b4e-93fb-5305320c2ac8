.pageVideo {
  .videoBox {
    .videoItem {
      width: 33%;
      max-width: 33%;
      min-width: 33%;
      height: 250px;
      .videoPoster {
        height: 100%;
        width: 100%;
        position: relative;
        .posterMask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 99;
          background: rgba(0, 0, 0, 0.4);
          color: $color-white;
          font-size: 34px;
          .posterDots {
            text {
              width: 10px;
              height: 10px;
              margin-right: 10px;
              background: #d8d8d8;
            }
            .dots2 {
              background: #c0c0c0;
            }
            .dots3 {
              background: #848484;
              margin: 0;
            }
          }
        }
      }
      // &.video-player{
      //     height: 100%;
      //     width: 100%;
      // }
      // &.video-pusher{
      //     height: 100%;
      //     width: 100%;
      // }
      cover-view {
        height: 100%;
        width: 100%;
      }
    }
    .videoFull {
      position: fixed;
      height: 100%;
      width: 100%;
      z-index: 99999;
      max-width: 100%;
    }
    .videoHide {
      height: 0px;
    }
  }
  .optBox {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 280px;
    z-index: 9999;
    background: #fff;
    padding: 60px 115px;
    .optItem {
      color: $color-title;
      font-size: 26px;
      image {
        height: 86px;
        width: 86px;
      }
    }
    .optEnd {
      height: 100px;
      width: 100px;
    }
  }
}
