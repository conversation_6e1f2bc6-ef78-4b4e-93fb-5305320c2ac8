import Taro, { Component } from '@tarojs/taro';
import {View, Textarea, Text} from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';


export default class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      consultationId: '', // 会诊id
      type: 0, // 0会诊意见 1会诊报告
      title: '会诊意见',
      editable: true,
      suggestion: '',
    };
  }

  componentDidMount() {
    const {consultationId, type} = this.$router.params;
    const title = +type === 0 ? '会诊意见':'会诊报告';
    Taro.setNavigationBarTitle({ title});
    this.setState({consultationId, type, title}, this.queryReport);
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '会诊意见',
    navigationBarTextStyle: 'black'
  };

  queryReport = async () => {
    const {consultationId, type: reportType} = this.state;
    const param = {
      consultationId,
      reportType,
    };
    const { code, data } = await Api.queryReport(param);
    if (code === 0 && data && data.length) {
      const {userId: doctorId = ''} = JSON.parse(Taro.getStorageSync('userInfo') || '{}');
      const reportItem = data.find(item => +item.doctorId === +doctorId) || {};
      this.setState({suggestion: reportItem.report, editable: !reportItem.report});
    }
  };

  addReport = async () => {
    const {consultationId, type: reportType, suggestion: content} = this.state;
    const param = {
      consultationId,
      reportType,
      content
    };
    const { code } = await Api.addReport(param);
    if (code === 0) {
      Taro.showToast({
        title: '添加成功', //提示的内容,
        icon: 'success' //图标,
      });
      setTimeout(Taro.navigateBack, 2000);
    }
  };

  submit = () => {
    const {title} = this.state;
    Taro.showModal({
      title: '提示',
      content: `${title}提交后不可再更改，请确保描述的准确性。是否确认提交？`,
      success: (val) => {
        console.log(val);
        if (val) {
          this.addReport();
        }
      }
    })
  };

  inputEvent = (e) =>{
    this.setState({
      suggestion:e.target.value
    })
  };

  render() {
    const {
      suggestion,
      editable,
      title
    } = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={s.title}>
          医生{title}
        </View>
        <View className={`${s.suggest}`}>
          <View className={`${s.suggestTitle}`}><Text>*</Text>我的{title}</View>
          {
            editable ? (
              <View>
                <Textarea
                  className={`${s.textarea}`}
                  maxlength={1000}
                  value={suggestion}
                  onInput={(e) => this.inputEvent(e)}
                  placeholder={`请填写您的${title}`}
                  placeholderStyle={{color: '#b2b2b2'}}
                />
                <View className={`${s.textNum}`}>{`${suggestion.length}/1000`}</View>

                <View className={`${s.actionButton}`}>
                  <View className={`${s.cancel}`} onClick={()=>Taro.navigateBack()}>取消</View>
                  <View className={`${s.submit}`} onClick={this.submit}>提交</View>
                </View>
              </View>
            ) : (
              <View>
                {suggestion}
              </View>
            )
          }
        </View>
      </View>
    );
  }
}
