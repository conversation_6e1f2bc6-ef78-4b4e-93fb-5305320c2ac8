import { View } from '@tarojs/components'
import Taro, { Component } from '@tarojs/taro'
import * as Api from '@/pages/index/api'

const CONFIG = {
  /** 游客活动列表 pages/guest/index?t=1 */
  1: '/pages/guest/activity-list/index',
  /** 游客活动详情 pages/guest/index?t=2&id=13 */
  2: '/pages/classroom/Child/Activity/detail',
  /** 注册申请列表 pages/guest/index?t=3&id=1 */
  3: '/package1/regist/detail/index',
}

const transformParams = query => Object.entries(query).reduce((res, [key, val]) => {
  return [...res, `${key}=${val}`]
}, []).join('&')

const parseUrlParamsString = urlParamsString => urlParamsString.split('&').reduce((res, item) => {
  const [key, val] = item.split('=')
  res[key] = val
  return res
}, {})


export default class Guest extends Component {

  componentWillMount() {
    this.authorize();
  }

  async getWxUserinfo() {
    return new Promise((resolve, reject) => {
      Taro.getUserInfo({
        success: resolve,
        fail: reject,
      })
    })
  }

  async authorize() {
    Taro.showLoading({ title: '加载中', mask: true });
    try {
      const { code } = await Taro.login();
      const { iv, encryptedData } = await this.getWxUserinfo();
      const res = await Api.authorize({ code, encryptedData, iv });
      if (res.code !== 0) return
      Taro.setStorageSync('login_access_token', res.data.login_access_token);
      this.redirect()
    } finally {
      Taro.hideLoading();
    }
  }

  redirect() {
    let { scene } = this.$router.params
    scene = decodeURIComponent(scene)
    scene = parseUrlParamsString(scene)
    const { t, ...rest } = scene
    const path = CONFIG[t] || '/pages/patient/index'
    const queryString = transformParams(rest)
    Taro.reLaunch({ url: `${path}?${queryString}` })
  }

  render() {
    return <View></View>
  }

}
