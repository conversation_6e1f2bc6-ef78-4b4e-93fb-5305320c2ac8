.container {
  margin-bottom: 150px;

  .title {
    background-color: #3ECEB6;
    color: #fff;
    font-size: 34px;
    padding: 20px;
    // position: fixed;
    width: 100%;
  }
}


.illnessDescription {
  background-color: #fff;
  padding: 10px 30rpx;

  .illText {
    background-color: #fff;
    font-size: 26px;
    border-top: 1px solid #E5E5E5;
    padding-top: 30px;
  }

}

.rowModule {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #E8E8E8;

  text {
    color: #989898;
    font-size: 26px;
  }

  .rowTitle {
    font-size: 30px;
    font-weight: 600;
    color: #2D2D2D;

    text {
      color: #989898;
      padding-left: 30px;
    }
  }
}


.suggest {
  background-color: #fff;
  margin-top: 20px;
  padding: 30px 20px;

  .suggestTitle {
    font-size: 34px;
    color: #2D2D2D;
    font-weight: 550;
  }

  span {
    color: red;
  }

  .textarea {
    height: 300px;
    font-size: 30px;
    color: #666;
    padding: 20px 10px;
  }

  .textNum{
    text-align: right;
  }
}

.actionButton {
  display: flex;
  position: fixed;
  margin: auto;
  left: 0;
  right: 0;
  width: 90%;
  bottom: 40px;
  justify-content: space-between;

  .submit {
    background-color: #3ECEB6;
    color: #fff;
    width: 45%;
    padding: 10px;
    border-radius: 10px;
    font-size: 36px;
    text-align: center;
  }

  .cancel {
    background-color: #fff;
    color: #2D2D20;
    width: 45%;
    padding: 10px;
    border-radius: 10px;
    font-size: 36px;
    text-align: center;
  }
}
