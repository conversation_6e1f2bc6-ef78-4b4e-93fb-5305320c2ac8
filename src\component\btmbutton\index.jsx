import Taro, { Component } from '@tarojs/taro';
import { View, Button, Form, Block } from '@tarojs/components';
import cn from 'classnames';
import * as Api from './api';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.innerStyle = {};

    this.isIpx = (Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_hrd_ipx`) || '').toString() === '1'; // 1是2否

    if (this.isIpx) {
      this.innerStyle.bottom = Taro.pxTransform($IPX_BOTTOM_HEIGHT * 1);
    }
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  formSubmit = (e = {}) => {
    const { onClick = () => {} } = this.props;
    onClick(e);
    // 收集formid
    const { formId } = e.detail || {};
    if (formId) {
      Api.saveFormId({ formId });
    }
  }

  render () {
    const { text, onClick, show = true, formType = 'buttonclick', disabled = false } = this.props;
    if (!show) {
      return null;
    }

    return (
      <View className='wgt-botton_btm'>
        <View className='button-box' style={this.innerStyle}>
          <View className={cn('button', { disabled })}>{text}</View>
          {
            !disabled ?
              <Block>
                {
                  formType === 'submit' ?
                    <Form onSubmit={this.formSubmit} reportSubmit>
                      <Button className='button-real' formType={formType}>{text}</Button>
                    </Form>
                    :
                    <Button className='button-real' formType={formType} onClick={onClick ? onClick : () => {}}>{text}</Button>
                }
              </Block> : null
          }
        </View>
      </View>
    )
  }
}
