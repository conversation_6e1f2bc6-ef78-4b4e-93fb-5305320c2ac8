.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30px;
}

/* 样本编号卡片样式 */
.sampleCard {
  background-color: #fff;
  border-radius: 16px;
  padding: 30px 25px;
  margin-bottom: 25px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.cardTitle {
  font-size: 34px;
  color: #999;
  margin-bottom: 25px;
  font-weight: normal;
}

.barcodeArea {
  margin: 35px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.barcodeImage {
  width: 450px;
  height: 90px;
}

.sampleInfoText {
  font-size: 42px;
  color: #333;
  font-weight: 500;
  margin: 35px 0 18px 0;
}

.scanTip {
  font-size: 32px;
  color: #999;
}

/* 扫描输入区域样式 */
.scanInputArea {
  padding: 50px 30px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.scanArea {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.scanMainArea {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scanIcon {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scanIconBg {
  width: 160px;
  height: 160px;
  border: 3px dashed #30A1A6;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(48, 161, 166, 0.08) 0%, rgba(48, 161, 166, 0.15) 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(48, 161, 166, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: #2a9196;
    background: linear-gradient(135deg, rgba(48, 161, 166, 0.15) 0%, rgba(48, 161, 166, 0.25) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(48, 161, 166, 0.2);

    &::before {
      opacity: 1;
    }
  }
}

.scanIconInner {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cameraIcon {
  width: 60px;
  height: 60px;
  background: #30A1A6;
  border-radius: 12px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
    border: 3px solid #fff;
    border-radius: 50%;
  }

  &::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 8px;
    background: #fff;
    border-radius: 4px 4px 0 0;
  }
}

.scanText {
  font-size: 28px;
  color: #30A1A6;
  font-weight: 600;
  margin-top: 20px;
  letter-spacing: 1px;
}

.dividerSection {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 300px;
  margin: 20px 0;
}

.dividerLine {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, #e0e0e0, transparent);
}

.dividerText {
  font-size: 24px;
  color: #999;
  margin: 0 20px;
  font-weight: 500;
}

.inputSection {
  width: 100%;
  max-width: 400px;
}

.codeInput {
  width: 100%;
  height: 88px;
  border: 2px solid #e8e8e8;
  border-radius: 16px;
  padding: 0 24px;
  font-size: 28px;
  text-align: center;
  background: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  &:focus {
    border-color: #30A1A6;
    outline: none;
    box-shadow: 0 4px 16px rgba(48, 161, 166, 0.15);
    transform: translateY(-1px);
  }

  &::placeholder {
    color: #bbb;
    font-size: 26px;
  }
}

/* 条码显示区域样式 */
.barcodeDisplayArea {
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

/* 输入内容显示区域样式 */
.inputDisplayArea {
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.inputDisplayContainer {
  background: rgba(48, 161, 166, 0.1);
  border: 2px solid #30A1A6;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  text-align: center;
}

.inputDisplayText {
  font-size: 28px;
  color: #30A1A6;
  font-weight: 600;
}

.clearButton {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border-radius: 12px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.clearButton:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
}

.clearText {
  color: white;
  font-size: 26px;
  font-weight: 600;
}

.barcodeContainer {
  background: #fff;
  border-radius: 14px;
  padding: 25px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  width: 100%;
  max-width: 450px;
}

.barcodeImage {
  width: 100%;
  max-width: 400px;
  height: auto;
  min-height: 80px;
}

.codeTextContainer {
  text-align: center;
  margin-top: 15px;
}

.codeText {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  letter-spacing: 2px;
  word-break: break-all;
}

.codeLabel {
  font-size: 26px;
  color: #666;
}

.rescanButton {
  background: linear-gradient(135deg, #30A1A6 0%, #2a9196 100%);
  border-radius: 12px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(48, 161, 166, 0.2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(48, 161, 166, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.rescanText {
  color: #fff;
  font-size: 24px;
  font-weight: 500;
}

/* 功能按钮卡片样式 */
.functionCard {
  margin: 18px 30px;
  background-color: #fff;
  border-radius: 18px;
  padding: 30px 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.functionButtons {
  display: flex;
  justify-content: space-around;
}

.functionBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 33.33%;
  padding: 18px 0;
}

.functionIcon {
  width: 95px;
  height: 95px;
  border-radius: 47px;
  margin-bottom: 12px;
  background-size: 60% 60%;
  background-position: center;
  background-repeat: no-repeat;
}

.basicInfoIcon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2330A1A6"><path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V6h16v12z"/><path d="M4 0h16v2H4z"/><path d="M7 10h10v2H7z"/><path d="M7 14h7v2H7z"/></svg>');
}

.surveyIcon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2330A1A6"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"/></svg>');
}

.consentIcon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2330A1A6"><path d="M18 2h-8L4.02 8 4 20c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-6 6h-2V4h2v4zm3 0h-2V4h2v4zm3 0h-2V4h2v4z"/></svg>');
}

.functionText {
  font-size: 28px;
  color: #333;
  text-align: center;
}

.statusBadge {
  position: absolute;
  top: 2px;
  right: 8px;
  background-color: #FF5151;
  color: #FFFFFF;
  font-size: 22px;
  padding: 3px 10px;
  border-radius: 11px;
  transform: translateY(-50%);
  z-index: 2;
  white-space: nowrap;
}

/* 进度流程样式 */
.processCard {
  background-color: #fff;
  border-radius: 18px;
  padding: 35px 30px;
  margin: 18px 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.processItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 35px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 28px;
    top: 56px;
    width: 2px;
    height: 35px;
    background-color: #e5e5e5;
  }
}

.processNumber {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  font-weight: bold;
  margin-right: 22px;
  flex-shrink: 0;

  &.completed {
    background-color: #30A1A6;
    color: #fff;
  }

  &.pending {
    background-color: #e5e5e5;
    color: #999;
  }
}

.processContent {
  flex: 1;
  padding-top: 10px;
}

.processTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 34px;
  color: #333;
}

.processStatus {
  font-size: 30px;
  color: #30A1A6;

  &.pending {
    color: #999;
  }
}

.processTime {
  font-size: 24px;
  color: #666;
  margin-top: 8px;
  line-height: 1.4;
}

/* 查看报告按钮样式 */
.actionButton {
  width: calc(100% - 60px);
  height: 100px;
  line-height: 100px;
  text-align: center;
  background: linear-gradient(135deg, #30a1a6 0%, #00c6b8 100%);
  color: #ffffff;
  font-size: 38px;
  font-weight: 500;
  border-radius: 50px;
  box-shadow: 0 8px 16px rgba(0, 185, 204, 0.2);
  margin: 35px 30px;
  cursor: pointer;
  transition: all 0.3s;

  &:active {
    opacity: 0.9;
    transform: scale(0.98);
  }

  &.disabled {
    background: linear-gradient(135deg, #b0b0b0 0%, #d0d0d0 100%);
    box-shadow: none;
    opacity: 0.8;
    cursor: not-allowed;

    &:active {
      opacity: 0.8;
      transform: none;
    }
  }
}

/* 基础信息弹窗样式 */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalContainer {
  width: 90%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px;
  background-color: #30A1A6;
  color: #fff;
}

.modalTitle {
  font-size: 36px;
  font-weight: 500;
}

.modalClose {
  font-size: 48px;
  cursor: pointer;
  line-height: 1;
  padding: 5px;
}

.modalContent {
  padding: 30px;
}

.infoRow {
  display: flex;
  margin-bottom: 25px;
  align-items: center;

  &:last-child {
    margin-bottom: 0;
  }
}

.infoLabel {
  width: 160px;
  font-size: 30px;
  color: #666;
  flex-shrink: 0;
}

.infoValue {
  flex: 1;
  font-size: 30px;
  color: #333;
  font-weight: 500;
}

/* 响应式设计 */
@media screen and (max-width: 750px) {
  .container {
    padding: 20px;
  }

  .sampleCard {
    padding: 30px 20px;
  }

  .cardTitle {
    font-size: 32px;
  }

  .barcodeImage {
    width: 350px;
    height: 70px;
  }

  .sampleInfoText {
    font-size: 36px;
  }

  .scanTip {
    font-size: 28px;
  }

  .scanInputArea {
    padding: 40px 20px;
    min-height: 250px;
  }

  .scanIconBg {
    width: 140px;
    height: 140px;
    border-radius: 20px;
  }

  .scanIconInner {
    width: 70px;
    height: 70px;
  }

  .cameraIcon {
    width: 50px;
    height: 50px;
    border-radius: 10px;

    &::before {
      width: 28px;
      height: 28px;
    }

    &::after {
      width: 14px;
      height: 6px;
      top: 6px;
    }
  }

  .scanText {
    font-size: 24px;
    margin-top: 16px;
  }

  .dividerText {
    font-size: 22px;
    margin: 0 16px;
  }

  .codeInput {
    height: 76px;
    font-size: 24px;
    padding: 0 20px;
    border-radius: 14px;

    &::placeholder {
      font-size: 22px;
    }
  }

  .barcodeDisplayArea {
    padding: 30px 15px;
    gap: 24px;
  }

  .inputDisplayArea {
    padding: 30px 15px;
    gap: 24px;
  }

  .inputDisplayContainer {
    padding: 24px;
    border-radius: 10px;
  }

  .inputDisplayText {
    font-size: 26px;
  }

  .clearButton {
    padding: 10px 20px;
    border-radius: 10px;
  }

  .clearText {
    font-size: 24px;
  }

  .barcodeContainer {
    padding: 30px 20px;
    border-radius: 14px;
    max-width: 420px;
  }

  .barcodeImage {
    max-width: 350px;
    min-height: 70px;
  }

  .codeTextContainer {
    margin-top: 16px;
  }

  .codeText {
    font-size: 30px;
    letter-spacing: 2px;
    margin-bottom: 10px;
  }

  .codeLabel {
    font-size: 24px;
  }

  .rescanButton {
    padding: 10px 20px;
    border-radius: 10px;
  }

  .rescanText {
    font-size: 22px;
  }

  .functionCard {
    margin: 12px 20px;
    padding: 25px 20px;
  }

  .functionButtons {
    justify-content: space-around;
  }

  .functionBtn {
    width: 30%;
    padding: 12px 5px;
  }

  .functionIcon {
    width: 75px;
    height: 75px;
    margin-bottom: 10px;
  }

  .functionText {
    font-size: 24px;
  }

  .statusBadge {
    font-size: 18px;
    padding: 2px 8px;
    border-radius: 9px;
  }

  .processCard {
    margin: 12px 20px;
    padding: 25px 20px;
  }

  .processNumber {
    width: 46px;
    height: 46px;
    font-size: 22px;
    margin-right: 18px;
  }

  .processTitle {
    font-size: 30px;
  }

  .processStatus {
    font-size: 26px;
  }

  .processTime {
    font-size: 22px;
    margin-top: 6px;
  }

  .actionButton {
    font-size: 34px;
    height: 90px;
    line-height: 90px;
    border-radius: 45px;
    margin: 25px 20px;
    width: calc(100% - 40px);
  }



  .modalContainer {
    width: 95%;
  }

  .modalHeader {
    padding: 20px;
  }

  .modalTitle {
    font-size: 30px;
  }

  .modalClose {
    font-size: 40px;
  }

  .modalContent {
    padding: 20px;
  }

  .infoRow {
    margin-bottom: 20px;
  }

  .infoLabel {
    width: 140px;
    font-size: 26px;
  }

  .infoValue {
    font-size: 26px;
  }
}

/* 知情同意书弹窗样式 */
.modalContent {
  width: 90%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modalBody {
  padding: 30px;
  max-height: 500px;
  overflow-y: auto;
}

.fileList {
  width: 100%;
}

.fileItem {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.fileInfo {
  flex: 1;
}

.fileName {
  font-size: 28px;
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.signDate {
  font-size: 24px;
  color: #666;
  line-height: 1.4;
}

.viewButton {
  background-color: #30A1A6;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 24px;
  font-weight: 500;
  margin-left: 15px;
  cursor: pointer;

  &:active {
    opacity: 0.8;
  }
}

.emptyState {
  text-align: center;
  padding: 50px 20px;
  color: #999;
  font-size: 28px;
}

.modalFooter {
  padding: 20px 30px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
}

.modalButton {
  background-color: #30A1A6;
  color: #fff;
  border: none;
  border-radius: 25px;
  padding: 12px 40px;
  font-size: 28px;
  font-weight: 500;
  cursor: pointer;

  &:active {
    opacity: 0.8;
  }
}

/* 响应式设计 - 知情同意书弹窗 */
@media screen and (max-width: 750px) {
  .modalContent {
    width: 95%;
  }

  .modalBody {
    padding: 20px;
    max-height: 400px;
  }

  .fileItem {
    padding: 15px;
    margin-bottom: 12px;
  }

  .fileName {
    font-size: 24px;
    margin-bottom: 6px;
  }

  .signDate {
    font-size: 22px;
  }

  .viewButton {
    padding: 8px 15px;
    font-size: 22px;
    margin-left: 10px;
  }

  .emptyState {
    padding: 40px 15px;
    font-size: 24px;
  }

  .modalFooter {
    padding: 15px 20px;
  }

  .modalButton {
    padding: 10px 30px;
    font-size: 26px;
  }
}
