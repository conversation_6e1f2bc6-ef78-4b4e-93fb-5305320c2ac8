page {
  height: 100%;
  background: $color-bg;
}
.container {
  font-size: 34px;
  .row {
    padding: 15px 30px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    background-color: $color-white;
    
    >text:last-child {
      max-width: 392px;
      text-align: right;
      color: $color-title;
      word-break: break-all;
    }
  }

  .patientDetail {
    overflow: hidden;
    height: 81px;

    .arrow {
      margin-left: 15px;
      transform: rotate(90deg);
      color: #989898;
      display: inline-block;
    }

    &.expand {
      .arrow {
        transform: rotate(-90deg);
      }
      height: auto;
    }
  }

  .attachFile {
    margin-top: 20px;
    align-items: center;

    .previewBtn {
      display: flex;
      align-items: center;
      >image {
        width: 54px;
        height: 54px;
        margin-right: 22px;
      }
      >text {
        color: $color-primary;
      }
    }
  }

  .record {
    margin-top: 20px;
    navigator {
      color: $color-primary;
    }
  }

  .imgbox{
    display: flex;
    .img{
      width: 100px;
      height: 100px;
    }
  }
}

.cancelBtn {
  margin: 85px 40px;
  background: $color-white;
  border-radius: 10px;
  height: 94px;
  line-height: 94px;
  text-align: center;
  font-size: 36px;
  color: $color-title;
}

.entryBtn {
  margin: 85px 40px;
  background: $color-primary;
  border-radius: 10px;
  height: 94px;
  line-height: 94px;
  text-align: center;
  font-size: 36px;
  color: $color-white;
}

.cardItem{
  padding: 15px 30px;
  box-sizing: border-box;
  flex-direction: column;
  display: flex;
  background-color: $color-white;
  .content{
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    word-break: break-all;
    background-color: rgb(248, 248, 248);
  }
}