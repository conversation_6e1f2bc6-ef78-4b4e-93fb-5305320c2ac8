import Taro, { Component } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';


export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '健康档案',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);
    this.state = {
      status: true,
      manInfo: { sex: "M" },
      femalInfo: { sex: "F" },
      description: "", //病情描述，在manInfo里面
      diseaseF: [], //女方疾病
      diseaseM: [], //男方疾病
      showHistoryF: [],
      showHistoryM: [],
    };
  }

  componentDidMount() {
    const { patientPid: pid } = this.props;
    this.getProfileInfo({ pid, sex: 'M' });
    this.getProfileInfo({ pid, sex: 'F' });
  }

  getProfileInfo = async (param = {}) => {
    let { data, code } = await Api.queryHealthRecord(param);
    const { sex } = param;
    if (code == 0 && data) {
      // if (data.basicInfo && (data.basicInfo.name || data.basicInfo.idNo)) {
      //   // 已保存基本信息
      //   data.isExistBasicInfo = true;
      // }
      if (sex === "M") {
        this.setState({
          manInfo: data,
          description: data.description
        })
        
        this.queryDiseaseListM();
        this.queryDiseaseHistoryM();
      } else {
        this.setState({
          femalInfo: data,
        })
        this.queryDiseaseListF();
        this.queryDiseaseHistoryF();
      }
    }
  }

  // 查询男方疾病
  queryDiseaseListM = async () => {
    const { code, data } = await Api.queryDiseaseList({ type: "m_disease_type" });
    const { manInfo } = this.state;
    let { questionRel = "", diseaseM = [] } = manInfo;
    questionRel = questionRel.split(",");
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < questionRel.length; j++) {
        if (data[i].id == questionRel[j]) {
          diseaseM.push(data[i].dictValue);
        }
      }
      this.setState({ diseaseM });
    }
  }
  // 查询女方疾病
  async queryDiseaseListF() {
    const { code, data } = await Api.queryDiseaseList({ type: "f_disease_type" });
    const { femalInfo } = this.state;
    let { questionRel = "", diseaseF = [] } = femalInfo;
    questionRel = questionRel.split(",");
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < questionRel.length; j++) {
        if (data[i].id == questionRel[j]) {
          diseaseF.push(data[i].dictValue);
        }
      }
      this.setState({ diseaseF });
    }
  }

  //查询病史
  async queryDiseaseHistoryF() {
    const { code, data } = await Api.queryDiseaseList({ type: "anamnesis_record " });
    const { femalInfo } = this.state;
    let { anamnesisRel = "" } = femalInfo;
    let showHistoryF = [];
    anamnesisRel = anamnesisRel.split(",");
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < anamnesisRel.length; j++) {
        if (data[i].id == anamnesisRel[j]) {
          showHistoryF.push(data[i].dictValue);
        }
      }
      this.setState({ showHistoryF });
    }
  }

  // 查询病史
  queryDiseaseHistoryM = async () => {
    const { code, data } = await Api.queryDiseaseList({ type: "anamnesis_record" });
    const { manInfo } = this.state;
    let { anamnesisRel = "" } = manInfo;
    let showHistoryM = [];
    anamnesisRel = anamnesisRel.split(",");
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < anamnesisRel.length; j++) {
        if (data[i].id == anamnesisRel[j]) {
          showHistoryM.push(data[i].dictValue);
        }
      }
      this.setState({ showHistoryM });
    }
  }

  illTextShow = () => {
    const { status } = this.state;
    this.setState({
      status: !status
    })
  }

  clickEvent = (val) => {
    // const { fReportList, mReportList } = this.state;
    let param = { status: val };
    // if (val) {
    //   param.reportList = mReportList;
    // } else {
    //   param.reportList = fReportList;
    // }
    this.setState(param)
  }

  render() {
    const {
      status,
      manInfo,
      femalInfo,
      diseaseF,
      diseaseM,
      showHistoryM,
      showHistoryF,
      description
    } = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={`${s.reportItems}`}>
          <View onClick={() => this.clickEvent(true)} style={{ opacity: status ? 1 : 0.5 }}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/man.png')} />
            <View className={`${s.reportName}`}>男方健康信息</View>
          </View>
          <View onClick={() => this.clickEvent(false)} style={{ opacity: !status ? 1 : 0.5 }}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/woman.png')} />
            <View className={`${s.reportName}`}>女方健康信息</View>
          </View>
        </View>
        { !status &&
        <View className={`${s.infoModule}`}>
          {/* <View className={`${s.infoTitle}`}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/woman.png')} />女方健康信息
          </View> */}
          <View className={`${s.rowModule}`}>
            <View>
              <View className={`${s.rowTitle}`}>基本信息</View>
              {femalInfo.basicInfo ?
                <View className={`${s.mainTxt}`}>
                  <text>{femalInfo.basicInfo.name || '暂无姓名'}/</text>
                  <text>{femalInfo.basicInfo.idNo || '暂无证件号码'}/</text>
                  <text>{femalInfo.basicInfo.profession || '暂无职业'}/</text>
                  <text>{femalInfo.basicInfo.nation || '暂无民族'}/</text>
                  <block><text>{femalInfo.basicInfo.height ? `${femalInfo.basicInfo.height}cm/` : '暂无身高'}</text></block>
                  <block><text>{femalInfo.basicInfo.weight ? `${femalInfo.basicInfo.weight}kg/` : '暂无体重'}</text></block>
                  <text>{femalInfo.basicInfo.address || '暂无地址'}/</text>
                  <text>{femalInfo.basicInfo.education || '暂无教育程度'}</text>
                </View> :
                <View className={`${s.mainTxt}`}>未填写</View>
              }
            </View>
            {/* <View>></View> */}
          </View>
          <View className={`${s.rowModule}`}>
            <View>
              <View className={`${s.rowTitle}`}>婚育史</View>
              {femalInfo.marriageRecord ?
                <View className={`${s.mainTxt}`}>
                  <block>
                    <text>{femalInfo.marriageRecord.hyzk || '暂无婚姻状况'}/</text>
                    <text>{femalInfo.marriageRecord.sys || '暂无生育史'}/</text>
                    <text>婚龄{femalInfo.marriageRecord.hl || ''}年</text>
                  </block>
                </View> :
                <View className={`${s.mainTxt}`}>未填写</View>
              }
            </View>
            {/* <View>></View> */}
          </View>
          <View className={`${s.rowModule}`}>
            <View>
              <View className={`${s.rowTitle}`}>月经史</View>
              {femalInfo.periodRecord ?
                <View className={`${s.mainTxt}`}>
                  <block>周期{femalInfo.periodRecord.yjzq || 0}</block>
                </View> :
                <View className={`${s.mainTxt}`}>未填写</View>
              }
            </View>
            {/* <View>
              <View className={`${s.itemArrow}`}></View>
            </View> */}
          </View>
          <View className={`${s.rowModule}`}>
            <View>
              <View className={`${s.rowTitle}`}>妇科是否有问题</View>
              {femalInfo.questionStatus == '1' &&
                <View className={`${s.mainTxt}`}>
                  {diseaseF.length > 0 &&
                    diseaseF.map((item, index) => {
                      return <block key={index}>
                        <text>{item}{index != diseaseF.length - 1 ? '/' : ''}</text>
                      </block>
                    })
                  }
                </View>}
              {femalInfo.questionStatus == '0' && <block>
                <View className={`${s.mainTxt}`}>
                  <text>没有问题</text>
                </View>
              </block>}
              {femalInfo.questionStatus == '2' && <block>
                <View className={`${s.mainTxt}`}>
                  <text>不清楚</text>
                </View>
              </block>}
              {(!femalInfo.questionStatus || femalInfo.questionStatus == 'null') && <View className={`${s.mainTxt}`}>未填写</View>}
            </View>
            {/* <View>></View> */}
          </View >
          <View className={`${s.rowModule}`} style={{ borderBottom: 'none' }}>
            <View>
              <View className={`${s.rowTitle}`}>既往病史</View>
              <View className={`${s.mainTxt}`}>
                {showHistoryF.length > 0 &&
                  showHistoryF.map((item, index) => {
                    return <block key={index}> {item}{index != showHistoryF.length - 1 ? '/' : ''}</block>
                  })
                }
              </View>
              {showHistoryF == 0 && <View className={`${s.mainTxt}`}>未填写</View>}
            </View>
            {/* <View>></View> */}
          </View>
        </View >
        }
        {
          status &&
        
        <View className={`${s.infoModule}`}>
          {/* <View className={`${s.infoTitle}`}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/man.png')} />男方健康信息
          </View> */}
          <View className={`${s.rowModule}`}>
            <View>
              <View className={`${s.rowTitle}`}>基本信息</View>
              {manInfo.basicInfo ?
                <View className={`${s.mainTxt}`}>
                  <text>{manInfo.basicInfo.name || '暂无姓名'}/</text>
                  <text>{manInfo.basicInfo.idNo || '暂无证件号码'}/</text>
                  <text>{manInfo.basicInfo.profession || '暂无职业'}/</text>
                  <text>{manInfo.basicInfo.nation || '暂无民族'}/</text>
                  <block><text>{manInfo.basicInfo.height ? `${manInfo.basicInfo.height}cm/` : '暂无身高'}</text></block>
                  <block><text>{manInfo.basicInfo.weight ? `${manInfo.basicInfo.weight}kg/` : '暂无体重'}</text></block>
                  <text>{manInfo.basicInfo.address || '暂无地址'}/</text>
                  <text>{manInfo.basicInfo.education || '暂无教育程度'}</text>
                </View> :
                <View className={`${s.mainTxt}`}>未填写</View>
              }
            </View>
            {/* <View>></View> */}
          </View>
          <View className={`${s.rowModule}`}>
            <View>
              <View className={`${s.rowTitle}`}>婚育史</View>
              {manInfo.marriageRecord ?
                <View className={`${s.mainTxt}`}>
                  <block>
                    <text>{manInfo.marriageRecord.hyzk || '暂无婚姻状况'}/</text>
                    <text>{manInfo.marriageRecord.sys || '暂无生育史'}/</text>
                    <text>婚龄{manInfo.marriageRecord.hl || ''}年</text>
                  </block>
                </View> :
                <View className={`${s.mainTxt}`}>未填写</View>
              }
            </View>
            {/* <View>></View> */}
          </View>
          <View className={`${s.rowModule}`}>
            <View>
              <View className={`${s.rowTitle}`}>小蝌蚪是否有问题</View>
              {manInfo.questionStatus == '1' &&
                <View className={`${s.mainTxt}`}>
                  {diseaseM.length > 0 &&
                    diseaseM.map((item, index) => {
                      return <block key={index}>
                        <text>{item}{index != diseaseM.length - 1 ? '/' : ''}</text>
                      </block>
                    })
                  }
                </View>
              }
              {manInfo.questionStatus == '0' &&
                <block >
                  <View className={`${s.mainTxt}`}>
                    <block>没有问题</block>
                  </View>
                </block>
              }
              {manInfo.questionStatus == '2' &&
                <block >
                  <View className={`${s.mainTxt}`}>
                    <block>不清楚</block>
                  </View>
                </block>
              }
              {(!manInfo.questionStatus || manInfo.questionStatus == 'null') &&
                <View className={`${s.mainTxt}`}>未填写</View>
              }
            </View>
            {/* <View>></View> */}
          </View >
          <View className={`${s.rowModule}`} style={{ borderBottom: 'none' }}>
            <View>
              <View className={`${s.rowTitle}`}>既往病史</View>
              {showHistoryM.length > 0 &&
                <View className={`${s.mainTxt}`}>
                  {showHistoryM.map((item, index) => {
                    return <block key={index}>
                      {item}{index != showHistoryM.length - 1 ? '/' : ''}
                    </block>
                  })
                  }
                </View>
              }
              {showHistoryM.length == 0 && <View className={`${s.mainTxt}`}>未填写</View>}
              {/* <View>></View> */}
            </View>
          </View>
        </View >
        }
        <View className={`${s.illnessDescription}`} >
          <View onClick={this.illTextShow} className={`${s.rowModule}`} style={{ borderBottom: 'none' }}>
            <View className={`${s.rowTitle}`}>病情描述</View>
            <View>{status ? 'v' : '>'}</View>
          </View>
          {status && <View className={`${s.illText}`}>{description?description:'暂无'}</View>}
        </View >
      </View >
    );
  }
}
