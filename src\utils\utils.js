import Taro from '@tarojs/taro';
import { HC_HEADER_CONFIG, IS_NEED_ENDTIME, TIME_MAP } from './config';

/**
 * 操作授权检验、请求
 * @param scopeName
 * @returns {*}
 */
export const getAuthorize = async(scopeName) => {
  let getSettingRes;
  try {
    getSettingRes = await Taro.getSetting();
  } catch (e) {
    getSettingRes = e;
  }

  if (getSettingRes.errMsg !== 'getSetting:ok') {
    // 调用授权列表接口失败
    return getSettingRes;
  }

  // 调用授权列表接口成功
  const { authSetting = {} } = getSettingRes;
  if (!authSetting[scopeName]) {
    // 尚未授权
    let authorizeRes;
    try {
      authorizeRes = await Taro.authorize({
        scope: scopeName,
      });
    } catch (e) {
      authorizeRes = e;
    }

    if (authorizeRes.errMsg !== 'authorize:ok') {
      if (scopeName == 'scope.userInfo') {
        return authorizeRes;
      }
      // 用户拒绝授权
      console.log('authorizeRes_fail:', authorizeRes);
      let openSettingRes;
      try {
        openSettingRes = await Taro.openSetting();
      } catch (e) {
        openSettingRes = e;
      }

      if (openSettingRes.errMsg !== 'openSetting:ok') {
        // 调用打开授权设置页失败
        console.log('openSettingRes_fail:', openSettingRes);
        return openSettingRes;
      }

      // 调用打开授权设置页并且设置成功，需要重新校验权限是否授予
      return await getAuthorize(scopeName);
    }

    // 用户授权成功
    return { errMsg: 'ok' };
  } else {
    // 已经获得授权
    return { errMsg: 'ok' };
  }
};

/**
 * 网络请求
 * @param
 * @returns {Promise}
 */
export const httpRequest = ({ url, data = {} }) => {
  return new Promise((resolve) => {
    const reqPages = Taro.getCurrentPages() || [];
    const reqPage = (reqPages[reqPages.length - 1] || {}).route || '';
    Taro.request({
      url,
      data,
      credentials: 'include',
      method: data.method || 'POST',
      header: { ...(data.header || { "content-type": "application/x-www-form-urlencoded" }), ...(HC_HEADER_CONFIG || {}) },
      success: (response = {}) => {
        const resPages = Taro.getCurrentPages() || [];
        const resPage = (resPages[resPages.length - 1] || {}).route || '';
        response.reqPage = reqPage;
        response.resPage = resPage;
        resolve(response);
      },
      fail: () => {
        const resPages = Taro.getCurrentPages() || [];
        const resPage = (resPages[resPages.length - 1] || {}).route || '';
        resolve({
          status: 600,
          reqPage,
          resPage
        });
      }
    });
  });
};

/**
 * 获取用户基本信息
 * @returns {*}
 */
export const getUserInfo = async () => {
  // 授权成功
  let getUserInfoRes;
  try {
    getUserInfoRes = await Taro.getUserInfo();
    Taro.setStorageSync('appUserInfo', getUserInfoRes);
  } catch (e) {
    getUserInfoRes = e;
  }

  // 获取用户信息成功
  const { userInfo = {} } = getUserInfoRes;
  Taro.setStorageSync('appUserInfo', userInfo);
  return getUserInfoRes;
};

/**
 * json对象转queryString键值对
 * @param json
 * @returns {*}
 */
export const jsonToQueryString = (json) => {
  if (json) {
    return Object.keys(json).map((key) => {
      if (json[key] instanceof Array) {
        return Object.keys(json[key]).map((k) => {
          return `${key}=${json[key][k]}`;
        }).join('&');
      }
      return `${key}=${json[key]}`;
    }).join('&');
  }
  return '';
};

/**
 * 格式化金额
 * @param moneyString
 * @param mark
 * @returns {*}
 */
export const formatMoney = (moneyString = '0', mark = 100) => {
  try {
    const moneyNumber = parseFloat(moneyString);
    if (typeof moneyNumber === 'number' && typeof mark === 'number') {
      return parseFloat(moneyNumber / mark).toFixed(2);
    }
    return 0;
  } catch (e) {
    console.log('error', e); // 缺失全局异常处理
    return 0;
  }
};

/**
 * 获取格式化日期
 * @param dateGap {Number} 日期间隔
 * @param spacer {String} 间隔符
 * @returns {Date} {String} 返回的格式化后的日期
 */
export const getFormatDate = (dateGap = 0, spacer = '/') => {
  let date = new Date();
  date = new Date(date.setDate(date.getDate() + dateGap));
  date = date.getFullYear() + spacer + (date.getMonth() + 1) + spacer + (date.getDate());
  return date.replace(/\b(\w)\b/g, '0$1');
};
/**
 * 根据出生日期获取生日
 * @param birthday {string} 出生日期 如：2018-09-29 或者 2018/09/29
 * @param spacer {String} 间隔符
 * @returns {Date} 返回的年龄
 */
export const getAgeByBirthday = (birthday = '', spacer = '/') => {
  if (!birthday) {
    console.log('获取年龄错误，出生日期不能为空');
    return 0;
  }
  const nowArr = getFormatDate(0, spacer).split(spacer);
  const birthArr = birthday.split(spacer);
  let yearCacl = nowArr[0] - birthArr[0];
  const monthCacl = nowArr[1] - birthArr[1];
  const dayCacl = nowArr[2] - birthArr[2];

  if(yearCacl < 0){
    console.log('获取年龄错误，出生日期不能大于当前日期');
    return 0;
  }
  if(monthCacl > 0){
    yearCacl += 1;
  } else if(monthCacl === 0){
    if(dayCacl >= 0){
      yearCacl += 1;
    }
  } else {
    yearCacl -= 1;
  }
  return yearCacl;
};
/**
 * 根据身份证获取出生日期
 * @param idno {string} 身份证号
 * @param spacer {String} 间隔符
 * @returns {Date} 返回的年龄
 */
export const getBirthdayByIdCard = (idno = '', spacer = '/') => {
  if (!idno) {
    console.log('获取年龄错误，身份证号不能为空');
    return '';
  } else if(idno.length !== 18){
    console.log('获取年龄错误，身份证号长度错误，必须18位');
    return '';
  }

  return `${idno.substring(6, 10)}${spacer}${idno.substring(10, 12)}${spacer}${idno.substring(12, 14)}`;
};
/**
 * 根据身份证获取性别
 * @param idno {string} 身份证号
 * @param spacer {String} 间隔符
 * @returns {String} 返回的性别
 */
export const getSexByIdCard = (idno = '') => {
  if (!idno) {
    console.log('获取性别错误，身份证号不能为空');
    return '';
  } else if(idno.length !== 18){
    console.log('获取性别错误，身份证号长度错误，必须18位');
    return '';
  }

  const sexStr = idno.substring(16, 17);

  if (!/\d/.test(sexStr)) {
    console.log('获取性别错误，身份证号异常');
    return '';
  }

  return (sexStr % 2 === 0) ? 'F' : 'M';
};

/**
 * 获取就诊时间段
 * visitPeriod  时段
 * visitBeginTime 开始时间
 * visitEndTime 结束时间
 * @param resData
 * @returns {string}
 */
export const getTimeSlot = (resData = {}) => {
  const { visitPeriod, visitBeginTime = '', visitEndTime = '' } = resData;
  if (visitBeginTime) {
    if (visitPeriod) {
      let timeSlot = `${TIME_MAP[visitPeriod] || null} ${visitBeginTime}`;
      if (IS_NEED_ENDTIME) {
        timeSlot = `${timeSlot}~${visitEndTime}`;
      }
      return timeSlot;
    } else {
      let timeSlot = visitBeginTime;
      if (IS_NEED_ENDTIME) {
        timeSlot = `${visitBeginTime}~${visitEndTime}`;
      }
      return timeSlot;
    }
  } else {
    return TIME_MAP[visitPeriod];
  }
};

export const getVisitTime = (visitDate, visitWeekName, visitPeriod, visitBeginTime, visitEndTime) => {
  var visitTime = visitDate;
  if (visitWeekName) {
    visitTime += ` ${visitWeekName}`;
  }
  if (TIME_MAP['' + visitPeriod]) {
    visitTime += ` ${TIME_MAP['' + visitPeriod]}`;
  }
  if (visitBeginTime) {
    visitTime += ` ${visitBeginTime}`;
    if (IS_NEED_ENDTIME) {
      visitTime += '~' + visitEndTime;
    }
  }
  return visitTime;
}

export const getWeekDay = (date) => {
  var weekDayMap = { '1': '一', '2': '二', '3': '三', '4': '四', '5': '五', '6': '六', '0': '日' };
  var weekDay = date ? weekDayMap[(new Date(date).getDay() || 0).toString()] : '';
  return weekDay;
}

export const validatorIdCard = (val = '') => {
  val = val.replace(/(^\s*)|(\s*$)/g, '');
  val = val.toUpperCase();
  const len = (val || '').length;

  if (len == 0) {
    return { ret: false, tip: '不能为空' };
  }
  if (len != 18 && len != 15) {
    return { ret: false, tip: '格式错误' };
  }
  // 15位的身份证，验证了生日是否有效
  if (len == 15) {
    const year = val.substring(6, 8);
    const month = val.substring(8, 10);
    const day = val.substring(10, 12);
    const tempDate = new Date(year, parseFloat(month) - 1, parseFloat(day));
    if (tempDate.getYear() != parseFloat(year) || tempDate.getMonth() != parseFloat(month) - 1
      || tempDate.getDate() != parseFloat(day)) {
      return { ret: false, tip: '格式错误' };
    }
    return { ret: true };
  }
  // 18位的身份证，验证最后一位校验位
  if (len == 18) {
    // 身份证的最后一为字符
    const endChar = val.charAt(len - 1);
    val = val.substr(0, 17);
    const table = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const table2 = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    const cNum = [];
    for (let i = 0; i < val.length; i++) {
      cNum[i] = val.charAt(i);
    }
    let sum = 0;
    for (let i = 0; i < cNum.length; i++) {
      // 其中(cNum[i]-48)表示第i位置上的身份证号码数字值，table[i]表示第i位置上的加权因子，
      const num = cNum[i].charCodeAt(0);
      const num1 = parseInt(table[i], 10);
      sum = (sum * 1) + ((num - 48) * num1);
    }
    // 以11对计算结果取模
    const index = Number(sum % 11);
    // 根据模的值得到对应的校验码,即身份证上的最后一位为校验码
    const verfiyCode = table2[index];
    if (endChar != verfiyCode) {
      return { ret: false, tip: '格式错误' };
    }
    return { ret: true };
  }
}

export const convertListStatus = (status) => {
  var statusMap = {
    'S': 'success',
    'F': 'fail',
    'L': 'lock',
    'C': 'cancel',
    'P': 'abnormal',
    'H': 'abnormal',
    'Z': 'abnormal',
    'E': 'abnormal',
  };

  return statusMap[status] || '';
}

/**
 * 获取聊天显示时间
 * @param createTime {string} 时间
 * @returns {String} 返回的时间
 */
export const getChatShowTime = (createTime = '', spacer = '-', dateGap = 0) => {
  if (!createTime) {
    return '';
  } else {
    let date = new Date();
    let year = date.getFullYear();
    date = new Date(date.setDate(date.getDate() + dateGap));
    date = date.getFullYear() + spacer + (date.getMonth() + 1) + spacer + (date.getDate());
    date = date.replace(/\b(\w)\b/g, '0$1');
    if (createTime.indexOf(date) === 0) {
      return createTime.substring(11, 16);
    } else if (createTime.indexOf(year) === 0) {
      return createTime.substring(5, 16);
    } else {
      return createTime;
    }
  }
};

export const openFile = url => {
  Taro.showLoading({ title: "加载中", mask: true });

  Taro.downloadFile({
    url,
    success(res) {
      const filePath = res.tempFilePath;
      Taro.openDocument({
        filePath,
        fileType: "pdf",
        complete() {
          Taro.hideLoading()
        }
      });
    },
    fail() {
      Taro.hideLoading();
    }
  });
}

/**
 * 用于把用utf16编码的字符转换成实体字符，以供后台存储
 * @param  {string} str 将要转换的字符串，其中含有utf16字符将被自动检出
 * @return {string}     转换后的字符串，utf16字符将被转换成&#xxxx;形式的实体字符
 */
export const utf16toEntities = (str) => {
  var patt = /[\ud800-\udbff][\udc00-\udfff]/g; // 检测utf16字符正则
  str = str.replace(patt, function (char) {
    var H, L, code;
    if (char.length === 2) {
      H = char.charCodeAt(0); // 取出高位
      L = char.charCodeAt(1); // 取出低位
      code = (H - 0xd800) * 0x400 + 0x10000 + L - 0xdc00; // 转换算法
      return "&#" + code + ";";
    } else {
      return char;
    }
  });
  return str;
};
