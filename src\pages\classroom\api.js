import { post, thirdPost } from '@/utils/request'

//获取遗传课堂分类
export const getArticleTypeList = (param) => post('/api/article/type/list', param);

export const getArticleList = (param) => post('/api/article/list', { ...param, position: 3 });

export const getArticleDetail = (param) => post('/api/article/get', param);

export const getActivityList = (param) => post('/api/train/get-train-list', param);

export const getActivityDetail = (param) => post('/api/train/get-train-by-id', param);

export const signActivity = (param) => post('/api/train/sign-train', param);

export const getSignList = (param) => post('/api/train/get-user-sign-list', param);

export const getSignDetail = (param) => post('/api/train/get-sign-by-id', param);

export const cancelSign = (param) => post('/api/train/cancel-train', param);

export const reSign = (param) => post('/api/train/resign-train', param);


export const saveOrder = (param) => post('/api/ext/saveorder', param);

export const prePayOrder = (param) => post('/api/ext/extpayorder', param);

// 选择支付方式
export const choosePayMode = (param) => thirdPost(`${$PAY_DOMAIN}/api/pay/choosepaymode`, param);

export const getOrderDetail = (param) => post('/api/ext/getextorderbyid', param);
