import { View, Button, Text } from "@tarojs/components";
import Taro, { Component } from "@tarojs/taro";
import * as API from '../api'
import s from './index.module.scss'

const MERGE_TOTAL_FEE = 200000


export default class Pay extends Component {

  constructor(props) {
    super(props)
    this.state = {
      payInfo: {},
      // userName: '',
      totalFee: 0,
      patientName: '',
      products: [],
    }
  }

  componentWillMount() {
    console.log('componentWillMount')
    this.getPayInfo()
  }

  componentWillUnmount() {
    console.log('componentWillUnmount')
    Taro.removeStorageSync('payInfoTemp')
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#30A1A6',
    navigationBarTitleText: '收银台',
    navigationBarTextStyle: 'white',
  };

  getPayInfo() {
    let payInfo = Taro.getStorageSync('payInfoTemp')
    // let userInfo = Taro.getStorageSync('userInfo') || '{}'
    try {
      // userInfo = JSON.parse(userInfo)
      payInfo = JSON.parse(payInfo)
      const { totalFee, patientName, extFieldsViews } = payInfo
      const extFieldsViewsObj = JSON.parse(extFieldsViews)
      const { products, isMerge, reportMailingFees, reportMailingType } = extFieldsViewsObj

      if (isMerge) {
        products.push({
          productName: '加急',
          productprice: MERGE_TOTAL_FEE,
        })
      }
      if (reportMailingType === '2') {
        products.push({
          productName: '快递费',
          productprice: reportMailingFees,
        })
      }
      this.setState({
        payInfo,
        // userName: userInfo.userName,
        totalFee,
        patientName,
        products,
      })
    } catch (error) {
      console.error(error)
      Taro.showToast({ title: '费用信息错误', icon: 'none' })
      setTimeout(Taro.navigateBack, 1500)
    }
  }

  async submit() {
    const { payInfo } = this.state
    const { code, data } = await API.saveOrder({
      ...payInfo,
    })
    if (code !== 0) return
    this.getPayConfig(data.orderId)
  }

  async getPayConfig(orderId) {
    const { code, data} = await API.prePayOrder({ orderId })
    if (code !== 0) return
    const { payOrderId } = data
    /** 获取可选的支付方式 暂时先定死微信支付 */
    // const res = await API.cashier({ payOrderId })
    // if (res.code !== 0) return
    const res2 = await API.choosePayMode({ payMode: 'weixin_miniapp', payOrderId, })
    this.chooseWeChatWapPay(res2.data.payParameter, orderId)
  }

  async chooseWeChatWapPay(config, orderId) {
    let requestPaymentRes;
    try {
      requestPaymentRes = await Taro.requestPayment({ ...config })
    } catch (e) {
      console.log(e)
      requestPaymentRes = e
    }
    if (requestPaymentRes.errMsg == 'requestPayment:fail cancel') {
      // 取消支付
    } else if (requestPaymentRes.errMsg == 'requestPayment:ok') {
      // 支付成功
      Taro.navigateTo({ url: `/pages/patient/bill/makebill-detail/index?id=${orderId}` })
    }
  }


  render() {

    const { totalFee, patientName, products } = this.state

    return (
      <View className={s.page}>
        <View className={s.page_header}>
          <View className={s.page_header_line1}>{ (totalFee / 100).toFixed(2) }元</View>
          <View className={s.page_header_line2}>支付金额</View>
        </View>

        <View className={s.block}>
          <View className={s.block_bar}></View>

          <View className={s.block_body}>
            <View className={s.block_line}>
              <View className={s.block_line_label}>费用类型</View>
              <View className={s.block_line_value}>扫码缴费</View>
            </View>
            {/* <View className={s.block_line}>
              <View className={s.block_line_label}>医生姓名</View>
              <View className={s.block_line_value}>{userName}</View>
            </View> */}
            <View className={s.block_line}>
              <View className={s.block_line_label}>就诊人</View>
              <View className={s.block_line_value}>{patientName}</View>
            </View>
            <View className={s.block_line}>
              <View className={s.block_line_label}>费用明细</View>
              <View className={s.block_line_value2}>
                金额: <Text className={s.block_line_value2_inner}>{(totalFee / 100).toFixed(2)}</Text>
              </View>
            </View>

            {/* 费用明细表格 */}
          <View className={s.table}>
            <View className={s.table_row}>
              <View className={[s.table_col, s.table_col_h]}>费用名称</View>
              <View className={[s.table_col, s.table_col_h]}>金额(元)</View>
            </View>
            { products.map((v, i) => (
              <View className={s.table_row} key={i}>
                <View className={s.table_col}>{v.productName}{ v.remark ? `(${v.remark})` : '' }</View>
                <View className={s.table_col}>{(v.productprice/100).toFixed(2)}</View>
              </View>
            )) }
          </View>
          </View>

        </View>

        <View className={s.page_footer}>
          <Button className={[s.btn]} onClick={this.submit}>确认支付</Button>
          <Button className={[s.btn, s.cancel]} onClick={Taro.navigateBack}>取消</Button>
        </View>

      </View>)
  }
}
