import Taro, { Component } from '@tarojs/taro'
import { View, Image, Text } from '@tarojs/components'

import chatPng from '@/resources/chat.png'
import s from './index.module.scss'

export default class Chat extends Component {

  constructor(props) {
    super(props)
    this.state = {
      hasPermission: false,
    }
  }

  componentDidShow() {
    try {
      let userInfo = Taro.getStorageSync('userInfo');
      userInfo = JSON.parse(userInfo)
      const hasPermission = userInfo && userInfo.zxsz == '1'
      this.setState({ hasPermission })
    } catch (error) {
      console.log(error)
    }
  }

  toChat() {
    Taro.navigateTo({ url: '/package1/consult/choosetype/index' })
  }

  render() {
    return (
      <block>
        {
          this.state.hasPermission ?
          <View className={s.container} onClick={this.toChat}>
            <Image className={s.icon} src={chatPng}></Image>
            <Text className={s.label}>家辉咨询</Text>
          </View> : null
        }
      </block>
    )
  }
}
