import Taro, { Component } from "@tarojs/taro";
import { View, Text, CheckboxGroup, Checkbox, Input, RadioGroup, Radio, Button } from "@tarojs/components";
import Field from '@/component/field';
import Modal from '@/component/modal';

import * as API from './api'
import s from "./index.module.scss";

const OTHER_PROJECT_ID = -1
const MERGE_TOTAL_FEE = 200000

const DEFAULT_PRODUCT_ITEM = {
  productId: OTHER_PROJECT_ID,
  productName: '其他（若以上无可选项目，可勾选此项）',
  productprice: 0,
}

const IdCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
const phoneReg = /^1[3456789]\d{9}$/;
const moneyReg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;

export default class MakeBill extends Component {

  constructor(props) {
    super(props)
    this.state = {
      patientId: '',
      patientName: '',
      idNumber: '',
      mobile: '',
      /** 所选的开单项目id */
      products: [],
      /** 其他项目金额 */
      extroMoney: '',
      remark: '',
      isMerge: 0,

      productList: [],
      showModal: false,

      accountInfo: {},
    }
  }

  componentWillMount() {
    const { patientPid } = this.$router.params;
    this.getProductList()
    patientPid && this.getPatientInfo(patientPid)
  }
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '开单申请',
    navigationBarTextStyle: 'black',
  };

  showError(title, duration = 1500) {
    Taro.showToast({ title, icon: 'none', duration });
  }

  handleSetState = (key, val) => {
    if (key === 'isMerge') {
      val = Number(val)
    }
    this.setState({ [key]: val })
    if (key === 'isMerge' && !!Number(val)) {
      this.setState({ showModal: true })
    }
  }

  async validate() {
    const { patientName, idNumber, mobile, products, remark, extroMoney } = this.state
    if (!patientName) return Promise.resolve('请填写姓名');
    if (!idNumber) return Promise.resolve('请填写身份证');
    if (!IdCardReg.test(idNumber)) return Promise.resolve('身份证格式错误')
    if (mobile && !phoneReg.test(mobile)) return Promise.resolve('电话格式错误')
    if (!products.length) return Promise.resolve('请选择开单项目');
    if (products.map(Number).includes(OTHER_PROJECT_ID)) {
      if (!extroMoney) return Promise.resolve('请填写金额');
      if (!moneyReg.test(extroMoney)) return Promise.resolve('请填写正确的金额');
      if (!remark) return Promise.resolve('请填写备注');
    }
    return Promise.resolve();
  }

  submit= async () => {
    const err = await this.validate()
    if (err) return this.showError(err)
    const userInfo = JSON.parse(Taro.getStorageSync('userInfo') || '{}') || {}
    const { patientId, patientName, idNumber, mobile, isMerge, productList, products, remark, accountInfo, extroMoney } = this.state
    console.log(patientId)
    const selectedProducts = productList.filter(v => products.map(Number).includes(v.productId))
    let totalFee = selectedProducts.reduce((res, v) => res + v.productprice, 0)
    if (isMerge) totalFee += MERGE_TOTAL_FEE
    if (products.map(Number).includes(OTHER_PROJECT_ID)) totalFee += Number(extroMoney) * 100
    if (accountInfo.reportMailingType == '2') totalFee += Number(accountInfo.reportMailingFees)
    const extFieldsViews = JSON.stringify({
      ...accountInfo,
      isMerge,
      products: selectedProducts.map(v => ({
        ...v,
        productId: v.productId === OTHER_PROJECT_ID ? undefined : v.productId,
        remark: v.productId === OTHER_PROJECT_ID ? remark : undefined,
        productName: v.productId === OTHER_PROJECT_ID ? '其他' : v.productName,
        productprice: v.productId === OTHER_PROJECT_ID ? Number(extroMoney) * 100 : v.productprice,
      }))
    });
    const { institutionId, allianceName } = userInfo
    const params = {
      totalFee,
      bizType: 'dna_gene_pay',
      // patientId,
      patientName,
      mobile,
      idNumber,
      extFieldsViews,
    }
    institutionId && (params.deptId = institutionId)
    allianceName && (params.deptName = allianceName)

    Taro.setStorageSync('payInfoTemp', JSON.stringify(params))
    Taro.navigateTo({ url: '/pages/patient/bill/makebill/pay/index' })
  }


  async getProductList() {
    const userInfo = JSON.parse(Taro.getStorageSync('userInfo') || '{}') || {}
    const { code, data } = await API.getProductList({
      acount: userInfo.account || 'doctor',
      allianceId: userInfo.institutionId || '1'
    })
    if (code !== 0) return
    const { resultCode, resultMessage, products, docaccount, docname, reportMailingType, reportMailingFees, } = data
    /** 获取产品列表失败 */
    if (Number(resultCode) === 1) {
      Taro.showModal({
        title: '提示',
        content: resultMessage,
        showCancel: false,
        success: () => {
          Taro.navigateBack()
        }
      })
      return
    }
    const productList = products || []
    productList.push(DEFAULT_PRODUCT_ITEM)
    const accountInfo = { docaccount, docname, reportMailingType, reportMailingFees }
    this.setState({ productList, accountInfo })
  }

  async getPatientInfo(pid) {
    const { code, data } = await API.getPatientInfoByPid({ pid })
    if (code !==0) return
      const { patientPid: patientId, patientName, patientIdNo: idNumber, patientMobile: mobile } = data;
      this.setState({ patientId, patientName, idNumber, mobile });
  }

  reset() {
    Taro.switchTab({ url: '/pages/patient/index' })
    Taro.navigateBack()
  }

  render() {
    const { patientName, idNumber, mobile, productList, products, showModal, accountInfo, isMerge } = this.state
    const { reportMailingType, reportMailingFees } = accountInfo
    return (
      <View className={s.make_bill_page}>
        <View className={s.page_notice}>温馨提示：请输入受检者准确的姓名及证件号码</View>
        <Field
          label='受检者姓名'
          labelStyle={{ color: '#000', width: '104px' }}
          placeholder='请输入姓名'
          required
          value={patientName}
          onSetValue={v => this.handleSetState('patientName', v)}
        />
        <Field
          value={idNumber}
          label='身份证'
          labelStyle={{ color: '#000', width: '104px' }}
          placeholder='请输入身份证号'
          maxlength={18}
          required
          onSetValue={v => this.handleSetState('idNumber', v)}
        />
        <Field
          value={mobile}
          label='受检者手机号'
          labelStyle={{ color: '#000', width: '104px' }}
          maxlength={11}
          type='number'
          placeholder='请输入手机号'
          onSetValue={v => this.handleSetState('mobile', v)}
        />

        <View className={s.page_body}>
          <View className={s.project_block}>
            <Text className={s.project_block__label}>请选择需要检测的项目</Text>
            <CheckboxGroup className={[s.project_block__list, 'f-col']} onChange={e => this.handleSetState('products', e.detail.value)}>
              {
                productList.map(item => (
                  <View key={item.productId} className={[s.project_block__item, 'f-row', 'f-c-center', 'f-m-between']}>
                    <Checkbox
                      className={['f-1', s.project_block__item_label]}
                      value={item.productId}
                      color='#30A1A6'
                    >{ item.productName }</Checkbox>
                    { item.productId !== OTHER_PROJECT_ID ? <Text className={s.project_block__item_price}>{ (item.productprice / 100).toFixed(2) }元</Text> : null }
                  </View>
                ))
              }
            </CheckboxGroup>
            {/* 其他项目 */}
            {products.map(Number).includes(OTHER_PROJECT_ID) ?
              <View>
                <Text className={s.project_block__label}>其他项目金额</Text>
                <View className={s.input_item} style='margin: 16px 0;'>
                  <Input className={s.input_item__inner} placeholder='输入金额' onInput={e => this.handleSetState('extroMoney', e.detail.value)} />
                </View>
                <Text className={s.project_block__label}>备注</Text>
                <View className={s.input_item} style='margin-top: 16px;'>
                  <Input className={s.input_item__inner} placeholder='输入备注' onInput={e => this.handleSetState('remark', e.detail.value)} />
                </View>
              </View> :
            null}
            {/* 是否加急 */}
            <View style='margin-top: 16px;'>
              <Text className={s.project_block__label}>请选择是否加急</Text>
              <RadioGroup
                className={['f-row', 'f-m-between']}
                style='margin-top: 16px;'
                onChange={e => this.handleSetState('isMerge', e.detail.value)}
              >
                <View className={['f-1', s.ratio_item]} style='margin-right: 24px;'>
                  <Radio className={s.ratio_item__inner} value={1} color='#30A1A6' checked={isMerge === 1}>是</Radio>
                </View>
                <View className={['f-1', s.ratio_item]}>
                  <Radio className={s.ratio_item__inner} value={0} color='#30A1A6' checked={isMerge === 0}>否</Radio>
                </View>
              </RadioGroup>
              {
                reportMailingType == '2' ? <Text className={s.tip}>注：检测需支付报告快递费用{ (Number(reportMailingFees) / 100).toFixed(2) }元</Text> : null
              }
            </View>


          </View>

          <View class={s.page_footer}>
            <Button className={[s.btn, s.submit]} onClick={this.submit}>确 认</Button>
            <Button className={[s.btn, s.cancel]} onClick={this.reset}>取 消</Button>
          </View>

        </View>

        <Modal
          show={showModal}
          title='温馨提示'
          onCancel={() => {
            this.setState({ showModal: false, isMerge: 0 })
          }}
          onOk={() => this.setState({ showModal: false })}
        >
          <View className={s.modalContent}>加急项目需支付{(MERGE_TOTAL_FEE/100).toFixed(2)}元，是否确认加急？</View>
        </Modal>
      </View>
    )
  }
}
