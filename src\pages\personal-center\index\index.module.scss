page {
  height: 100%;
  background: $color-bg;
}
.container {
  .card {
    margin-bottom: 20px;
    padding: 64px 30px 64px 20px;
    border-radius:8px;

    .avatar {
      width: 154px;
      height: 154px;
      border-radius: 50%;
      margin-right: 36px;
    }

    .doctor<PERSON>ame {
      color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
      /* 36B */
      font-family: PingFang SC;
      font-size: 36px;
      font-style: normal;
      font-weight: 600;
      line-height: 54px; /* 150% */
    }
    .header_line{
      color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));
      /* 28R */
      font-family: PingFang SC;
      font-size: 28px;
      font-style: normal;
      font-weight: 400;
      line-height: 42px; /* 150% */
    }
    &_right{
      flex: 0 0 64px;
      &_image{
        width: 64px;
        height: 64px;
        border-radius: 4px;
      }
    }
    .box{
      padding: 8px;
      display: flex;
      flex-direction: column;
      border-radius: 8px;
      background: #3F969D;
    }
    .code_name{
      margin-top: 2px;
      line-height: 18px;
      font-size: 13px;
      color: #fff;
      font-weight: bold;
    }
  }

  .page_body{
    margin: 0 20px;
    padding: 0 32px;
    border-radius: 8px;
    border-radius: 8px;
    background: var(--grey-grey-00, #FFF);
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.12);
  }

  .record {
    display: flex;
    padding: 24px 0px;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.06) inset;

    color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
    font-family: PingFang SC;
    font-size: 32px;
    font-style: normal;
    font-weight: 400;
    line-height: 48px; /* 150% */

    &:last-of-type{
      box-shadow: none
    }
    &_arrow{
      width: 24px;
      height: 24px;
    }
  }

  .logoutBtn {
    margin: 85px 48px 0;
    border-radius: 76px;
    height: 94px;
    line-height: 94px;
    text-align: center;
    font-size: 34px;
    color: #3F969D;
    border: 1px solid #3F969D;
  }
}

.tool_modal{
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tool_tips{
  margin-bottom: 24px;
  text-indent: 2em;
  color: rgba(0, 0, 0, 0.70);
  font-size: 34px;
  line-height: 52px;
}
.tool_btn{
  padding: 8px 56px;
  border-radius: 16px;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  color: #FFF;
  font-size: 34px;
}
