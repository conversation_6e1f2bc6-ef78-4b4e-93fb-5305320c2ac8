import Taro, { Component } from "@tarojs/taro";
import { View } from '@tarojs/components'
import Empty from '@/component/empty'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      hasPermission: false,
    }
  }

  componentDidShow() {
    let userInfo = Taro.getStorageSync('userInfo');
    userInfo = JSON.parse(userInfo)
    const hasPermission = userInfo && userInfo.zxsz == '1'
    this.setState({ hasPermission })
    if (hasPermission) {
      Taro.redirectTo({ url: '/package1/consult/choosetype/index' })
    }
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '家辉咨询',
    navigationBarTextStyle: 'black'
  };

  render() {
    const { hasPermission } = this.state
    return (<View>
      { hasPermission ? null : <Empty text='暂未开通权限' /> }
    </View>)
  }
}
