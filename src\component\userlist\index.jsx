import Taro, { Component } from '@tarojs/taro';
import { View, Block } from '@tarojs/components';
import { IS_WITHHOLD } from '@/utils/config';

import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  navTo = (url) => {
    Taro.navigateTo({ url });
  }

  render () {
    const { onClick = () => {}, cardList = [], leftBindNum = 0 } = this.props;

    if (!cardList.length) {
      return null;
    }

    return (
      <Block>
        {
          cardList.map((card) => {
            return (
              <View
                key={card.patientId}
                className={styles.card}
                onClick={() => onClick(card)}
              >
                <View className={styles.cardInfo}>
                  <View className={styles.infoMain}>
                    <View className={styles.mainName}>
                      <View className={styles.name}>{card.patientName}</View>
                      {
                        card.relationType == 1 ? <View className={styles.status}>本人</View> : null
                      }
                      {
                        card.isDefault == 1 ? <View className={styles.status}>默认</View> : null
                      }
                      {
                        (IS_WITHHOLD && card.signStatus && card.relationType == 1) ?
                          <View className={`${styles.status} ${styles.signStatus}`}>代扣已开通</View>
                          : null
                      }
                    </View>
                  </View>
                  <View className={styles.infoExtra}>{card.patCardTypeName || '就诊卡'}：{card.patCardNo}</View>
                </View>
              </View>
            );
          })
        }
        {
          leftBindNum > 0 ?
            <View
              onClick={() => this.navTo('/pages/usercenter/bindscan/index')}
              className={styles.adduser}
            >
              <View className={styles.addTitle}>添加就诊人</View>
              <View className={styles.addText}>还可添加{leftBindNum}人</View>
            </View> : null
        }
      </Block>
    )
  }
}
