import Taro, { Component } from "@tarojs/taro";
import { View, Image, Text, Picker, Button, Input, ScrollView } from "@tarojs/components";
import Empty from '@/component/empty'
import dayjs from 'dayjs'

import barcodePng from '@/resources/images/sample/barcode.png'

import * as API from '../api'

import s from './index.module.scss'


export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      startDate: '',
      endDate: '',
      list: [],
      pageNum: 1,
      pageCount: 1,
    }
    this.lock = false;
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '已上传样本',
    navigationBarTextStyle: 'black'
  };

  componentDidShow() {
    this.getList()
  }

  async getList(param = {}) {
    if (this.lock) return;
    this.lock = true;
    const { startDate, endDate, list } = this.state;
    const { data = {} } = await API.getAlreadySampleList({...param, createDateStart: startDate, createDateEnd: endDate})
    this.lock = false;
    this.setState({ list: param.pageNum > 1 ? list.concat(data.recordList || []) : data.recordList || [], pageNum: data.currentPage, pageCount: data.pageCount });
  }

  onDateChange = (e, key) => {
    const value = e.detail.value
    this.setState({ [key]: value })
  }


  toDetail = id => {
    Taro.navigateTo({ url: `/pages/sample/detail/index?id=${id}` })
  }

  search = async () => {
    this.getList();
  }

  reset = () => {
    this.setState({ startDate: '', endDate: '' })
    this.getList()
  }

  scrollLower = () => {
    const { pageNum, pageCount } = this.state;
    if (pageNum < pageCount) {
      this.getList({pageNum: pageNum + 1});
    }
  }


  render() {
    const { startDate, endDate, list } = this.state
    return (
      <View className={s.page}>
        <View className={s.header}>
          <View className={s.header_daterange}>
            <Picker
              className={s.header_daterange__picker}
              mode='date'
              value={startDate}
              onChange={e => this.onDateChange(e, 'startDate')}
            >
              <Input value={startDate} placeholder='开始日期' disabled />
            </Picker>
            <Text className={s.header_daterange__split}>-</Text>
            <Picker
              className={s.header_daterange__picker}
              mode='date'
              value={endDate}
              onChange={e => this.onDateChange(e, 'endDate')}
            >
              <Input value={endDate} placeholder='结束日期' disabled />
            </Picker>
            { startDate || endDate ?
              <Text className={s.header_daterange__clear} onClick={this.reset}>清空</Text> :
              <Text className={s.header_daterange__clear}></Text> }
          </View>
          <Button className={s.header_search} onClick={this.search}>筛选</Button>
        </View>
        
        <View className={s.contaoner}>
          <View className={s.body}>
            {
              list && list.length ?
              <ScrollView
                className={s.list}
                scrollY
                onScrollToLower={this.scrollLower}
              >
                { list.map(v => (
                  <View key={v.id} className={s.item} onClick={() => this.toDetail(v.id)}>
                    <View className={s.item_header}>
                      <Image className={s.item_header__icon} src={barcodePng} />
                      <Text className={s.item_header__title}>{v.sampleNumber}</Text>
                    </View>
                    <View className={s.item_footer}>
                      <Text className={s.item_header__his}>{v.submitHospitalName}</Text>
                      <Text className={s.item_header__date}>{dayjs(v.extfiled4).format('YYYY-MM-DD HH:mm')}</Text>
                    </View>
                  </View>
                ))}
              </ScrollView> :
              <Empty text='暂无样本' />
            }
          </View>
        </View>
      </View>
    )
  }
}
