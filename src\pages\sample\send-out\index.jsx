import Taro, { Component } from "@tarojs/taro";
import { View, Text, Image, Input, Button, Camera, Picker, RadioGroup, Radio, Textarea } from "@tarojs/components";
import { AtFloatLayout } from "taro-ui"
import Empty from '@/component/empty'

import scanPng from '@/resources/images/sample/scan.png'
import clearPng from '@/resources/images/sample/clear.png'
import receivePng from '@/resources/images/sample/receive.png'
import sendPng from '@/resources/images/sample/send.png'
import addressPng from '@/resources/images/sample/address.png'
import arrowPng from '@/resources/images/arrow-right.png'
import * as API from '../api'
import s from './index.module.scss'

export default class Index extends Component {

    constructor(props) {
        super(props)
        this.state = {
            showScanPopup: false,      // 控制扫描弹窗
            currentSample: {           // 当前扫描样本
                sampleNo: '',
                productLine: '',
                sonproductLine: '',
                remark: '' // 新增备注字段


            },
            productLines: [],          // 产品线列表
            sonproductLines: [],      // 子产品线列表
            currentAddress: null,//寄件地址
            receiverAddress: {},//收件地址
            hideCamera: true,
            list: [],
            isOpenedPayType: false,
            payTypeList: [
                {
                    code: 1,
                    name: '寄付现结',
                    memo: '快递员取件后，寄方可通过在线支付、扫快递员码等方式付款',
                    isChecked: true,
                    monthlyCard: ''
                },
                {
                    code: 2,
                    name: '到付',
                    memo: '快递签收后，收方可通过在线支付、扫快递员码等方式付款',
                    isChecked: false,
                    monthlyCard: ''
                },
                {
                    code: 3,
                    name: '寄付月结',
                    memo: '按月结算运费，需提前开通月结卡',
                    isChecked: false,
                    monthlyCard: ''
                }
            ],
            currentPayType: {},
            expressList: [{ value: '顺丰特快', code: 1 }, { value: '顺丰标快', code: 2 }],
            expressType: {},
            dfsz: '0'
        }
    }


    componentWillMount() {
        this.initCameraAuth()
        Taro.showLoading()
        // this.getCurrentLocation()
        this.getDefaultAddress()
        this.getReceiverAddress()
        this.getUserConfig();
        this.setState({
            currentPayType: this.state.payTypeList.filter(x => x.isChecked)[0]
        })
        this.fetchProductLines();//获取产品线列表
    }

    componentDidMount() {
        const userInfo = Taro.getStorageSync('userInfo');
        console.log("222&", userInfo);

    }


    componentDidShow() {
        let pages = Taro.getCurrentPages();
        let currentPage = pages[pages.length - 1]; // 获取当前页面
        if (currentPage.data.currentAddress) { // 获取值
            this.setState({ currentAddress: currentPage.data.currentAddress })
        }
    }

    config = {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTitleText: '样本寄送',
        navigationBarTextStyle: 'black'
    };
    // 获取产品线列表
    fetchProductLines = async () => {
        const { code, data } = await API.getProductList(); // 修改为正确的接口方法
        if (code === 0) { 
            //console.log('我在测试产品线列表', data)
            // 转换接口数据格式为picker需要的格式
            // 转换接口数据格式
            const productLines = data.map(item => ({
                value: item.productName,  // 显示文本
                code: item.id             // 对应值
            }));
            this.setState({ productLines });
        }
    }
    //获取子产品线
    fetchSonProductLines = async (parentId) => {
        //console.log('子产品线Id', parentId)
        const param = {
            parentId: parentId
        }

        const { code, data } = await API.getProductList(param);
        //console.log('子产品线Id2', data)
        if (code === 0) {
            if (data != undefined && data != null) {
                const sonproductLines = data.map(item => ({
                    value: item.productName,
                    code: item.id
                }));
                this.setState({ sonproductLines });
            } else {
                this.setState({ sonproductLines: [] })
            }

        }
    }
    /**
     * 获取寄件人默认地址
     */
    getDefaultAddress = async () => {
        const { code, data } = await API.getAddressList()
        if (code !== 0) return
        let list = data.addressList || []
        if (list.length > 0) {
            let address = list.filter(x => x.isDefault === 1)[0]
            this.setState({
                currentAddress: address
            })
        }
    }

    /**
     * 获取收件人地址
     */
    getReceiverAddress = async () => {
        const { code, data } = await API.getProfileByKey({ key: 'mail_sample_receiver_address' })
        if (code !== 0) return
        this.setState({
            receiverAddress: JSON.parse(data.profileValue || '{}')
        })
    }

    /**
     * 获取是否到付配置
     * @returns 
     */
    getUserConfig = async () => {
        const { code, data } = await API.getUserConfig()
        if (code !== 0) return
        console.log('到付',data)
        this.setState({
            dfsz: data.dfsz
        }, this.setPayTypeList)
    }

    setPayTypeList = () => {
        const { dfsz, payTypeList } = this.state
        if (dfsz === '0') {
            //移除到付配置
            let temp = payTypeList.filter(x => x.code !== 2)
            console.log('到付2',temp)
            this.setState({ payTypeList: temp })
        }
    }

    /**
     * 获取当前位置
     */


    getCurrentLocation = () => {
        Taro.getLocation({
            type: 'wgs84',
            success: function (res) {
                console.log('当前位置',res);
                const latitude = res.latitude    // 纬度
                const longitude = res.longitude  // 经度
                const speed = res.speed          // 速度（米/秒）
                const accuracy = res.accuracy    // 位置精度（米）
            }
        })
    }

    /**
     * 跳转选择地址页面
     */
    goAddressList = () => {
        Taro.navigateTo({
            url: '/package1/address/index?mode=choose'
        })
    }

    goToDetail(id) {
        Taro.navigateTo({ url: `/package1/address/edit/index?id=${id}&mode=choose` });
    }

    getSetting = () => new Promise((resolve, reject) => {
        Taro.getSetting({
            success: res => {
                const auth = res.authSetting
                const scopeCamera = auth['scope.camera']
                resolve(scopeCamera)
            },
            fail: reject
        })
    })

    initCameraAuth = async () => {
        const scopeCamera = await this.getSetting()
        /** 已授权摄像头 */
        if (scopeCamera !== false) {
            this.setState({ hideCamera: false })
        } else {
            Taro.showToast({ title: '请点击右上角图标授权相机', icon: 'none' })
        }
    }

    openSetting = async () => {
        if (!this.state.hideCamera) return
        const scopeCamera = await this.getSetting()
        if (scopeCamera) {
            this.setState({ hideCamera: false })
            return
        }
        Taro.openSetting({
            success: res => {
                if (res.authSetting['scope.camera']) {
                    this.setState({ hideCamera: false })
                }
            }
        })
    }

    onCameraInitDone = () => {
        Taro.hideLoading()
    }

    handleCameraError(e) {
        Taro.hideLoading()
        Taro.showToast({ icon: 'none', title: e.detail.errMsg || '摄像机调用失败' })
        this.setState({ hideCamera: true })
    }

    onCameraError = e => {
        this.handleCameraError(e)
    }

    onCameraScanCode = e => {
        const { list } = this.state
        const result = e.detail.result;
        if (!result) return;

        // 检查是否存在相同样本号（对象数组检查）
        const exists = list.some(item => item.sampleNo === result);
        if (exists) {
            Taro.showToast({ icon: 'none', title: '该条码已录入，请重新选择条码' });
        } else {
            Taro.showToast({ icon: 'none', title: '检测到样本号' });

            this.setState({
                showScanPopup: true,
                hideCamera: true,  // 关闭相机
                currentSample: {
                    sampleNo: result,
                    productLine: ''
                }
            });
        }
    }
    // 新增处理输入变化
    handleNext = () => {
        const { currentSample, list } = this.state;
        if (!currentSample.productLine) {
            Taro.showToast({ title: '请选择产品线', icon: 'none' });
            return;
        }
        //console.log('产品线'.currentSample);
        // 获取产品线名称
        const parentProduct = this.state.productLines.find(p => p.code === currentSample.productLine) || {};
        const sonProduct = this.state.sonproductLines.find(s => s.code === currentSample.sonproductLine) || {};

        this.setState({
            list: [...list, {
                sampleNo: currentSample.sampleNo,
                productLine: currentSample.productLine,
                productLineName: parentProduct.value || '未知产品线',
                sonproductLine: currentSample.sonproductLine,
                sonproductLineName: sonProduct.value || ''
            }],
            showScanPopup: false,
            hideCamera: false,  // 恢复相机
            currentSample: {
                sampleNo: '',
                productLine: '',
                sonproductLine: '',
                remark: ''
            }
        });
    }

    deleteSample = (sampleNo) => {
        const { list } = this.state
        const newList = list.filter(item => item.sampleNo !== sampleNo)
        this.setState({ list: newList })
    }

    openPayTypeLayout = () => {
        const { currentPayType, payTypeList } = this.state
        this.setState({
            isOpenedPayType: true
        })
        if (currentPayType.code) {
            for (let i = 0; i < payTypeList.length; i++) {
                if (payTypeList[i].code === currentPayType.code) {
                    payTypeList[i].isChecked = true
                } else {
                    payTypeList[i].isChecked = false
                    payTypeList[i].monthlyCard = ''
                }
            }
        }
    }

    closePayTypeLayout = () => {
        this.setState({
            isOpenedPayType: false
        })
    }

    payTypeChanged = (e) => {
        const { payTypeList } = this.state
        if (!e.detail.value[0])
            return
        payTypeList.map(v => (
            v.code + '' === e.detail.value[0] ? v.isChecked = true : v.isChecked = false
        ))

        this.setState({ payTypeList })
    }

    monthlyCardInput = (e) => {
        const { payTypeList } = this.state
        payTypeList.map(v => (
            v.isChecked ? v.monthlyCard = e.detail.value : ''
        ))
        this.setState({ payTypeList })
    }

    /**
     * 确定付款方式
     * @returns 
     */
    confirmPayType = async () => {
        const error = await this.validate()
        if (error) return this.showError(error)
        const { payTypeList } = this.state
        this.setState({
            isOpenedPayType: false,
            currentPayType: payTypeList.filter(x => x.isChecked)[0]
        })
    }

    showError(title, duration = 1500) {
        Taro.showToast({ title, icon: 'none', duration });
    }

    async validate() {
        const { payTypeList } = this.state
        let temp = payTypeList.filter(x => x.isChecked)[0]
        //寄付月结判断是否填写月结卡号
        if (temp.code === 3 && !temp.monthlyCard) return Promise.resolve('请输入月结卡号')
        return Promise.resolve()
    }

    selectRange = (e) => {
        const { expressList } = this.state;
        this.setState({
            expressType: expressList[e.detail.value]
        })
    }

    async validateOrderData() {
        const { currentAddress, list, expressType, remark } = this.state
        if (!currentAddress || !currentAddress.id) return Promise.resolve('请选择寄件人')
        //样本信息或备注未填写时提示至少填写一项
        if ((!list || list.length === 0) && !remark) return Promise.resolve('请录入样本或填写备注')
        //console.log(666, remark, list.length, list)
        //if  return Promise.resolve('请录入样本')
        if (!expressType.code) return Promise.resolve('请选择快件类别')
        return Promise.resolve()
    }

    /**
     * 创建订单的异步方法
     * 
     * 功能描述:
     * - 验证订单数据
     * - 获取用户信息和平台信息
     * - 构建订单参数，包括医院ID、平台ID、寄件地址、支付方式等
     * - 将样本列表数据转换为JSON字符串
     * - 调用创建订单API
     * - 处理下单成功/失败的响应
     * 
     * @async
     * @returns {Promise<void>}
     * @throws {Error} 当订单创建失败时显示错误提示
     */
    createOrder = async () => {
        const error = await this.validateOrderData()
        if (error) return this.showError(error)
        const userInfoStr = Taro.getStorageSync('userInfo');
        const userInfo = userInfoStr ? JSON.parse(userInfoStr) : {};
        //console.log(444, userInfo);
        const platformInfo = userInfo.extProps || {};
        console.log(555, platformInfo);
        const { currentAddress, list, currentPayType, expressType } = this.state

        // 将list数组转换为JSON字符串
        const sampleData = JSON.stringify(list.map(item => ({
            sampleNumber: item.sampleNo,
            productId: item.productLine,
            productName: item.productLineName,
            sonProductId: item.sonproductLine,
            sonProductName: item.sonproductLineName
        })));
        if (this.state.remark === undefined) { this.state.remark = '' }
        //console.log(6666, this.state.remark);
        let params = {
            hisId: userInfo.hisId || '',         // 医院ID（必填）
            platformId: userInfo.platformId || '', // 平台Id（必填）
            platformSource: userInfo.$compid__143 || '3', // 平台来源（必填）3
            subSource: userInfo.$compid__142 || '2',     // 子渠道2
            account: userInfo.account || '',
            senderAddressId: currentAddress.id,       // 寄件人地址id（必填）
            payMethod: currentPayType.code,           // 付款方式（必填）
            monthlyCard: currentPayType.monthlyCard || '', // 月结卡号
            productCode: 'OTHER',//寄样类型，前端没查询到
            expressType: expressType.code,  // 快递类型
            remark: this.state.remark, // 备注字段

            //录入产品信息上传
            //转换json字符串
            // list:  this.state.list.map(item => ({
            //     sampleNumber: item.sampleNo,                 // 样本编号
            //     productId: item.productLine,                 // 产品id
            //     productName: item.productLineName,           // 产品名称
            //     sonProductId: item.sonproductLine,           // 子产品id
            //     sonProductName: item.sonproductLineName      // 子产品名称
            // })),
            list: sampleData // 样本数据JSON字符串

        }
        // console.log('测试list', params.list)
        // console.log('测试remark', params.remark)
        const { code, data } = await API.createOrder(params)
        console.log('下单接口2', data,code)
        // const { res } = await API.createOrder(params)
        // console.log('下单接口返回全部信息    ', res)

        if (code !== 0) {
            Taro.showModal({
                title: '提示',
                content: data.resultMessage || '下单失败',
                showCancel: false,

            });
            console.log('下单失败')
        } else {
            Taro.showToast({
                title: '下单成功',
                icon: 'success',
                duration: 1500
            })
            setTimeout(this.redirectToList, 1500)
            console.log('下单成功')
        }
    }

    redirectToList = () => {
        Taro.redirectTo({
            url: '/pages/sample/send-list/index'
        })
    }

    clearSample = () => {
        this.setState({
            list: []
        })
    }

    render() {
        const { hideCamera, list, isOpenedPayType, payTypeList, currentPayType, currentAddress, receiverAddress, expressList, expressType } = this.state
        return (
            <View className={s.page}>
                <View className={s.list_item}>
                    <View className={s.list_item_info}>
                        <Image src={sendPng} className={s.list_item_icon}></Image>
                        {
                            currentAddress ?
                                <View className={s.list_item_address} onClick={() => this.goToDetail(currentAddress.id)}>
                                    <View className={['flex', 'f-c-center']}>
                                        <View className={s.list_item_name}>{currentAddress.userName}</View>
                                        <View className={s.list_item_mobile}>{currentAddress.mobile ? currentAddress.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''}</View>
                                    </View>
                                    <Text>{currentAddress.address}</Text>
                                </View>
                                :
                                <View className={s.list_item_address} onClick={() => this.goToDetail(0)}>
                                    <View className={['flex', 'f-c-center']}>
                                        <View className={s.list_item_newname}>新建寄件人</View>
                                    </View>
                                    <Text></Text>
                                </View>
                        }
                        <View className={s.list_item_rtext} onClick={this.goAddressList}>
                            <Image src={addressPng} className={s.list_item_icon}></Image>
                            <Text>地址簿</Text>
                        </View>
                    </View>
                    <View className={s.list_item_info}>
                        <Image src={receivePng} className={s.list_item_icon}></Image>
                        <View className={s.list_item_address}>
                            <View className={['flex', 'f-c-center']}>
                                <View className={s.list_item_name}>{receiverAddress.name}</View>
                                <View className={s.list_item_mobile}>{receiverAddress.mobile ? receiverAddress.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''}</View>
                            </View>
                            <Text>{receiverAddress.address}</Text>
                        </View>
                    </View>
                </View>
                <View className={s.list_item}>
                    <View className={s.header}>
                        <View className={s.header_title}>
                            <Text>请将条形码对准扫码单</Text>
                            <Image
                              className={s.header_title__icon}
                              src={scanPng}
                              open-type='openSetting'
                              onClick={this.openSetting}
                            />
                        </View>
                        {
                            hideCamera ? null :
                                <Camera
                                  className={s.header_camera}
                                  mode='scanCode'
                                  device-position='back'
                                  resolution='high'
                                  frame-size='large'
                                  flash='off'
                                  onInitDone={this.onCameraInitDone}
                                  onError={this.onCameraError}
                                  onScanCode={this.onCameraScanCode}
                                />
                        }
                    </View>
                </View> 
                <View className={s.list_item}>
                    <View className={['flex', 'f-m-between']}>
                        <Text className={s.list_item_tips}>待寄送样本（{this.state.list.length}）</Text>
                        {
                            list && list.length ?
                                <Text onClick={this.clearSample}>清空</Text> : null
                        }
                        
                    </View>
                    {list && list.length ? <View className={s.list_item_content}>
                        {list.map(item => (
                            <View key={item.sampleNo} className={s.list_item_sample}>
                                <Text className={s.list_item_sample_text} >
                                    {`${item.sampleNo} - ${item.productLineName} - ${item.sonproductLineName || '未选择子产品线'} `}
                                </Text>
                                <Image
                                  src={clearPng}
                                  className={s.list_item_sample_img}
                                  onClick={() => this.deleteSample(item.sampleNo)}
                                />
                            </View>
                        ))}
                    </View> : <Empty text='暂无样本' />}
                </View>
                <View className={s.list_item}>
                    <View className={[s.list_item_field, 'flex', 'f-c-center']}>
                        <Text className={s.list_item_paytype}>付款方式<Text>*</Text></Text>

                        <View className={['f-1', 'flex', 'f-m-end', 'f-c-center']} onClick={this.openPayTypeLayout}>
                            <View className={s.list_item_paytype_input}>{currentPayType.name ? currentPayType.name : (payTypeList && payTypeList.length ? payTypeList[0].name : '')}</View>
                            <Image src={arrowPng} className={s.list_item_arrow}></Image>
                        </View>
                    </View>
                    <View className={[s.list_item_field, 'flex', 'f-c-center']}>
                        <Text className={s.list_item_paytype}>快件类别<Text>*</Text></Text>

                        <Picker mode='selector' range={expressList} range-key='value' onChange={this.selectRange} className={['f-1']}>
                            <View className={['f-1', 'flex', 'f-m-end', 'f-c-center']}>
                                <View className={s.list_item_paytype_input}>{expressType.value || '请选择快件类别'}</View>
                                <Image src={arrowPng} className={s.list_item_arrow}></Image>
                            </View>
                        </Picker>
                    </View>

                </View>
                <View className={s.bottom}>
                    <Button className={s.bottom_btn} onClick={this.createOrder.bind(this)}>下单</Button>
                </View>

                <AtFloatLayout
                  title='样本信息录入'
                  isOpened={this.state.showScanPopup}
                  onClose={() => this.setState({ 
                    showScanPopup: false,
                    hideCamera: false  // 恢复相机
                })}
                >
                    <View className={s.scanPopup}>
                        {/* 样本编号显示 */}
                        <View className={s.formRow}>
                            <Text className={s.label}>样本编号</Text>
                        </View>

                        <View className={s.scanPopup}>
                            {/* <Text className={s.sampleNumber} >{this.state.currentSample.sampleNo}</Text> */}
                            <Input className={s.sampleNumber} type='text' value={this.state.currentSample.sampleNo} onInput={e => {
                                this.setState(prev => ({
                                    currentSample: {
                                        ...prev.currentSample,
                                        sampleNo: e.detail.value
                                    }
                                }));
                          }}></Input>

                        </View>

                        {/* 产品线选择 */}
                        <View className={s.formRow}>
                            <Text className={s.label}>产品线<Text>*</Text></Text>
                            <Picker
                              mode='selector'
                              range={this.state.productLines}
                              range-key='value'
                              value={this.state.productLines.findIndex(item => item.code === this.state.currentSample.productLine)}
                              onChange={e => {
                                    const selected = this.state.productLines[e.detail.value];
                                    //console.log(selected)
                                    this.setState(prev => ({
                                        currentSample: {
                                            ...prev.currentSample,
                                            productLine: selected.code, // 存储code
                                            sonproductLine: ''
                                        }
                                    }), () => {
                                        this.fetchSonProductLines(selected.code);
                                    });
                                }}
                            >
                                <View className={s.pickerTrigger}>
                                    <Text className={`${s.pickerText} ${!this.state.currentSample.productLine ? s.placeholder : ''}`}>
                                        {(this.state.productLines.find(item => item.code === this.state.currentSample.productLine) || {}).value
                                            || '请选择产品线'}
                                    </Text>

                                    {/* <Text className={`${s.pickerText} ${!this.state.currentSample.productLine ? s.placeholder : ''}`}>
                                        {(this.state.productLines.find(item => item.code === this.state.currentSample.productLine) || {}).value
                                            || '请选择产品线'}
                                    </Text> */}
                                    <View className={s.arrow}></View>
                                </View>
                            </Picker>
                        </View>

                        {/*选择子产品线*/}
                        <View className={s.formRow}>
                            <Text className={s.label}>子产品线</Text>
                            <Picker
                              mode='selector'
                              range={this.state.sonproductLines}
                              range-key='value'
                              value={this.state.sonproductLines.findIndex(item => item.code === this.state.currentSample.sonproductLine)}
                              onChange={e => {
                                    const selected = this.state.sonproductLines[e.detail.value];
                                    this.setState(prev => ({
                                        currentSample: {
                                            ...prev.currentSample,
                                            sonproductLine: selected.code
                                        }
                                    }));
                                }}
                            >
                                <View className={s.pickerTrigger}>
                                    <Text className={`${s.pickerText} ${!this.state.currentSample.sonproductLine ? s.placeholder : ''}`}>
                                        {(this.state.sonproductLines.find(item => item.code === this.state.currentSample.sonproductLine) || {}).value
                                        || '请选择子产品线'}
                                    </Text>
                                    <View className={s.arrow}></View>
                                </View>
                            </Picker>
                        </View>


                        {/* 下一个按钮 */}
                        <Button className={s.nextButton} onClick={this.handleNext}> 下一个</Button>
                    </View>

                </AtFloatLayout>
                <AtFloatLayout isOpened={isOpenedPayType} title='付款方式' onClose={this.closePayTypeLayout} >
                    {
                        payTypeList && payTypeList.length ?
                            <View>
                                <RadioGroup onChange={(e) => this.payTypeChanged(e)}>
                                    {
                                        payTypeList.map(v => (
                                            <View className={s.layout} key={v.code}>
                                                <View className={s.layout_item}>
                                                    <Text className={s.layout_item_text}>{v.name}</Text>
                                                    <Radio checked={v.isChecked} value={v.code} color='#3F969D' className={s.list_item_checkbox}></Radio>
                                                </View>
                                                <View>{v.memo}</View>
                                                {
                                                    v.code === 3 && v.isChecked ?
                                                        <View className={s.layout_monthly}>
                                                            <Text>月结卡号</Text>
                                                            <Input placeholder='请输入顺丰月结卡号' className={s.layout_monthly_input} onInput={e => this.monthlyCardInput(e)} value={v.monthlyCard}></Input>
                                                        </View> : null
                                                }
                                            </View>
                                        ))
                                    }
                                </RadioGroup>
                                <Button className={s.layout_btn} onClick={this.confirmPayType}>确定</Button>
                            </View>
                            : <Empty text='暂无付款方式' />
                    }
                </AtFloatLayout>

                <View className={s.list_item}>
                    <View className={[s.list_item_field2, 'flex', 'f-c-center']}>
                        <Text className={s.list_item_paytype2}>备注</Text>
                        <Textarea
                          className={s.list_item_remark_input}
                          maxlength='200'
                          placeholder='如存在试管无编号的情况，请注明产品和数量'
                          placeholderClass={s.placeholder}
                          value={this.state.remark}
                          onInput={(e) => this.setState({ remark: e.detail.value })}
                          confirm-type='done'
                        />
                    </View>
                    <View className={s.word_count}>
                        {this.state.remark == null ? '总字数 0/200' : `总字数 ${this.state.remark.length}/200`}

                    </View>
                </View>

            </View>
        )
    }
}
