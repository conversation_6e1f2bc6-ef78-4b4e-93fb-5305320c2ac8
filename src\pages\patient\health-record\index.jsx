import Taro, { Component } from '@tarojs/taro';
import { Image, Picker, View } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import { VisitingRecord } from './visiting-record/index';
import { BaseInfo } from './base-info/index';
import { UserInfo } from './user-info/index';
import { HospitalReport } from './hospital-report/index';
import { OutHospitalReport } from './out-hospital-report/index';

export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '健康档案',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);
    this.state = {
      // topbar: ['就诊记录', '基本信息', '院内报告', '院外报告'],
      topbar: ['患者信息', '院内报告', '院外报告'],
      topbarSelected: '0',
    };
  }

  componentDidMount() {

  }

  onChangeTopBar(index) {
    this.setState({ topbarSelected: index });
    this.changeTopBar(index);
  }

  changeTopBar(index) {
    const i = String(index || this.state.topbarSelected);
    this.setState({ activeIndex: i });
    // switch (i) {
    //   case '0':
    //     break;
    //   case '1':

    //     break;
    //   default:
    //     break;
    // }
  }

  render() {
    const {
      topbar,
      topbarSelected,
    } = this.state;
    const { patientPid } = this.$router.params;
    return (
      <View className={`${s.container}`}>
        <View className={s.topbarBox}>
          <View className={`${s.topBar} f-row`}>
            {
              topbar.map((item, index) => {
                return (
                  <View
                    key={index}
                    className={`${s.barItem} f-center f-center f-1 ${topbarSelected == index && s.barActive}`}
                    onClick={() => this.onChangeTopBar(index)}
                  >
                    {item}
                  </View>
                )
              })
            }
          </View>
        </View>
        {/* 就诊记录 */}
        {/* {topbarSelected == 0 && <VisitingRecord patientPid={patientPid} />} */}
        {/* 基本信息 */}
        {/* {topbarSelected == 1 && <BaseInfo patientPid={patientPid} />} */}
        {/* 患者信息 */}
        {topbarSelected == 0 && <UserInfo patientPid={patientPid} />}
        {/* 院内报告 */}
        {topbarSelected == 1 && <HospitalReport patientPid={patientPid}/>}
        {/* 院外报告 */}
        {topbarSelected == 2 && <OutHospitalReport patientPid={patientPid}/>}
      </View>
    );
  }
}
