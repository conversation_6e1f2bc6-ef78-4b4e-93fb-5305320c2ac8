import Taro, { Component } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import Collapse from '../collapse/index'


export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      detail: []
    };
  }

  componentDidMount() {
    this.queryReport();
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '会诊报告详情',
    navigationBarTextStyle: 'black'
  };

  queryReport = async () => {
    const {consultationId} = this.$router.params;
    const {code, data} = await Api.queryReportDetail({consultationId});
    if (code !== 0) return;
    this.transformData(data);
  }

  transformData = (data) => {
    const { patientVo, applyVo, opinionList = [], reportDetail} = data;
    const {userId: doctorId = ''} = JSON.parse(Taro.getStorageSync('userInfo') || '{}');
    const patientInfo = {
      title: '患者基本信息',
      data: [
        {label: '姓名', value: patientVo.patientName},
        {label: '性别', value: patientVo.patientSex==='M'?'男':'女'},
        {label: '年龄', value: patientVo.patientAge},
        {label: 'PID', value: patientVo.patientId},
      ],
      type: 2
    };
    const applyInfo = {
      title: '会诊申请信息',
      data: [
        {label: '申请单号', value: applyVo.orderNo},
        {label: '申请时间', value: applyVo.createTime},
        {label: '主诉', value: applyVo.mainIssue},
        {label: '现病史', value: applyVo.nowIssue},
        {label: '既往史', value: applyVo.historyIssue},
        {label: '现有诊断', value: applyVo.existDiagnosis},
        {label: '会诊目的', value: applyVo.purpose},
        {label: '会诊原因', value: applyVo.reason},
      ],
      type: 2
    };
    const detail = [patientInfo, applyInfo];
    const opinion = opinionList.find(item => +item.doctorId === +doctorId);
    if (opinion) {
      const opinionInfo = {
          title: '我的会诊意见',
          doctorName: opinion.doctorName,
          time: opinion.updateTime,
          data: opinion.report,
          type: 1,
          bottomView: true
        };
      detail.push(opinionInfo);
    }
    if (reportDetail) {
      const reportInfo = {
        title: '会诊报告',
        doctorName: reportDetail.doctorName,
        time: reportDetail.updateTime,
        data: reportDetail.report,
        type: 1,
        bottomView: true
      };
      detail.push(reportInfo);
    }

    this.setState({detail});
  }

  render() {
    const {detail} = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={`${s.collapseCss}`}>
          {detail.map((item, index) => {
            return <Collapse key={index} bottomView={item.bottomView || false} title={item.title} content={item.data} type={item.type} time={item.time} doctorName={item.doctorName} />;
          })}
        </View>
      </View>
    );
  }
}
