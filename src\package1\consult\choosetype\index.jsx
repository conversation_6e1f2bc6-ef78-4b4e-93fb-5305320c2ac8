import Taro, { Component } from "@tarojs/taro";
import { View, Image } from '@tarojs/components'
import Empty from '@/component/empty'
import * as Utils from "@/utils/utils";

import * as Api from './api'
import './style.scss'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      emptyConfigShow: false,
      chatList: [],
      patientsVoList: [],
      institutionId: '',
      institutionName: '',
      isShowConsult: false,
      isShowTeamConsult: false,
    }
  }

  componentDidShow() {
    let userInfo = Taro.getStorageSync('userInfo');
    userInfo = JSON.parse(userInfo)
    this.setState({
      chatList: [],
      patientsVoList: [],
      isShowConsult: false,
      institutionId: userInfo.institutionId,
      institutionName: userInfo.institutionName,
    }, this.queryPatChat)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '家辉咨询',
    navigationBarTextStyle: 'black'
  };

  openConsult = (item = {}, type = "redirectTo") => {
    // 是否有咨询内容 以跳转咨询详情或创建咨询
    const { groupId = "", chatType = '' } = item;
    if (groupId) {
      const url = `/package1/consult/chat/index?groupId=${groupId}&type=${chatType}`;
      if (type === "navigateTo") {
        // 跳转
        Taro.navigateTo({ url, type: item.chatType });
      } else {
        // 重定向
        Taro.redirectTo({ url });
      }
    } else {
      const param = {
        type: 1, // 免费聊天
        flg: item.chatType == 1 ? 0 : 1, // 单人聊天 0 多人聊天 1
        chatType: item.chatType || 2
      };
      this.createChat(param);
    }
  }
  openVideo = () => {
    Taro.navigateTo({
      url: `/package1/consult/video/index?groupId=606426459568865280`
    });
  }

  createChat = async(param = {}) => {
    const { institutionId, institutionName, isShowConsult } = this.state
    // 创建聊天
    param.institutionId = institutionId;
    param.name = institutionName;
    const { code, data = {} } = await Api.createChat(param);
    if (code == 0 && data.id) {
      Taro.redirectTo({
        url: `/package1/consult/chat/index?groupId=${data.id}`
      });
    } else {
      if (!isShowConsult) {
        this.setState({ emptyConfigShow: true })
      }
    }
  }
  /**
   * 当前患者是否已存在聊天会话
   */
  queryPatChat = async () => {
    const { institutionId } = this.state;
    const { data = [], code } = await Api.queryPatChat({ institutionId });
    if (code == 0) {
      let list = data;
      if (typeof data === "string") {
        list = JSON.parse(data) || [];
      }
      list.map(item => {
        if (!item.groupId && item.id) {
          item.groupId = item.id;
        }
        if (item.createTime || item.updateTime) {
          item.time = Utils.getChatShowTime(
            item.updateTime || item.createTime || ""
          );
        }
        return item;
      });
      if (list.length <= 0) {
        this.openConsult({
          chatType: 2
        })
      } else if (list.length === 2) {
        this.setState({
          emptyConfigShow: false,
          isShowConsult: true,
          isShowTeamConsult: true,
        })
      } else {
        this.judgeUserExsist(list);
      }
      list.sort((a,b)=> a.chatType - b.chatType);
      this.setState({ chatList: list })
    } else {
      this.setState({ emptyConfigShow: true })
    }
  }
  /**
   * 查询是否有家人已登录
   */
  judgeUserExsist = async (chatList) => {
    const { institutionId } = this.state;
    const { data = {}, code } = await Api.judgeUserExsist({ institutionId });
    // flg 0:直接跳过进入单人聊天界面 1:让用户选择进去群聊还是单聊，单聊的话，没有会直接新建单聊会话。
    if (code == 0) {
      const { flg, patientsVoList = [] } = data;
      if (Number(flg) === 0 && patientsVoList.length <= 1) {
        // 直接跳过进入单人聊天界面
        let singleChat;
        // 没有单人聊天
        if (patientsVoList.length === 0) {
          singleChat = {
            chatType: 1
          };
        } else {
          singleChat = chatList[0];
        }
        this.openConsult(singleChat)
      } else {
        // 有家属绑定 显示选择
        this.setState({ emptyConfigShow: false, isShowConsult: true })
        if (patientsVoList.length > 0) {
          this.setState({ patientsVoList: data.patientsVoList, isShowTeamConsult: true })
          // 如果查询到的聊天列表没有单人聊天
          if (chatList.length === 1) {
            chatList[0].chatType = 2;
            // chatList.unshift({
            //   chatType: 1,
            //   groupName: "单人聊天"
            // });
            // 组内聊天排前面
            chatList.sort((prev, next) => {
              return prev.chatType - next.chatType;
            });
            this.setState({ chatList })
          }
        }
      }
    }
  }

  render() {
    const { isShowConsult, isShowTeamConsult, chatList, emptyConfigShow, patientsVoList } = this.state
    return (
      <View>
        <View class='p-page'>
          { isShowConsult ?
          <View>
            <View class='m-list list-all-box'>
              { chatList.map((item, index) =>
              <View key={index}>
                { item.chatType != 1 ?
                <View class='list-item' onClick={() => this.openConsult(item, 'navigateTo')}>
                  <View class='item-box'>
                    <View class='item-hd'>
                      { item.chatType == 1 ? <Image mode='widthFix' src={`${$REPLACE_IMG_DOMAIN}/his-miniapp/p242/single.png`} /> : null }
                      { item.chatType == 2 ? <Image mode='widthFix' src={`${$REPLACE_IMG_DOMAIN}/his-miniapp/p242/team.png`} /> : null }
                    </View>
                    <View class='item-bd'>
                      <View class='bd-info'>
                        <View class='info-lt'>
                          <View class='lt-title'>{item.chatType == 1 ? '单独咨询' : '机构咨询'}</View>
                        </View>
                      </View>
                      { item.chatType == 2 && patientsVoList.length > 0 ?
                      <View class='bd-text'>
                        { patientsVoList.map((itm, idx) =>
                          <block key={idx}>
                            {itm.name}
                            { idx !== patientsVoList.length - 1 ? <text>、</text> : null }
                          </block>
                        )}
                      </View> : null }
                    </View>
                    { item.id ?
                    <View class='info-rt'>
                        <View class='bd-extra'>{item.time || item.updateTime || item.createTime}</View>
                        { item.unreadNum > 0 ? <View class='bd-extra active'>有未读消息</View> : null }
                        <View class='bd-extra' wx:else>暂无未读消息</View>
                    </View> :
                    <View class='info-rt'>
                        <View class='bd-extra'>点击创建咨询会话</View>
                    </View> }
                  </View>
                </View> : null }
              </View>
              )}
            </View>
            { isShowTeamConsult ? <View class='tips'>
                说明：您的机构成员已注册关联信息，您可以选择“机构咨询”同机构成员和医生护士一起交流。若成员信息有误，请联系客服处理。
            </View> : null }
          </View> : null }
          { emptyConfigShow ? <Empty text='未查询到可咨询信息'></Empty> : null }
        </View>
      </View>
    )
  }
}
