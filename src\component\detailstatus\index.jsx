import Taro, { Component } from '@tarojs/taro';
import { View, Image, RichText } from '@tarojs/components';
import { STATUS_MAP } from './config';
import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount() { }

  componentDidMount() {
    this.setStatus(this.props.statusConfig);
  }

  componentWillReceiveProps(nextProps) {
    this.setStatus(nextProps.statusConfig)
  }

  componentWillUnmount() { }

  componentDidShow() { }

  componentDidHide() { }

  setStatus(config = {}) {
    const { status = '' } = config;
    const statusObj = STATUS_MAP[status];
    console.log(statusObj);
    if (!statusObj) return;
    this.props.onSetNavColor(statusObj.navigationBarColor);// 根据订单状态设置导航颜色

    this.setState({
      statusClassName: statusObj.name || '',
    });
  }

  render() {
    const { text, detailData = {}, statusConfig = {}, leftTime } = this.props;
    const { statusClassName } = this.state;

    return (
      <View className={`wgt-detailstatus wgt-detailstatus-${statusClassName}`}>
        <View className='wgt-detailstatus-bd'>
          {/*<View className='wgt-detailstatus-bd-icon'>*/}
          {/*  {*/}
          {/*    statusClassName ?*/}
          {/*      <Image mode='widthFix' src={`${$CDN_DOMAIN}/detail-${statusClassName}.png`}></Image>*/}
          {/*      :*/}
          {/*      null*/}
          {/*  }*/}
          {/*</View>*/}
          <View className='wgt-detailstatus-bd-tit'>
            <View>{statusConfig.statusName}</View>
          </View>
          {
            detailData.hasRefund ?
              <View className='wgt-detailstatus-bd-label'>有退款1</View>
              :
              null
          }
          {
            detailData.status === 'L' ?
              <View className='wgt-detailstatus-bd-timer'>{leftTime}</View>
              :
              null
          }
        </View>
        {
          statusConfig.text && (
            <View className='wgt-detailstatus-ft'>
              <RichText nodes={statusConfig.text} />
            </View>
          )
        }

      </View>
    )
  }
}
