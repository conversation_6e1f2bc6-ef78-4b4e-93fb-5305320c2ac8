import Taro, { Component } from '@tarojs/taro';
import { Image, Picker, View, ScrollView } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import ReportView from './report-view/index';


export default class Index extends Component {

  constructor(props) {
    super(props);
    this.state = {
      status: true,
      fReportList: [],
      mReportList: [],
      fAbnormal: [],
      mAbnormal: [],
      mPics: [],
      fPics: [],
      pics: []
    };
  }

  componentDidMount = () => {
    this.queryReportOutHospitalDetail('F');
    this.queryReportOutHospitalDetail('M');
    this.queryReportOutHospital('F');
    this.queryReportOutHospital('M');
  }

  queryReportOutHospital = async (sex) => {
    const { patientPid: pid } = this.props;
    const param = {
      pid,
      sex,
      sortStatus: 0
    }
    const { code, data } = await Api.queryReportOutHospital(param);
    if (code == 0) {
      let param = {};
      let pics = [];
      data.map((item) => {
        pics.push(item.fileUrl);
      });
      if (sex == 'M') {
        param.mPics = pics;
        param.pics = pics;
      } else {
        param.fPics = pics;
      }
      this.setState(param);
    }
  }

  queryReportOutHospitalDetail = async (sex) => {
    const { patientPid: pid } = this.props;
    const param = {
      pid,
      sex,
    }
    const { code, data } = await Api.queryReportOutHospitalDetail(param);
    if (code == 0) {
      let param = {};
      if (sex == 'M') {
        param.mReportList = data;
        this.sortData(data, 'M');
      } else {
        param.fReportList = data;
        this.sortData(data, 'F');
      }
      this.setState(param);
    }
  }

  sortData = (data, sex) => {
    let abnormal = [];
    let param = {};
    data.map((item) => {
      if (item.result == 0) {
        abnormal.push(item);
      }
    });
    if (sex == 'M') {
      param.mAbnormal = abnormal;
    } else {
      param.fAbnormal = abnormal;
    }
    this.setState(param);
  }

  clickEvent = (val) => {
    const { fReportList, mReportList, fPics, mPics } = this.state;
    let param = { status: val };
    if (val) {
      param.reportList = mReportList;
      param.pics = mPics;
    } else {
      param.reportList = fReportList;
      param.pics = fPics;
    }
    this.setState(param)
  }

  bigImg = (index) => {
    const { pics } = this.state;
    Taro.previewImage({
      current: pics[index], // 当前显示图片的http链接
      urls: pics // 需要预览的图片http链接列表
    })
  }

  render() {
    const {
      status,
      pics,
      fAbnormal,
      mAbnormal,
      mReportList,
      fReportList
    } = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={`${s.type}`}>待分类报告</View>
        <ScrollView className={`${s.imgModule}`} scrollX>
          {pics.length > 0 ?
            pics.map((item, index) => {
              return <Image onClick={() => this.bigImg(index)} style={{ width: '80px', height: '80px', margin: '0 10px' }} width={80} height={80} key={index} src={item} ></Image>
            })
            : <View className={`${s.noReport}`}>暂无报告</View>}
        </ScrollView>
        <View className={`${s.type}`}>已分类报告</View>
        <View className={`${s.reportItems}`}>
          <View onClick={() => this.clickEvent(true)} style={{ opacity: status ? 1 : 0.5 }}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/man.png')} />
            <View className={`${s.reportName}`}>男方报告</View>
          </View>
          <View onClick={() => this.clickEvent(false)} style={{ opacity: !status ? 1 : 0.5 }}>
            <Image className={`${s.imgCss}`} src={require('../../../../static/image/woman.png')} />
            <View className={`${s.reportName}`}>女方报告</View>
          </View>
        </View>
        <ReportView title={'异常指标'} data={status ? mAbnormal : fAbnormal} key={index} />
        <View className={`${s.ispadding}`}>
          <ReportView title={'所有结果'} data={status ? mReportList : fReportList} key={index} />
        </View>
      </View>
    );
  }
}
