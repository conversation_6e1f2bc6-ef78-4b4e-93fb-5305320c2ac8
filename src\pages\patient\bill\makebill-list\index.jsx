import Taro, { Component } from '@tarojs/taro';
import { View, Text, Icon, Input, Picker, Block } from '@tarojs/components';
import { Tabs } from '@/component/Tabs'
import Empty from '@/component/empty';

import s from './index.module.scss'
import * as API from './api'

export const STATUS_MAP = {
  S: '缴费成功',
  F: '缴费失败',
  H: '异常',
  Z: '异常',
}

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      activeTab: 1,
      list: [{ label: '我的开单', value: 1 }, { label: '患者扫码开单', value: 2, }],
      records: [],
      endTime: '',
      startTime: '',
      patientName: ''
    }
  }

  componentWillMount() {


  }

  componentDidShow() {
    const currentDate = new Date();

    currentDate.setMonth(currentDate.getMonth() - 1);
    currentDate.setFullYear(currentDate.getFullYear() - 1);

    const currentYear = new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)) + '-' + (new Date().getDate() < 10 ? '0' + (new Date().getDate()) : new Date().getDate());

    const oneMonthAgo = new Date().getFullYear() + '-' + ((currentDate.getMonth() + 1) < 10 ? '0' + (currentDate.getMonth() + 1) : (currentDate.getMonth() + 1)) + '-' + (new Date().getDate() < 10 ? '0' + (new Date().getDate()) : new Date().getDate());
    this.setState(() => ({
      startTime: oneMonthAgo,
      endTime: currentYear
    }), () => {
      this.getPatientRecords()
    });
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '开单记录',
    navigationBarTextStyle: 'black',
  };


  /** 医生开单记录 */
  // getRecods = async () => {
  //   const { code, data } = await API.getBillRecords()
  //   if (code !== 0) return
  //   const records = data.extorderList || []
  //   this.setState({ records, })
  // }

  /** 患者扫码开单记录 */
  getPatientRecords = async () => {
    const { endTime, startTime, patientName } = this.state;
    const { code, data } = await API.getPatientRecords({
      startTime,
      endTime,
      patientName
    })
    if (code !== 0) return
    const records = data.extorderList || []
    this.setState({ records })
    this.getPerfectInfo(records)

  }

  getPerfectInfo = async (list) => {
    if (list.lenth === 0) return
    for (let item of list) {
      if (item.sampleNumber) {
        const { code, data } = await API.getBySamplenumber({ id: item.sampleNumber })
        if (code == 0) {
          item.sjzReadyFlag = data.sjzReadyFlag ? data.sjzReadyFlag : ''
          item.sampleReadyFlag = data.sampleReadyFlag ? data.sampleReadyFlag : ''
          item.sampleNumber = data.sampleNumber
          item.extfiled1 = data.extfiled1;
        }
      }

    }
    this.setState({ records: list })
  }

  toDetail = (id, xcxSamplePrefectFlag, sampleNumber, extfiled1) => {
    Taro.navigateTo({ url: `/pages/patient/bill/makebill-detail/index?id=${id}&xcxSamplePrefectFlag=${xcxSamplePrefectFlag}&sampleNumber=${sampleNumber}&extfiled1=${extfiled1}` })
  }

  bindSearchInput = (e) => {
    const { value } = e.detail;
    this.setState({ patientName: value });

    return value;
  }

  onDateChange = (e, key) => {
    const value = e.detail.value
    this.setState(() => ({
      [key]: value
    }), () => {
      this.getPatientRecords()
    });
  }

  reset = () => {
    this.setState({
      startTime: '',
      endTime: '',
    })
  }

  render() {
    const { activeTab, list, records, startTime, endTime, patientName } = this.state
    return (
      <View className={s.page}>
        <View className={s.page_header}>
          {/* <Tabs value={activeTab} list={list} setValue={this.handleSwitchTabs} /> */}
          <View className={[s.search, 'f-center']}>
            <icon style='margin-right: 8px;' type='search' size='16' color='rgba(0, 0, 0, 0.4)' />
            <Input
              className='f-1'
              placeholder='请输入受检者姓名'
              confirm-type="search"
              value={patientName}
              onInput={this.bindSearchInput}
              onConfirm={this.getPatientRecords}
            />

          </View>
          <View className={s.header_daterange}>
            <Picker
              className={s.header_daterange__picker}
              mode='date'
              value={startTime}
              onChange={e => this.onDateChange(e, 'startTime')}
            >
              <View className={s.picker_content}>{startTime || '开始日期'}</View>
            </Picker>
            <Picker
              className={s.header_daterange__picker}
              mode='date'
              value={endTime}
              onChange={e => this.onDateChange(e, 'endTime')}
            >
              <View className={s.picker_content}>{endTime || '结束日期'}</View>
            </Picker>
            {startTime || endTime ?
              <Text className={s.header_daterange__clear} onClick={this.reset}>清空</Text> :
              <Text className={s.header_daterange__clear}></Text>}
          </View>
        </View>

        <View className={s.page_body}>
          {
            records.length ? records.map(v => (
              <View key={v.id} className={s.list_item} onClick={() => this.toDetail(v.id, v.xcxSamplePrefectFlag, v.sampleNumber, v.extfiled1)}>
                <View className={[s.list_item_status, 'f-row', 'f-c-center']}>
                  <Icon
                    style={{ marginRight: '12px' }}
                    type={v.status === 'S' ? 'success' : v.status === 'F' ? 'cancel' : 'info'}
                    color={v.status === 'S' ? '#30A1A6' : v.status === 'F' ? '#C55D5D' : '#E7AA35'}
                  />
                  <Text>{STATUS_MAP[v.status]}</Text>
                  {
                    v.xcxSamplePrefectFlag == '1' &&
                    <Text>
                      <Text className={[v.sampleReadyFlag === '1' ? '' : s.offline, s.info_box]}>样本信息</Text>
                      <Text className={[v.sjzReadyFlag === '1' ? '' : s.offline, s.info_box]}>受检者信息</Text>
                    </Text>
                  }
                </View>
                <View className={[s.list_item_row, 'f-row', 'f-c-center']}>
                  <Text>就诊人：</Text>
                  <Text>{v.patientName}</Text>
                </View>
                <View className={[s.list_item_row, 'f-row', 'f-c-center']}>
                  <Text>支付金额：</Text>
                  <Text>{(v.totalFee / 100).toFixed(2)}</Text>
                </View>
                <View className={[s.list_item_row, 'f-row', 'f-c-center']}>
                  <Text>支付时间：</Text>
                  <Text>{v.payedTime}</Text>
                </View>
              </View>
            ))
              :
              <Empty text='暂无记录' />
          }
        </View>
      </View>
    )
  }
}
