import Taro, { Component } from '@tarojs/taro';
import { View, Icon, Text, Button, Image } from '@tarojs/components';

import s from './index.module.scss'
import * as API from './api'

export const STATUS_MAP = {
  S: '缴费成功',
  F: '缴费失败',
  H: '系统异常',
  Z: '系统异常',
}

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      detail: {},
      xcxSamplePrefectFlag: '',
      sampleNumber: '',
      extfiled1: ''
    }
  }



  componentWillMount() {
  }

  componentDidShow() {
    const { id, xcxSamplePrefectFlag, sampleNumber, extfiled1 } = this.$router.params;
    this.setState({ xcxSamplePrefectFlag, sampleNumber, extfiled1 })
    this.getDetail(id)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '开单详情',
    navigationBarTextStyle: 'black',
  };

  async getDetail(orderId) {
    const { code, data } = await API.getDetail({ orderId })
    if (code !==0) return
    this.setState({ detail: data })
  }

  generateProducts(extFieldsViews) {
    try {
      const fieldsViews = JSON.parse(extFieldsViews)
      const list = (fieldsViews.products || []).map(v => ({ label: v.productName, value: v.productprice, productId: v.productId }))
      if (fieldsViews.isMerge) {
        list.push({
          label: '加急',
          value: 2000,
        })
      }
      if (fieldsViews.reportMailingType == 2) {
        list.push({
          label: '快递费',
          value: fieldsViews.reportMailingFees,
        })
      }
      return list
    } catch (error) {
      
    }
  }

  goPerfect = () => {
    const { detail, sampleNumber, extfiled1 } = this.state
    const products = this.generateProducts(detail.extFieldsViews) || []
    const productId = products.length ? products[0].productId : ''
    if(extfiled1 && extfiled1 == '3') {
      Taro.navigateTo({ url: `/pages/patient/bill/perfectinformationfetail/index?patientName=${detail.patientName}&idNumber=${detail.idNumber}&mobile=${detail.mobile}&orderId=${detail.id}&productId=${productId}&deptId=${detail.deptId}&deptName=${detail.deptName}&sampleNumber=${detail.sampleNumber}`})
    }else{
      if(detail.templateType && detail.templateType === 'XB'){
        Taro.navigateTo({ url: `/pages/patient/bill/perfectinformation/index?patientName=${detail.patientName}&idNumber=${detail.idNumber}&mobile=${detail.mobile}&orderId=${detail.id}&productId=${productId}&deptId=${detail.deptId}&deptName=${detail.deptName}&sampleNumber=${detail.sampleNumber}`})
      }else{
        Taro.showModal({
          title: "温馨提示",
          content:
            "暂不支持该类型产品的样本信息移动端录入。",
          showCancel: false,
          confirmText: "确定",
          confirmColor: "#3CC51F"
        });
      }
      
    }
  }

  render() {
    const { detail, xcxSamplePrefectFlag, sampleNumber, extfiled1 } = this.state
    const payCertificate = JSON.parse(detail.payCertificate)
    const products = this.generateProducts(detail.extFieldsViews) || []
    const productId = products.length ? products[0].productId : ''
    return (
      <View className={s.page}>
        <View className={[s.header]}>
          <View className={['f-row', 'f-c-center']}>
            <Icon
              style={{ marginRight: '12px' }}
              size='64rpx'
              type={detail.status === 'S' ? 'success' : detail.status === 'F' ? 'cancel' : 'info'}
              color={detail.status === 'S' ? '#30A1A6' : detail.status === 'F' ? '#C55D5D' : '#E7AA35'}
            />
            <Text
              className={s.header_title}
              style={{ color: detail.status === 'S' ? '#30A1A6' : detail.status === 'F' ? '#C55D5D' : '#E7AA35' }}
            >{STATUS_MAP[detail.status]}</Text>
          </View>
          <View className={s.header_tips}>
            { detail.status === 'S' ?
              '扫码开单并缴费成功，请按医生要求进行相关检测。' :
              detail.status === 'F' ?
                '您的缴费提交失败，请返回重试，或者联系医院工作人员。若已缴费且未自动退费，请联系医院工作人员核实处理。' :
                '您的缴费提交异常，请返回重试，或者联系医院工作人员。若已缴费且未自动退费，请联系医院工作人员核实处理。'
            }
          </View>
        </View>
        {
          xcxSamplePrefectFlag === '1' &&
          <Button
            className={s.btn}
            onClick={this.goPerfect}
          >{extfiled1 == '3' ? '查看' : '完善'}样本信息</Button>
        }

        <View className={s.page_body}>

          <View className={s.block}>
            <View className={[s.fee_summary, 'f-row', 'f-c-center', 'f-m-between']}>
              <View className={s.fee_summary_left}>缴费明细</View>
              <View className={s.fee_summary_right}>
                金额：<Text className={s.fee_summary_right_money}>{ (detail.totalFee / 100).toFixed(2)}</Text>
              </View>
            </View>

            {/* 费用明细表格 */}
            <View className={s.table}>
              <View className={s.table_row}>
                <View className={[s.table_col, s.table_col_h]}>费用名称</View>
                <View className={[s.table_col, s.table_col_h]}>金额(元)</View>
              </View>
              { (products || []).map((v, i) => (
                <View className={s.table_row} key={i}>
                  <View className={s.table_col}>{v.label}</View>
                  <View className={s.table_col}>{(v.value/100).toFixed(2)}</View>
                </View>
              )) }
            </View>


            <View className={s.title}>缴费信息</View>

            <View className={s.line}>
              <Text className={s.line_label}>姓名</Text>
              <Text className={s.line_value}>{detail.patientName}</Text>
            </View>
            <View className={s.line}>
              <Text className={s.line_label}>证件号码</Text>
              <Text className={s.line_value}>{detail.idNumber}</Text>
            </View>
            {/* <View className={s.line}>
              <Text className={s.line_label}>开单医生</Text>
              <Text className={s.line_value}>{detail.hisSerialNo}</Text>
            </View>
            <View className={s.line}>
              <Text className={s.line_label}>开单医院</Text>
              <Text className={s.line_value}>{detail.hisName}</Text>
            </View> */}
            <View className={s.line}>
              <Text className={s.line_label}>支付方式</Text>
              <Text className={s.line_value}>{detail.payMethod}</Text>
            </View>
            <View className={s.line}>
              <Text className={s.line_label}>订单金额</Text>
              <Text className={s.line_value}>{(detail.totalRealFee/100).toFixed(2)}</Text>
            </View>
            <View className={s.line}>
              <Text className={s.line_label}>订单时间</Text>
              <Text className={s.line_value}>{detail.payedTime}</Text>
            </View>
            {
              payCertificate.length == 0 &&
              <View className={s.line}>
              <Text className={s.line_label}>支付流水号</Text>
              <Text className={s.line_value}>{detail.agtOrdNum}</Text>
            </View>
            }
            
            <View className={s.line}>
              <Text className={s.line_label}>平台订单号</Text>
              <Text className={s.line_value}>{detail.id}</Text>
            </View>
            {
              payCertificate.length &&
              <View className={s.line}>
                <Text className={s.line_label}>门诊缴费凭证</Text>
                <View>
                  {
                    payCertificate.map(v => (
                      <Image className={s.img} key={v} src={v}/>
                    ))
                  }
                  
                </View>
              </View>
            }
            
          </View>
        </View>
      </View>
    )
  }
}
