import Taro, { Component } from "@tarojs/taro";
import {
  Button,
  Image,
  Navigator,
  ScrollView,
  Text,
  View,
} from "@tarojs/components";
import s from "./index.module.scss";
import * as Api from "./api";
import WPage from "@/component/wpage";
import DetailStatus from "@/component/detailstatus";

export default class Index extends Component {
  config = {
    backgroundTextStyle: "light",
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "开单详情",
    navigationBarTextStyle: "black",
  };

  constructor(props) {
    super(props);
    this.state = {
      id: "",
      detail: {},
      status: "",
      isExpandPatient: true,
      statusConfig: {},
      isNew: 0,
    };
  }

  componentWillMount() {}

  componentDidMount() {
    const { id, isNew } = this.$router.params;
    this.setState({ id, isNew }, this.queryRecordDetail);
  }

  componentWillUnmount() {
    const { isNew } = this.state;
    if (isNew == 1) {
      Taro.navigateBack({
        delta: 2,
      });
    }
  }

  componentDidShow() {}

  componentDidHide() {}

  setNavColor = (colorParam) => {
    Taro.setNavigationBarColor(colorParam);
  };

  queryRecordDetail = async () => {
    const { id } = this.state;
    const { code, data: detail } = await Api.queryRecordDetail({ id });
    if (code === 0) {
      detail.fileUrlJsonArray =
        detail.fileUrlJsonArray && JSON.parse(detail.fileUrlJsonArray || "{}");
      detail.prescriptionJsonArray =
        detail.prescriptionJsonArray &&
        JSON.parse(detail.prescriptionJsonArray || "{}");
      const prescriptionFemale = detail.prescriptionJsonArray.female.map((item, index) => {
        return item.prescriptionName;
      });
      const prescriptionMale = detail.prescriptionJsonArray.male.map((item, index) => {
        return item.prescriptionName;
      })
      detail.prescriptionFemale = prescriptionFemale.join(',');
      detail.prescriptionMale = prescriptionMale.join(',');
      console.log("detail", detail);

      const statusConfig = this.genConfig(detail.status);
      this.setState({ detail, status: detail.status, statusConfig });
    }
  };

  repeatApply = async () => {
    const { detail = {} } = this.state;
    const { relationPatientId = '', patientName = '', doctorId = '', doctorid = '' } = detail
    const { patientPid } = this.$router.params;
    
    let param = {
      relationPatientId,
      patientPid,
      patientName,
      doctorId: doctorid,
      consultationType: "referral",
    };
    const { code, data } = await Api.createConsultation(param);
    if (code == 0) {
      const { orderNo, consultationId } = data;
      const {
        medicalHistory,
        prescriptionJsonArray,
        diagnosisFemale,
        diagnosisMale,
        fileUrlJsonArray,
      } = detail;
      let applyParam = {
        orderNo,
        medicalHistory,
        prescriptionJsonArray: prescriptionJsonArray && JSON.stringify(prescriptionJsonArray) || '',
        diagnosisFemale,
        diagnosisMale,
        fileUrlJsonArray: fileUrlJsonArray && JSON.stringify(fileUrlJsonArray) || '',
      };
      const { code: applyCode } = await Api.applyConsultation(applyParam);
      if (applyCode == 0) {
        Taro.showToast({
          title: "重新提交成功",
          icon: "success",
          duration: 1000,
        });
        const that = this;
        setTimeout(() => {
          that.setState({ id: consultationId }, that.queryRecordDetail)          
        }, 2000);
      }
    }
  }

  genConfig(status) {
    switch (status) {
      case "0":
        return { statusName: "待审核", status: "P" };
      case "1":
        return { statusName: "已通过", status: "S" };
      case "2":
        return { statusName: "转诊中", status: "S" };
      case "3":
        return { statusName: "已结束", status: "S" };
      case "4":
        return { statusName: "已驳回", status: "F" };
      case "5":
        return { statusName: "已取消申请", status: "C" };
      case "6":
        return { statusName: "已取消", status: "C" };
    }
  }

  previewImage = (idx, keyValue) => {
    console.log("idx", idx);
    console.log("keyValue", keyValue);
    const { detail } = this.state;
    const { fileUrlJsonArray = {} } = detail;
    const urls =
      fileUrlJsonArray[keyValue].length &&
      fileUrlJsonArray[keyValue].map((item) => item.fileUrl);
    console.log("urls", urls);
    Taro.previewImage({ current: urls[idx], urls });
  };

  cancelApply = async () => {
    const _this = this;
    const { detail } = this.state;
    Taro.showModal({
      title: "温馨提示",
      content: "确定取消申请？",
      success: async function (res) {
        if (res.confirm) {
          const { code } = await Api.cancelApply({
            consultationId: detail.consultationId,
          });
          if (code !== 0) return;
          Taro.showToast({
            title: "取消成功",
            icon: "success",
          });
          setTimeout(_this.queryRecordDetail, 2000);
        }
      },
    });
  };

  render() {
    const { detail = {}, status, statusConfig, isExpandPatient } = this.state;

    const { fileUrlJsonArray = {} } = detail;
    console.log("fileUrlJsonArray", fileUrlJsonArray);
    return (
      <WPage>
        <DetailStatus
          onSetNavColor={this.setNavColor}
          statusConfig={statusConfig}
        />
        <ScrollView style={{ paddingBottom: "50px" }}>
          <View className={s.container}>
            <View className={s.row}>
              <Text>申请单号</Text>
              <Text>{detail.orderNo}</Text>
            </View>

            {
              detail.applyTime &&
              <View className={s.row}>
              <Text>申请时间</Text>
              <Text>{detail.applyTime}</Text>
            </View>}
            {status !== 0 && status !== 5 && detail.reviewTime && (
              <View className={s.row}>
                <Text>审核时间</Text>
                <Text>{detail.reviewTime}</Text>
              </View>
            )}
            {detail.extFields &&
              <View className={s.row}>
                <Text>审核备注</Text>
                <Text>{`${detail.extFields || ""}`}</Text>
              </View>
            }
            <View className={s.row}>
              <Text>申请方</Text>
              <Text>{`${detail.doctorHospitalName || ""} ${
                detail.doctorName || ""
              }`}</Text>
            </View>

            {(status === 2 || status === 3 || status === 6) && (
              <View className={s.row}>
                <Text>受邀医师</Text>
                <Text>
                  {detail.doctorList
                    .map((doctor) => {
                      return `${doctor.name}${
                        doctor.userType === "1" ? "(主诊)" : ""
                      }\n`;
                    })
                    .join("")}
                </Text>
              </View>
            )}

            <View className={s.row}>
              <Text>患者信息</Text>
              <Text>{detail.patientName}</Text>
            </View>
            {detail.remarks &&
              <View className={s.row}>
              <Text>申请备注</Text>
              <Text>{`${detail.remarks || ""}`}</Text>
            </View>}
            <View
              style={{ marginTop: "16px" }}
              className={`${s.patientDetail} ${isExpandPatient && s.expand}`}
            >
              <View
                className={s.row}
                onClick={() =>
                  this.setState({ isExpandPatient: !isExpandPatient })
                }
              >
                <Text>患者详情</Text>
                <Text style={{ fontWeight: 600 }}>
                  {isExpandPatient ? "收起" : "展开"}
                  <Text className={s.arrow}>⟩</Text>
                </Text>
              </View>
              <View>
                <View className={s.cardItem}>
                  <Text>基本病史：</Text>
                  <View className={s.content}>{detail.medicalHistory}</View>
                  {fileUrlJsonArray.medicalHistoryFile.length && (
                    <View className={`${s.imgbox}`}>
                      {fileUrlJsonArray.medicalHistoryFile.map(
                        (item, index) => (
                          <Image
                            mode="aspectFit"
                            className={s.img}
                            src={item.fileUrl}
                            onClick={this.previewImage.bind(
                              this,
                              index,
                              "medicalHistoryFile"
                            )}
                          />
                        )
                      )}
                    </View>
                  )}
                </View>

                {fileUrlJsonArray.pelvicOperationHistoryFile.length && (
                  <View className={`${s.imgbox} ${s.row}`}>
                    {fileUrlJsonArray.pelvicOperationHistoryFile.map(
                      (item, index) => (
                        <Image
                          mode="aspectFit"
                          className={s.img}
                          src={item.fileUrl}
                          onClick={this.previewImage.bind(
                            this,
                            index,
                            "pelvicOperationHistoryFile"
                          )}
                        />
                      )
                    )}
                  </View>
                )}

                {fileUrlJsonArray.spermMaleFile.length && (
                  <View className={`${s.imgbox} ${s.row}`}>
                    {fileUrlJsonArray.spermMaleFile.map((item, index) => (
                      <Image
                        mode="aspectFit"
                        className={s.img}
                        src={item.fileUrl}
                        onClick={this.previewImage.bind(
                          this,
                          index,
                          "spermMaleFile"
                        )}
                      />
                    ))}
                  </View>
                )}

                {fileUrlJsonArray.hereditaryDiseasesFile.length && (
                  <View className={`${s.imgbox} ${s.row}`}>
                    {fileUrlJsonArray.hereditaryDiseasesFile.map(
                      (item, index) => (
                        <Image
                          mode="aspectFit"
                          className={s.img}
                          src={item.fileUrl}
                          onClick={this.previewImage.bind(
                            this,
                            index,
                            "hereditaryDiseasesFile"
                          )}
                        />
                      )
                    )}
                  </View>
                )}

                <View className={s.row}>
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>
                    疾病诊断：
                  </Text>
                  <Text></Text>
                </View>
                <View className={s.row}>
                  <Text>女方：</Text>
                  <Text>{detail.diagnosisFemale}</Text>
                </View>
                <View className={s.row}>
                  <Text>男方：</Text>
                  <Text>{detail.diagnosisMale}</Text>
                </View>

                <View className={s.row}>
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>
                    开单项目：
                  </Text>
                  <Text></Text>
                </View>
                <View className={s.row}>
                  <Text>女方：</Text>
                  <Text>{detail.prescriptionFemale}</Text>
                </View>
                <View className={s.row}>
                  <Text>男方：</Text>
                  <Text>{detail.prescriptionMale}</Text>
                </View>
              </View>
            </View>
            {/* <View className={`${s.imgbox} ${s.row}`}>
            {fileUrlJsonArray.hereditaryDiseasesFile.length &&
              fileUrlJsonArray.hereditaryDiseasesFile.map((item, index) => (
                  <Image mode="aspectFit" className={s.img} src={item.fileUrl} onClick={this.previewImage.bind(this, index, 'hereditaryDiseasesFile')}/>
              ))}
            </View> */}
            {fileUrlJsonArray.otherFile.length > 0 && (
              <View className={`${s.attachFile} ${s.row}`}>
                <Text>附件</Text>
                <View
                  className={s.previewBtn}
                  onClick={this.previewImage.bind(this, 0, "otherFile")}
                >
                  <Image src={fileUrlJsonArray.otherFile[0].fileUrl} />
                  <Text>点击查看图片</Text>
                </View>
              </View>
            )}

            {status === 3 && (
              <View className={`${s.record} ${s.row}`}>
                <Text>转诊报告</Text>
                <Navigator
                  url={`/pages/consultation-opinion/consultation-report/index?consultationId=${detail.consultationId}`}
                >
                  详细
                </Navigator>
              </View>
            )}
          </View>

          {status === '0' && (
            <Button className={s.cancelBtn} onClick={this.cancelApply}>
              取消申请
            </Button>
          )}

          
          {/* {status === '5' && (
            <Button className={s.cancelBtn} onClick={this.repeatApply}>
              重新申请
            </Button>
          )} */}
          

          {detail.shouldEnter && status === 2 && (
            <Navigator
              className={s.entryBtn}
              url={`/pages/consultation/chat/index?id=${
                detail.chatGroupId
              }&isEndFeeType=${0}&chatType=${3}&patName=${
                detail.patientName
              }&consultationId=${detail.consultationId}&pid=${
                detail.patientPid
              }`}
            >
              进入转诊间
            </Navigator>
          )}
        </ScrollView>
      </WPage>
    );
  }
}
