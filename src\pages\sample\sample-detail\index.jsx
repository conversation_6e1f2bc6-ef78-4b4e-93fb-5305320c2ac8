import Taro, { Component } from '@tarojs/taro';
import { View, Text, Image, Input } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';

export default class SampleDetail extends Component {
  constructor(props) {
    super(props);
    this.state = {
      sampleCode: '', // 样本条码
      sjzName: '', // 姓名
      sjzPhone: '', // 手机号
      questionnaireStatus: '0', // 问卷状态：0-未填写，1-已填写
      signStatus: '0', // 签署状态：0-未签署，1-已签署
      reportStatus: '0', // 报告状态：0-未出具，3-已出具
      timestamp: Date.now(), // 时间戳用于条形码
      userInfo: null, // 用户信息
      showBasicInfoModal: false, // 是否显示基础信息弹窗
      orderDetail: null, // 订单详情
      // 基础信息相关字段
      sjzAge: '', // 年龄
      sjzSex: '', // 性别
      sjzIdNum: '', // 证件号码
      sjzHeight: '', // 身高
      sjzWeight: '', // 体重
      // 监护人信息（儿童版）
      jhrName: '', // 监护人姓名
      jhrIdNum: '', // 监护人证件号
      jhrPhone: '', // 监护人电话
      // 其他字段
      appId: '', // 小程序AppId
      pagePath: '', // 小程序页面路径
      orderCode: '', // 订单编码
      sampleId: '', // 样本ID
      userId: '', // 用户ID
      hisId: '', // 医院ID
      infoStatus: '0', // 信息状态
      collectionStatus: '0', // 采集状态
      // 样本管条码相关
      sampleTubeCode: '', // 样本管条码
      tempInputValue: '', // 临时输入值
      showScanInput: false, // 是否显示扫描输入界面
      signFileList: [], // 知情同意书文件列表
      signPdfUrl: '', // 签署的PDF文件URL
      showConsentModal: false, // 是否显示知情同意书弹窗
      // 问卷相关状态
      surveyId: '77', // 默认问卷ID
      adultSurveyId: '77', // 成人版问卷ID
      childSurveyId: '78', // 儿童版问卷ID
      selectedType: 'adult', // 选择的问卷类型：adult/child
      questionUserId: '', // 问卷用户ID
      titleList: [], // 问卷题目列表
      questionType: '1', // 问卷类型：1-成人版，2-儿童版
      // 样本处理进度相关状态
      sampleTime: '', // 样本采集时间
      receiveTime: '', // 实验室收取时间
      analysisTime: '', // 实验分析时间
      reportTime: '' // 报告出具时间
    };
  }

  componentDidMount() {
    const { id, sampleCode, userInfo } = this.$router.params;

    console.log('页面参数:', { id, sampleCode, userInfo });

    // 确定要使用的ID
    let targetId = id || sampleCode;

    if (userInfo) {
      // 如果有用户信息参数，先解析并设置状态
      try {
        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfo));
        console.log('解析的用户信息:', parsedUserInfo);

        // 从用户信息中获取ID
        if (!targetId && parsedUserInfo.sampleCode) {
          targetId = parsedUserInfo.sampleCode;
        }

        this.setState({
          sampleCode: sampleCode || parsedUserInfo.sampleCode,
          sjzName: parsedUserInfo.sjzName ,
          sjzPhone: parsedUserInfo.sjzPhone,
          userInfo: parsedUserInfo,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }

    // 无论是否有userInfo，都要调用API加载完整数据
    if (targetId) {
      console.log('使用ID加载样本数据:', targetId);
      this.loadSampleData(targetId);
    } else {
      console.warn('没有找到有效的ID参数，无法加载样本数据');
      Taro.showToast({
        title: '缺少样本ID参数',
        icon: 'none'
      });
    }

    // 监听问卷提交成功事件
    Taro.eventCenter.on('refreshSampleDetail', this.handleRefreshSampleDetail);
  }

  // 页面显示时触发，用于处理从其他小程序返回时的数据刷新
  componentDidShow() {
    console.log('页面显示，检查是否需要刷新数据');

    // 如果页面已经加载过数据，则刷新数据
    const { sampleCode } = this.state;
    const { id, sampleCode: paramSampleCode } = this.$router.params;
    const targetId = id || paramSampleCode || sampleCode;

    if (targetId && this.hasLoadedData) {
      console.log('从其他小程序返回，刷新页面数据:', targetId);
      this.refreshPageData(targetId);
    }
  }

  componentWillUnmount() {
    // 清理事件监听
    Taro.eventCenter.off('refreshSampleDetail', this.handleRefreshSampleDetail);
  }

  // 处理刷新样本详情事件
  handleRefreshSampleDetail = () => {
    console.log('收到刷新样本详情事件，重新加载数据');
    const { sampleCode } = this.state;
    if (sampleCode) {
      this.loadSampleData(sampleCode);
    }
  }

  // 刷新页面数据（用于从其他小程序返回时）
  refreshPageData = async (id) => {
    try {
      console.log('刷新页面数据，ID:', id);

      // 不显示加载提示，静默刷新
      const res = await Api.getHealthOrderById({ id });

      if (res && res.code === 0 && res.data) {
        const data = res.data;
        console.log('刷新获取的数据:', data);

        // 更新状态，包括知情同意书签署状态、问卷填写状态、样本处理状态等
        this.setState({
          sampleCode: data.sampleCode || id,
          sjzName: data.sjzName || '',
          sjzPhone: data.sjzPhone || '',
          questionnaireStatus: data.questionnaireStatus || '0',
          signStatus: data.signStatus || '0',
          reportStatus: data.reportStatus || '0',
          orderDetail: data,
          sampleData: data, // 添加 sampleData 字段
          // 基础信息
          sjzAge: data.sjzAge || '',
          sjzSex: data.sjzSex || '',
          sjzIdNum: data.sjzIdNum || '',
          sjzHeight: data.sjzHeight || '',
          sjzWeight: data.sjzWeight || '',
          // 监护人信息
          jhrName: data.jhrName || '',
          jhrIdNum: data.jhrIdNum || '',
          jhrPhone: data.jhrPhone || '',
          // 其他状态
          infoStatus: data.infoStatus || '0',
          collectionStatus: data.collectionStatus || '0',
          // 知情同意书相关
          signFileList: data.signFileList || [],
          signPdfUrl: data.signPdfUrl || '',
          // 问卷相关
          questionUserId: data.questionUserId || '',
          titleList: data.titleList || [],
          questionType: data.questionType || '1',
          // 样本处理进度
          sampleTime: data.sampleTime || '',
          receiveTime: data.receiveTime || '',
          analysisTime: data.analysisTime || '',
          reportTime: data.reportTime || '',
          // 更新时间戳
          timestamp: Date.now()
        });

        console.log('页面数据刷新成功');
      } else {
        console.warn('刷新数据失败:', res);
      }
    } catch (error) {
      console.error('刷新页面数据失败:', error);
      // 静默失败，不显示错误提示
    }
  }

  config = {
    navigationBarTitleText: '报告出具',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black'
  };

  loadSampleData = async (id) => {
    try {
      Taro.showLoading({ title: '加载中...' });

      // 使用与用户端一致的API接口
      const res = await Api.getHealthOrderById({ id });

      // 标记数据已经加载过
      this.hasLoadedData = true;

      if (res && res.code === 0 && res.data) {
        const orderData = res.data;

        console.log('订单详情数据:', orderData);

        // 处理知情同意书文件列表
        let signFileList = [];
        let signPdfUrl = '';

        if (orderData.signFiles) {
          try {
            signFileList = typeof orderData.signFiles === 'string'
              ? JSON.parse(orderData.signFiles)
              : orderData.signFiles;

            // 格式化日期
            signFileList.forEach(file => {
              if (file.signDate) {
                try {
                  const date = new Date(file.signDate);
                  file.formattedDate = this.formatDateTime(date);
                } catch (e) {
                  console.error('格式化日期失败:', e);
                  file.formattedDate = file.signDate;
                }
              }
            });
          } catch (e) {
            console.error('解析签署文件列表失败:', e);
            signFileList = [];
          }
        } else if (orderData.signFileList && orderData.signFileList.length > 0) {
          // 兼容旧的signFileList格式
          signFileList = orderData.signFileList;

          // 如果有signPdfUrl，也记录下来
          if (orderData.signPdfUrl) {
            signPdfUrl = orderData.signPdfUrl;
          }
        } else {
          signFileList = [];
          // 如果有signPdfUrl，也记录下来
          if (orderData.signPdfUrl) {
            signPdfUrl = orderData.signPdfUrl;
          }
        }

        // 获取PDF URL
        if (signFileList && signFileList.length > 0) {
          signPdfUrl = signFileList[0].signPdfUrl || '';
        }

        // 设置条形码内容
        let sampleCodeDisplay = '';
        if (orderData.reportStatus === '0') {
          sampleCodeDisplay = orderData.id || id;
        } else {
          sampleCodeDisplay = orderData.sampleNumber || orderData.id || id;
        }

        this.setState({
          // 基础信息
          sampleCode: sampleCodeDisplay,
          sjzName: orderData.sjzName || '',
          sjzPhone: orderData.sjzPhone || '',
          sjzAge: orderData.sjzAge || '',
          sjzSex: orderData.sjzSex || '',
          sjzIdNum: orderData.sjzIdNum || '',
          sjzHeight: orderData.sjzHeight || '',
          sjzWeight: orderData.sjzWeight || '',
          // 监护人信息（儿童版）
          jhrName: orderData.jhrName || '',
          jhrIdNum: orderData.jhrIdNum || '',
          jhrPhone: orderData.jhrPhone || '',
          // 状态信息
          questionnaireStatus: orderData.questionnaireStatus || '0',
          signStatus: orderData.signStatus || '0',
          reportStatus: orderData.reportStatus || '0',
          infoStatus: orderData.infoStatus || '0',
          collectionStatus: orderData.collectionStatus || '0',
          // 时间戳
          timestamp: Date.now(),
          // 订单详情
          userInfo: orderData,
          orderDetail: orderData,
          sampleData: orderData, // 添加 sampleData 字段
          // 知情同意书相关
          signFileList: signFileList,
          signPdfUrl: signPdfUrl,
          // 问卷相关数据
          questionUserId: orderData.questionUserId || '',
          questionType: orderData.questionType || '1',
          selectedType: orderData.questionType === '2' ? 'child' : 'adult',
          // 样本处理进度时间
          sampleTime: orderData.sampleTime || '',
          receiveTime: orderData.receiveTime || '',
          analysisTime: orderData.analysisTime || '',
          reportTime: orderData.reportTime || '',
          // 其他字段
          appId: orderData.appId || '',
          pagePath: orderData.pagePath || '',
          orderCode: orderData.orderCode || '',
          sampleId: orderData.sampleId || '',
          userId: orderData.userId || '',
          hisId: orderData.hisId || ''
        });

        console.log('问卷状态:', orderData.questionnaireStatus, '签署状态:', orderData.signStatus);
        console.log('进度状态信息:', {
          reportStatus: orderData.reportStatus,
          sampleTime: orderData.sampleTime,
          receiveTime: orderData.receiveTime,
          reportTime: orderData.reportTime
        });

      } else {
        Taro.showToast({
          title: res.msg || '获取样本详情失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载样本数据失败:', error);
      Taro.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      Taro.hideLoading();
    }
  }

  // 查看报告
  viewReport = () => {
    console.log('点击查看报告按钮');
    Taro.showToast({
      title: '查看报告PDF功能待实现',
      icon: 'none'
    });
  }

  // 扫描样本管条码
  scanSampleTube = () => {
    console.log('点击扫描样本管条码');
    // 这里可以调用Taro的扫码API
    Taro.scanCode({
      success: (res) => {
        console.log('扫码结果:', res.result);
        this.setState({
          sampleTubeCode: res.result
        });
        Taro.showToast({
          title: '扫码成功',
          icon: 'success'
        });
      },
      fail: (error) => {
        console.error('扫码失败:', error);
        Taro.showToast({
          title: '扫码失败，请重试',
          icon: 'none'
        });
      }
    });
  }

  // 手动输入样本管条码（实时输入，不触发显示）
  onSampleTubeCodeInput = (e) => {
    const value = e.detail.value;
    this.setState({
      tempInputValue: value
    });
  }

  // 手动输入样本管条码（失去焦点时触发显示）
  onSampleTubeCodeBlur = (e) => {
    const value = e.detail.value;
    if (value && value.trim() !== '') {
      this.setState({
        sampleTubeCode: value.trim(),
        tempInputValue: ''
      });
    }
  }

  // 清除样本管条码，重新扫描
  clearSampleTubeCode = () => {
    this.setState({
      sampleTubeCode: '',
      tempInputValue: ''
    });
  }

  // 绑定样本管
  bindSampleTube = async () => {
    const { sampleTubeCode, tempInputValue, orderDetail } = this.state;

    // 优先使用已确认的条码，其次使用临时输入值
    let finalCode = sampleTubeCode;
    if (!finalCode && tempInputValue && tempInputValue.trim() !== '') {
      finalCode = tempInputValue.trim();
      // 将临时输入值设置为正式条码
      this.setState({
        sampleTubeCode: finalCode,
        tempInputValue: ''
      });
    }

    if (!finalCode || finalCode.trim() === '') {
      Taro.showToast({
        title: '请先扫描或输入样本管条码',
        icon: 'none'
      });
      return;
    }

    // 获取订单ID
    const orderId = (orderDetail && orderDetail.id) || this.$router.params.id || this.$router.params.sampleCode;
    if (!orderId) {
      Taro.showToast({
        title: '订单信息不完整，无法绑定',
        icon: 'none'
      });
      return;
    }

    console.log('绑定样本管条码:', finalCode, '订单ID:', orderId);

    Taro.showLoading({ title: '绑定中...' });

    try {
      // 调用绑定样本编号API
      const res = await Api.bindSampleNumber(orderId, finalCode);

      Taro.hideLoading();

      if (res && res.code === 0) {
        Taro.showToast({
          title: '绑定成功',
          icon: 'success'
        });

        // 绑定成功后重新加载数据
        setTimeout(() => {
          this.loadSampleData(orderId);
        }, 1500);
      } else {
        Taro.showToast({
          title: (res && res.msg) || '绑定失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('绑定样本管失败:', error);
      Taro.hideLoading();
      Taro.showToast({
        title: '绑定失败，请重试',
        icon: 'none'
      });
    }
  }



  // 打开基础信息弹窗
  openBasicInfoModal = () => {
    this.setState({ showBasicInfoModal: true });
  }

  // 关闭基础信息弹窗
  closeBasicInfoModal = () => {
    this.setState({ showBasicInfoModal: false });
  }



  // 处理知情同意书点击
  handleConsentClick = () => {
    const { signStatus, signFileList, signPdfUrl } = this.state;

    console.log('处理知情同意书点击, 签署状态:', signStatus);

    // 检查知情同意书签署状态
    if (signStatus === '1') {
      // 已签署，打开知情同意书弹窗查看
      if (signFileList && signFileList.length > 0) {
        this.openConsentModal();
      } else if (signPdfUrl) {
        // 直接查看PDF
        this.downloadAndOpenPdf(signPdfUrl);
      } else {
        Taro.showToast({
          title: '未找到知情同意书文件',
          icon: 'none'
        });
      }
    } else {
      // 未签署，跳转到签署小程序
      console.log('知情同意书未签署，跳转到签署小程序');
      this.navigateToConsentMiniProgram();
    }
  }

  // 跳转到知情同意书小程序
  navigateToConsentMiniProgram = async () => {
    try {
      Taro.showLoading({ title: '处理中...', mask: true });

      const { sampleCode } = this.state;
      const { sampleCode: paramSampleCode } = this.$router.params;
      const sampleId = paramSampleCode || sampleCode;

      // 先调用getHealthOrderById检查是否已经有appId和pagePath
      let res;
      try {
        const orderRes = await Api.getHealthOrderById({ id: sampleId });
        console.log('getHealthOrderById接口返回:', orderRes);
        
        // 如果接口调用成功并且返回了appId和pagePath，直接使用
        if (orderRes && orderRes.code === 0 && orderRes.data) {
          if (orderRes.data.appId && orderRes.data.pagePath) {
            console.log('已获取到appId和pagePath，无需调用signSampleFile');
            res = orderRes;
          } else {
            // 没有appId或pagePath，调用signSampleFile接口
            console.log('未获取到appId或pagePath，调用signSampleFile接口');
            res = await Api.signSampleFile({ id: sampleId });
          }
        } else {
          // 接口调用失败，使用signSampleFile作为备选
          console.log('getHealthOrderById调用失败，使用signSampleFile作为备选');
          res = await Api.signSampleFile({ id: sampleId });
        }
      } catch (orderError) {
        console.error('调用getHealthOrderById接口失败:', orderError);
        console.log('尝试调用signSampleFile接口');
        res = await Api.signSampleFile({ id: sampleId });
      }

      Taro.hideLoading();

      if (res && res.code === 0 && res.data) {
        // API调用成功，检查是否需要跳转到小程序
        if (res.data.appId && res.data.pagePath) {
          console.log('跳转到知情同意书小程序:', res.data);

          // 准备额外数据
          const extraData = {
            id: sampleId
          };
          
          // 如果API返回了其他需要传递的数据，也添加到extraData中
          if (res.data.orderCode) extraData.orderCode = res.data.orderCode;
          if (res.data.fileCode) extraData.fileCode = res.data.fileCode;

          // 跳转到知情同意书小程序
          Taro.navigateToMiniProgram({
            appId: res.data.appId,
            path: res.data.pagePath,
            extraData: extraData,
            success: () => {
              console.log('跳转到知情同意书小程序成功');
            },
            fail: (error) => {
              console.error('跳转到知情同意书小程序失败:', error);
              Taro.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        } else if (res.data.signatureUrl) {
          // 直接签署成功
          this.setState({
            signStatus: '1',
            signPdfUrl: res.data.signatureUrl
          });

          Taro.showToast({
            title: '签署成功',
            icon: 'success'
          });
          
          // 刷新页面数据
          this.loadSampleData(sampleId);
        } else {
          Taro.showToast({
            title: '签署配置异常',
            icon: 'none'
          });
        }
      } else {
        Taro.showToast({
          title: (res && res.msg) || '签署失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('跳转到知情同意书失败:', error);
      Taro.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
      Taro.hideLoading();
    }
  }

  // 查看知情同意书
  viewConsentForm = () => {
    const { signFileList, signPdfUrl } = this.state;

    console.log('查看知情同意书');

    if (signFileList && signFileList.length > 0) {
      // 有多份知情同意书，弹出选择列表
      const items = signFileList.map(file => file.fileName || '知情同意书');

      Taro.showActionSheet({
        itemList: items,
        success: (res) => {
          if (res.tapIndex >= 0) {
            // 选择了某个知情同意书
            const selectedFile = signFileList[res.tapIndex];
            this.downloadAndOpenPdf(selectedFile.signPdfUrl);
          }
        }
      });
    } else if (signPdfUrl) {
      // 只有一份知情同意书
      this.downloadAndOpenPdf(signPdfUrl);
    } else {
      Taro.showToast({
        title: '未找到知情同意书',
        icon: 'none'
      });
    }
  }

  // 下载并打开PDF
  downloadAndOpenPdf = (pdfUrl) => {
    if (!pdfUrl) {
      Taro.showToast({
        title: '文件地址无效',
        icon: 'none'
      });
      return;
    }

    console.log('下载并打开PDF:', pdfUrl);

    Taro.showLoading({ title: '加载中...', mask: true });

    Taro.downloadFile({
      url: pdfUrl,
      success: (res) => {
        Taro.hideLoading();
        if (res.statusCode === 200) {
          Taro.openDocument({
            filePath: res.tempFilePath,
            success: () => {
              console.log('打开文档成功');
            },
            fail: (error) => {
              console.error('打开文档失败:', error);
              Taro.showToast({
                title: '打开文档失败',
                icon: 'none'
              });
            }
          });
        } else {
          Taro.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        Taro.hideLoading();
        console.error('下载文件失败:', error);
        Taro.showToast({
          title: '下载失败',
          icon: 'none'
        });
      }
    });
  }

  // 打开知情同意书弹窗
  openConsentModal = () => {
    const { signFileList } = this.state;

    // 处理时间戳转换为日期字符串
    if (signFileList && signFileList.length > 0) {
      signFileList.forEach(file => {
        if (file.signDate) {
          // 判断是否为时间戳格式（数字或数字字符串）
          if (typeof file.signDate === 'number' || (typeof file.signDate === 'string' && /^\d+$/.test(file.signDate))) {
            file.signDate = this.formatTimestamp(parseInt(file.signDate));
          }
        }
      });
    }

    this.setState({ showConsentModal: true });
  }

  // 关闭知情同意书弹窗
  closeConsentModal = () => {
    this.setState({ showConsentModal: false });
  }

  // 格式化时间戳
  formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  // 格式化日期时间
  formatDateTime = (date) => {
    if (!date) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // 根据问卷类型获取对应的问卷ID
  getSurveyIdByType = async (questionType) => {
    try {
      // 根据问卷类型确定要请求的key
      const key = questionType === '2' ? 'health_sample_que_child_id' : 'health_sample_que_adult_id';

      console.log('获取问卷ID，问卷类型:', questionType, 'key:', key);

      // 调用getProfileByKey接口获取问卷ID
      const res = await Api.getProfileByKey({
        hisId: '242', // 医院ID
        platformId: '242', // 平台ID
        key: key
      });

      console.log('getProfileByKey接口返回:', res);

      if (res && res.code === 0 && res.data && res.data.profileValue) {
        console.log('成功获取问卷ID:', res.data.profileValue);
        return res.data.profileValue;
      } else {
        console.error('获取问卷ID失败:', res);
        return null;
      }
    } catch (error) {
      console.error('调用getProfileByKey接口失败:', error);
      return null;
    }
  }

  // 处理问卷详情点击
  handleQuestionnaireClick = async () => {
    const { questionnaireStatus, questionType } = this.state;

    console.log('点击问卷详情，状态:', questionnaireStatus, '问卷类型:', questionType);

    try {
      // 根据问卷类型获取对应的问卷ID
      const surveyId = await this.getSurveyIdByType(questionType);

      console.log('获取到的问卷ID:', surveyId);

      if (!surveyId) {
        Taro.showToast({
          title: '获取问卷信息失败',
          icon: 'none'
        });
        return;
      }

      console.log('准备跳转，问卷状态:', questionnaireStatus, '问卷ID:', surveyId);

      if (questionnaireStatus === '1') {
        // 问卷已填写，查看问卷详情
        this.viewSurveyDetail(surveyId);
      } else {
        // 问卷未填写，跳转到问卷填写页面
        this.goToSurveyDetail(surveyId, this.state.questionUserId);
      }
    } catch (error) {
      console.error('处理问卷点击失败:', error);
      Taro.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  }

  // 查看问卷详情
  viewSurveyDetail = (surveyId) => {
    const { sampleCode, questionType, sampleData, questionUserId } = this.state;

    // 使用 state 中的 questionUserId，如果没有则从 sampleData 中获取
    const finalQuestionUserId = questionUserId || (sampleData && sampleData.questionnaireUrl) || '';

    console.log('查看问卷详情，问卷ID:', surveyId, '用户ID:', finalQuestionUserId, 'sampleData:', sampleData);

    if (!surveyId) {
      Taro.showToast({
        title: '问卷信息不完整',
        icon: 'none'
      });
      return;
    }

    // 跳转到问卷查看页面
    this.goToQuestionnaireView(surveyId, finalQuestionUserId, sampleCode, questionType);
  }

  // 跳转到问卷查看页面
  goToQuestionnaireView = (surveyId, questionUserId, sampleCode, questionType) => {
    const { id } = this.$router.params;
    const orderId = id || sampleCode; // 获取健康样本订单ID

    let url = `/pages/sample/questionnaire-view/index?id=${surveyId}`;

    // 添加用户ID参数
    if (questionUserId) {
      url += `&questionUserId=${questionUserId}`;
    }

    // 添加orderId参数（参考用户端实现）
    if (orderId) {
      url += `&orderId=${orderId}`;
    }

    // 添加样本编码参数
    if (sampleCode) {
      url += `&sampleCode=${sampleCode}`;
    }

    // 添加问卷类型参数
    if (questionType) {
      url += `&questionType=${questionType}`;
    }

    console.log('跳转到问卷查看页面，URL:', url);

    // 导航到问卷查看页面
    Taro.navigateTo({
      url: url,
      success: () => {
        console.log('跳转到问卷查看页面成功');
      },
      fail: (error) => {
        console.error('跳转到问卷查看页面失败:', error);
        Taro.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  }

  // 跳转到问卷详情页面
  goToSurveyDetail = (surveyId, questionUserId) => {
    const { sampleCode, id } = this.$router.params;
    const { questionType, sampleData } = this.state;

    // 获取健康样本订单ID，优先使用路由参数中的id，其次使用sampleCode
    const healthSampleOrderId = id || sampleCode;

    // 从 sampleData 中获取 questionnaireUrl 作为 questionUserId
    const questionnaireUrl = (sampleData && sampleData.questionnaireUrl) || '';

    console.log('goToSurveyDetail 参数:', { surveyId, questionUserId, sampleCode, questionType, healthSampleOrderId, questionnaireUrl });

    // 构建URL，添加必要参数
    let url = `/pages/sample/questionnaire-detail/index?id=${surveyId}&readOnly=true`;

    // 添加record=1参数，表示需要回显问卷记录
    url += '&record=1';

    // 添加样本编码参数
    if (sampleCode) {
      url += `&sampleCode=${sampleCode}`;
    }

    // 添加健康样本订单ID参数（这是关键的修改）
    if (healthSampleOrderId) {
      url += `&healthSampleOrderId=${healthSampleOrderId}`;
    }

    // 添加问卷用户ID参数，优先使用 questionnaireUrl
    if (questionnaireUrl) {
      url += `&questionnaireUrl=${questionnaireUrl}`;
    } else if (questionUserId) {
      url += `&questionUserId=${questionUserId}`;
    }

    // 添加问卷类型参数
    if (questionType) {
      url += `&questionType=${questionType}`;
    }

    console.log('跳转到问卷详情页面，URL:', url);

    // 导航到问卷详情页面
    Taro.navigateTo({
      url: url,
      success: () => {
        console.log('跳转到问卷详情页面成功');
      },
      fail: (error) => {
        console.error('跳转到问卷详情页面失败:', error);
        Taro.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  }

  // 获取问卷详情
  getSurveyDetail = async (id) => {
    try {
      // 检查API是否存在
      if (!Api || !Api.getSurveyDetail) {
        console.error('API或getSurveyDetail方法不存在');
        return;
      }

      // 如果没有传入id，根据问卷类型动态获取
      if (!id) {
        const { questionType } = this.state;
        id = await this.getSurveyIdByType(questionType);

        if (!id) {
          Taro.showToast({
            title: '获取问卷ID失败',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }

      // 确保传递正确的id参数
      const res = await Api.getSurveyDetail({ id });

      console.log('问卷详情接口返回:', res);

      if (res && res.code === 0 && res.data) {
        // 处理问卷数据
        this.setState({
          titleList: res.data.titleList || [],
          surveyId: id
        });

        console.log('问卷详情加载成功，题目数量:', res.data.titleList ? res.data.titleList.length : 0);
      } else {
        Taro.showToast({
          title: res.msg || '获取问卷详情失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('获取问卷详情失败', error);
      Taro.showToast({
        title: '获取问卷详情失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  }

  // 获取样本处理进度状态文本
  getProcessStatusText = (step) => {
    const { reportStatus } = this.state;

    switch (step) {
      case 1: // 样本采集
        return reportStatus === '0' ? '待进行' : '已完成';
      case 2: // 实验室收取样本
        return (reportStatus === '2' || reportStatus === '3') ? '已完成' : '待进行';
      case 3: // 实验分析
        return reportStatus === '3' ? '已完成' : '待进行';
      case 4: // 报告出具
        return reportStatus === '3' ? '已完成' : '待进行';
      default:
        return '待进行';
    }
  }

  // 获取样本处理进度状态样式类
  getProcessStatusClass = (step) => {
    const { reportStatus } = this.state;

    switch (step) {
      case 1: // 样本采集
        return reportStatus === '0' ? 'pending' : 'completed';
      case 2: // 实验室收取样本
        return (reportStatus === '2' || reportStatus === '3') ? 'completed' : 'pending';
      case 3: // 实验分析
        return reportStatus === '3' ? 'completed' : 'pending';
      case 4: // 报告出具
        return reportStatus === '3' ? 'completed' : 'pending';
      default:
        return 'pending';
    }
  }

  // 获取样本处理进度时间
  getProcessTime = (step) => {
    const { reportStatus, sampleTime, receiveTime, analysisTime, reportTime } = this.state;

    switch (step) {
      case 1: // 样本采集
        return reportStatus !== '0' ? sampleTime : '';
      case 2: // 实验室收取样本
        return (reportStatus === '2' || reportStatus === '3') ? receiveTime : '';
      case 3: // 实验分析
        return reportStatus === '3' ? (analysisTime || reportTime) : '';
      case 4: // 报告出具
        return reportStatus === '3' ? reportTime : '';
      default:
        return '';
    }
  }

  render() {
    const {
      sampleCode,
      sjzName,
      sjzPhone,
      sjzAge,
      sjzSex,
      sjzIdNum,
      sjzHeight,
      sjzWeight,
      jhrName,
      jhrIdNum,
      jhrPhone,
      questionnaireStatus,
      signStatus,
      reportStatus,
      timestamp,
      selectedType,
      questionType,
      showBasicInfoModal,
      signFileList,
      showConsentModal,
      sampleTubeCode
    } = this.state;

    return (
      <View className={s.container}>
        {/* 样本编号卡片 - 根据reportStatus状态显示不同内容 */}
        <View className={s.sampleCard}>
          {reportStatus === '0' ? (
            /* 样本采集阶段 - 显示扫描输入界面 */
            <View>
              <View className={s.cardTitle}>样本管条码</View>
              <View className={s.scanInputArea}>
                {sampleTubeCode ? (
                  /* 已输入条码，显示输入的内容 */
                  <View className={s.inputDisplayArea}>
                    <View className={s.inputDisplayContainer}>
                      <Text className={s.inputDisplayText}>样本管条码：{sampleTubeCode}</Text>
                    </View>
                    <View className={s.clearButton} onClick={this.clearSampleTubeCode}>
                      <Text className={s.clearText}>清除重新输入</Text>
                    </View>
                  </View>
                ) : (
                  /* 未输入条码，显示扫描界面 */
                  <View className={s.scanArea}>
                    <View className={s.scanMainArea}>
                      <View className={s.scanIcon} onClick={this.scanSampleTube}>
                        <View className={s.scanIconBg}>
                          <View className={s.scanIconInner}>
                            <View className={s.cameraIcon}></View>
                          </View>
                        </View>
                        <Text className={s.scanText}>点击扫描</Text>
                      </View>
                    </View>

                    <View className={s.dividerSection}>
                      <View className={s.dividerLine}></View>
                      <Text className={s.dividerText}>或</Text>
                      <View className={s.dividerLine}></View>
                    </View>

                    <View className={s.inputSection}>
                      <Input
                        className={s.codeInput}
                        placeholder="手动输入样本管条码"
                        value={this.state.tempInputValue}
                        onInput={this.onSampleTubeCodeInput}
                        onBlur={this.onSampleTubeCodeBlur}
                        confirmType="done"
                        adjustPosition={true}
                      />
                    </View>
                  </View>
                )}
              </View>
              <View className={s.scanTip}>请扫描或输入样本管条码</View>
            </View>
          ) : (
            /* 其他阶段 - 显示原有的样本条码 */
            <View>
              <View className={s.cardTitle}>样本条码</View>
              <View className={s.barcodeArea}>
                <Image
                  className={s.barcodeImage}
                  mode="widthFix"
                  src={`https://wechatdev.jiahuiyiyuan.com/barcode?msg=${sampleCode}&type=code128&mw=1.2&hrp=none&t=${timestamp}`}
                />
              </View>
              <View className={s.sampleInfoText}>{sjzName} {sjzPhone}</View>
              <View className={s.scanTip}>请在采样时出示此条码</View>
            </View>
          )}
        </View>

        {/* 功能按钮卡片 */}
        <View className={s.functionCard}>
          <View className={s.functionButtons}>
            <View className={s.functionBtn} onClick={this.openBasicInfoModal}>
              <View className={`${s.functionIcon} ${s.basicInfoIcon}`}></View>
              <View className={s.functionText}>基础信息</View>
            </View>

            <View className={s.functionBtn} onClick={this.handleQuestionnaireClick}>
              <View className={`${s.functionIcon} ${s.surveyIcon}`}></View>
              <View className={s.functionText}>问卷详情</View>
              {questionnaireStatus !== '1' && (
                <View className={s.statusBadge}>未填写</View>
              )}
            </View>

            <View className={s.functionBtn} onClick={this.handleConsentClick}>
              <View className={`${s.functionIcon} ${s.consentIcon}`}></View>
              <View className={s.functionText}>知情同意书</View>
              {signStatus !== '1' && (
                <View className={s.statusBadge}>未签署</View>
              )}
            </View>
          </View>
        </View>

        {/* 进度流程 */}
        <View className={s.processCard}>
          <View className={s.processItem}>
            <View className={`${s.processNumber} ${s[this.getProcessStatusClass(1)]}`}>
              <Text>1</Text>
            </View>
            <View className={s.processContent}>
              <View className={s.processTitle}>
                <Text>样本采集</Text>
                <Text className={`${s.processStatus} ${s[this.getProcessStatusClass(1)]}`}>
                  {this.getProcessStatusText(1)}
                </Text>
              </View>
              {this.getProcessTime(1) && (
                <View className={s.processTime}>
                  <Text>{this.getProcessTime(1)}</Text>
                </View>
              )}
            </View>
          </View>

          <View className={s.processItem}>
            <View className={`${s.processNumber} ${s[this.getProcessStatusClass(2)]}`}>
              <Text>2</Text>
            </View>
            <View className={s.processContent}>
              <View className={s.processTitle}>
                <Text>实验室收取样本</Text>
                <Text className={`${s.processStatus} ${s[this.getProcessStatusClass(2)]}`}>
                  {this.getProcessStatusText(2)}
                </Text>
              </View>
              {this.getProcessTime(2) && (
                <View className={s.processTime}>
                  <Text>{this.getProcessTime(2)}</Text>
                </View>
              )}
            </View>
          </View>

          <View className={s.processItem}>
            <View className={`${s.processNumber} ${s[this.getProcessStatusClass(3)]}`}>
              <Text>3</Text>
            </View>
            <View className={s.processContent}>
              <View className={s.processTitle}>
                <Text>实验分析</Text>
                <Text className={`${s.processStatus} ${s[this.getProcessStatusClass(3)]}`}>
                  {this.getProcessStatusText(3)}
                </Text>
              </View>
              {this.getProcessTime(3) && (
                <View className={s.processTime}>
                  <Text>{this.getProcessTime(3)}</Text>
                </View>
              )}
            </View>
          </View>

          <View className={s.processItem}>
            <View className={`${s.processNumber} ${s[this.getProcessStatusClass(4)]}`}>
              <Text>4</Text>
            </View>
            <View className={s.processContent}>
              <View className={s.processTitle}>
                <Text>报告出具</Text>
                <Text className={`${s.processStatus} ${s[this.getProcessStatusClass(4)]}`}>
                  {this.getProcessStatusText(4)}
                </Text>
              </View>
              {this.getProcessTime(4) && (
                <View className={s.processTime}>
                  <Text>{this.getProcessTime(4)}</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* 动态按钮：根据reportStatus状态显示不同按钮 */}
        {reportStatus === '3' ? (
          /* 报告已出具，显示查看报告按钮 */
          <View className={s.actionButton} onClick={this.viewReport}>
            查看报告
          </View>
        ) : reportStatus === '0' ? (
          /* 样本采集阶段，显示绑定样本管按钮 */
          <View className={s.actionButton} onClick={this.bindSampleTube}>
            绑定此样本管
          </View>
        ) : null}



        {/* 基础信息弹窗 */}
        {showBasicInfoModal && (
          <View className={s.modalOverlay} onClick={this.closeBasicInfoModal}>
            <View className={s.modalContainer} onClick={(e) => e.stopPropagation()}>
              <View className={s.modalHeader}>
                <Text className={s.modalTitle}>基础信息</Text>
                <Text className={s.modalClose} onClick={this.closeBasicInfoModal}>×</Text>
              </View>
              <View className={s.modalContent}>
                {sjzName && (
                  <View className={s.infoRow}>
                    <Text className={s.infoLabel}>姓名：</Text>
                    <Text className={s.infoValue}>{sjzName}</Text>
                  </View>
                )}
                {sjzSex && (sjzSex === '1' || sjzSex === '2') && (
                  <View className={s.infoRow}>
                    <Text className={s.infoLabel}>性别：</Text>
                    <Text className={s.infoValue}>
                      {sjzSex === '1' ? '男' : '女'}
                    </Text>
                  </View>
                )}
                {sjzAge && (
                  <View className={s.infoRow}>
                    <Text className={s.infoLabel}>年龄：</Text>
                    <Text className={s.infoValue}>{sjzAge}</Text>
                  </View>
                )}
                {sjzIdNum && (
                  <View className={s.infoRow}>
                    <Text className={s.infoLabel}>证件号码：</Text>
                    <Text className={s.infoValue}>{sjzIdNum}</Text>
                  </View>
                )}
                {sjzPhone && (
                  <View className={s.infoRow}>
                    <Text className={s.infoLabel}>联系电话：</Text>
                    <Text className={s.infoValue}>{sjzPhone}</Text>
                  </View>
                )}
                {sjzHeight && (
                  <View className={s.infoRow}>
                    <Text className={s.infoLabel}>身高：</Text>
                    <Text className={s.infoValue}>{sjzHeight} cm</Text>
                  </View>
                )}
                {sjzWeight && (
                  <View className={s.infoRow}>
                    <Text className={s.infoLabel}>体重：</Text>
                    <Text className={s.infoValue}>{sjzWeight} kg</Text>
                  </View>
                )}
                {/* 儿童版监护人信息 */}
                {(questionType === '2' || selectedType === 'child') && (
                  <View>
                    {jhrName && (
                      <View className={s.infoRow}>
                        <Text className={s.infoLabel}>监护人姓名：</Text>
                        <Text className={s.infoValue}>{jhrName}</Text>
                      </View>
                    )}
                    {jhrIdNum && (
                      <View className={s.infoRow}>
                        <Text className={s.infoLabel}>监护人证件号：</Text>
                        <Text className={s.infoValue}>{jhrIdNum}</Text>
                      </View>
                    )}
                    {jhrPhone && (
                      <View className={s.infoRow}>
                        <Text className={s.infoLabel}>监护人电话：</Text>
                        <Text className={s.infoValue}>{jhrPhone}</Text>
                      </View>
                    )}
                  </View>
                )}
              </View>
            </View>
          </View>
        )}

        {/* 知情同意书弹窗 */}
        {showConsentModal && (
          <View className={s.modalOverlay} onClick={this.closeConsentModal}>
            <View className={s.modalContent} onClick={(e) => e.stopPropagation()}>
              <View className={s.modalHeader}>
                <Text className={s.modalTitle}>知情同意书</Text>
                <View className={s.modalClose} onClick={this.closeConsentModal}>×</View>
              </View>

              <View className={s.modalBody}>
                {signFileList && signFileList.length > 0 ? (
                  <View className={s.fileList}>
                    {signFileList.map((file, index) => (
                      <View key={index} className={s.fileItem}>
                        <View className={s.fileInfo}>
                          <Text className={s.fileName}>{file.fileName || '知情同意书'}</Text>
                          {file.signDate && (
                            <Text className={s.signDate}>签署时间：{file.signDate}</Text>
                          )}
                        </View>
                        <View
                          className={s.viewButton}
                          onClick={() => this.downloadAndOpenPdf(file.signPdfUrl)}
                        >
                          查看
                        </View>
                      </View>
                    ))}
                  </View>
                ) : (
                  <View className={s.emptyState}>
                    <Text>暂无知情同意书</Text>
                  </View>
                )}
              </View>

              <View className={s.modalFooter}>
                <View className={s.modalButton} onClick={this.closeConsentModal}>
                  关闭
                </View>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  }
}
