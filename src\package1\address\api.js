import { post } from '@/utils/request';

//获取地址详情
export const getAddressDetail = (param) => post('/api/address/addressdetail', param);

//添加地址
export const addAddress = (param) => post('/api/address/addaddress', param);

//修改地址
export const modifyAddress = (param) => post('/api/address/modifyaddress', param);

//获取地址列表
export const getAddressList = param => post('/api/address/addresslist', param);

//删除地址
export const delAddress = param => post('/api/address/deladdress', param);

//设置默认地址
export const setDefaultAddress = param => post('/api/address/setdefaultaddress', param);