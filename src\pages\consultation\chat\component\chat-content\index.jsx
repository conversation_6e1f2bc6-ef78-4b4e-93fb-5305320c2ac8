import Taro, {Component} from '@tarojs/taro';
import {Image, RichText, View} from '@tarojs/components';
import s from './index.module.scss';

export default class Index extends Component {
  config = {
  };

  constructor(props) {
    super(props);

    this.state = {
    };
  }

  componentWillMount() {
  }

  componentDidMount() {
  }

  componentWillUnmount() {
  }

  componentDidShow() {
  }

  componentDidHide() {
  }

  previewImage(url) {
    Taro.previewImage({
      urls: [url] //需要预览的图片链接列表,
    });
  }

  goToReport() {
    const {consultationId} = this.props;
    Taro.navigateTo({
      url: `/pages/consultation-opinion/consultation-report/index?consultationId=${consultationId}`
    });
  }



  render() {
    const {
      time, direction, image, type, name, cid, logo, content
    } = this.props;
    const renderContent = () => {
      if (type === '1') {
        return (
          <View className={s.commContentBox}>
            <View className={s.commContent}>
              {
                content && <RichText className={s.textContent} nodes={content} />
              }
            </View>
          </View>
        );
      } else if (type === '3') {
        return (
          <View className={s.commImageBox}>
            <Image src={image} className={s.commImage} onClick={(event) => this.previewImage(image)} mode='aspectFit' />
          </View>
        );
      }
      else if (type === '5') {
        return (
          <View className={s.commContentBox}>
            <View className={s.commContent}>
              <Image src={image} className={s.expressionImage} onClick={(event) => this.previewImage(image)} mode='aspectFit' />
            </View>
          </View>
        );
      }
      else if (type === '6') {
        return (
          <View className={s.text}>
            <View className={s.contentBox}>
              <Image src='https://hlwyy.zxxyyy.cn/hospital/his-miniapp/p242/consult/video.png' className={s.videoImage} />
              <View className='f-1'>发起了视频问诊</View>
            </View>
          </View>
        );
      }
      else if (type === '7') {
        return (
          <View className={s.report} onClick={this.goToReport}>
            <View className={s.reportHeader}>
              会诊报告
            </View>
            <View className={s.reportContent}>
              {content}
            </View>
          </View>
        );
      }
    }
    return (
      <View className={s.chatMsg}>
        {time && <View className={s.date}>{time}</View>}
        <View className={`${s.msgContentBox} f-row ${direction == 'right' ? s.right + ' f-row-rvs ' : s.left}`} id={`id-${cid}`}>
          <Image className={s.commLogo} src={logo || `${$CDN_DOMAIN}/his-miniapp/p242/defaultHeadImg.png`} />

          <View className={direction == 'right' ? s.tl : s.left}>
            <View className={s.name} style={{fontSize: '9px', marginBottom: '5px', textAlign: direction == 'right' ? 'right' : 'left'}}>{name}</View>
            {renderContent()}
          </View>
        </View>
      </View>
    )
  }
}
