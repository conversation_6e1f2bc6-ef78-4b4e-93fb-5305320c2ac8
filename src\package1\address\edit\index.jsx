import Taro, { Component } from '@tarojs/taro'
import { View, Button, Text, Input, Switch, Picker, Block } from '@tarojs/components'
import arrowPng from '@/resources/images/arrow-right.png'
import s from './index.module.scss'
import * as API from '../api'

const phoneReg = /^1[3456789]\d{9}$/

export default class Index extends Component {

    constructor(props) {
        super(props)
        this.state = {
            detail: {
                userName: '',
                mobile: '',
                provinceName: '',
                cityName: '',
                areaName: '',
                provinceCode: '',
                cityCode: '',
                areaCode: '',
                addressDetail: '',
                address: '',
                isDefault: 0
            },
            userId: '',
            mode: ''
        }
    }

    componentWillMount() {
        const { id, mode } = this.$router.params;
        if (mode === 'choose') {
            this.setState({ mode: mode })
        }
        if (id > 0) {
            Taro.setNavigationBarTitle({ title: '编辑地址' })
            this.getDetail(id)
        } else {
            Taro.setNavigationBarTitle({ title: '新增地址' })
        }
        const { userId = '' } = JSON.parse(Taro.getStorageSync('userInfo') || '{}');
        this.setState({ userId });
    }

    config = {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTitleText: '编辑地址',
        navigationBarTextStyle: 'black'
    };

    /**
     * 获取地址详情
     * @param {*} id 地址主键id
     * @returns 
     */
    async getDetail(id) {
        const { code, data } = await API.getAddressDetail({ id })
        if (code !== 0) return
        this.setState({ detail: { ...data } })
    }

    onInputName = (e) => {
        this.setState(preState => ({
            detail: {
                ...preState.detail,
                userName: e.detail.value
            }
        }))
    }

    onInputMobile = (e) => {
        this.setState(preState => ({
            detail: {
                ...preState.detail,
                mobile: e.detail.value
            }
        }))
    }

    onInputDetailAddress = (e) => {
        this.setState(preState => ({
            detail: {
                ...preState.detail,
                addressDetail: e.detail.value
            }
        }))
    }

    /**
     * 省市区选择
     * @param {*} e 
     */
    onRegionChange = (e) => {
        this.setState(preState => ({
            detail: {
                ...preState.detail,
                provinceName: e.detail.value[0],
                cityName: e.detail.value[1],
                areaName: e.detail.value[2],
                provinceCode: e.detail.code[0],
                cityCode: e.detail.code[1],
                areaCode: e.detail.code[2]
            }
        }))
    }

    onChangeDefault = (e) => {
        this.setState(preState => ({
            detail: {
                ...preState.detail,
                isDefault: e.detail.value ? 1 : 0
            }
        }))
    }

    /**
     * 清空
     */
    clearText = () => {
        this.setState({
            detail:
            {
                userName: '',
                mobile: '',
                provinceName: '',
                cityName: '',
                areaName: '',
                provinceCode: '',
                cityCode: '',
                areaCode: '',
                addressDetail: '',
                isDefault: 0
            }
        })
    }

    save = async () => {
        const { detail, userId } = this.state
        const error = await this.validate()
        if (error) return this.showError(error)
        const fn = detail.id ? API.modifyAddress : API.addAddress
        const { code, msg } = await fn({
            ...detail,
            userId
        })
        if (code !== 0) return
        this.navigateBack(msg)
    }

    navigateBack = async (msg) => {
        const { detail, mode } = this.state
        //选择地址模式需要向页面传参
        if (mode === 'choose') {
            let pages = Taro.getCurrentPages(); // 获取当前的页面栈 
            let prevPage = pages[pages.length - 2]; // 获取上一页面
            //新增
            if (!detail.id) {
                //查询地址详情
                let id = msg
                const { code, data } = await API.getAddressDetail({ id })
                if (code !== 0) return
                prevPage.setData({
                    currentAddress: data
                });
            } else {
                prevPage.setData({
                    currentAddress: {
                        ...detail,
                        address: detail.provinceName + detail.cityName + detail.areaName + detail.addressDetail
                    }
                });
            }
        }
        Taro.navigateBack()
    }

    showError(title, duration = 1500) {
        Taro.showToast({ title, icon: 'none', duration });
    }

    async validate() {
        const { detail } = this.state
        if (!detail.userName) return Promise.resolve('请输入姓名')
        if (!phoneReg.test(detail.mobile)) return Promise.resolve('请输入正确的手机号')
        if (!detail.provinceName || !detail.cityName || !detail.areaName) return Promise.resolve('请选择省市区')
        if (!detail.addressDetail) return Promise.resolve('请输入详细地址')
        return Promise.resolve()
    }

    handleDelete = () => {
        Taro.showModal({
            title: '提示',
            content: '确认删除地址？',
            confirmColor: '#30A1A6',
            success: res => {
                if (res.confirm) {
                    this.deleteAddress()
                }
            }
        })
    }

    async deleteAddress() {
        const { code } = await API.delAddress({ id: this.$router.params.id })
        if (code !== 0) return
        Taro.showToast({ title: '删除成功', icon: 'none' })
        setTimeout(Taro.navigateBack, 1500)
    }

    chooseWxAddress = () => {
        let that = this
        Taro.chooseAddress({
            success(res) {
                that.setState({
                    detail: {
                        userName: res.userName,
                        mobile: res.telNumber,
                        provinceName: res.provinceName,
                        cityName: res.cityName,
                        areaName: res.countyName,
                        addressDetail: res.detailInfo
                    }
                })
            }
        })
    }

    /**
     * 渲染
     * @returns 
     */
    render() {
        const { detail } = this.state;
        return (
            <Block>
                <View className={s.notice}>根据国家法律法规要求，寄件人姓名须与您提供的实名信息一致</View>
                <View className={s.page}>
                    <View className={s.item}>
                        <View className={s.item_title}>联系人信息</View>
                        <View className={s.item_wxaddress} onClick={this.chooseWxAddress}>微信地址簿</View>
                    </View>
                    <View className={s.item}>
                        <View className={s.item_label}>姓名</View>
                        <Input className={s.item_input} placeholder='真实姓名' type='text' confirm-type='done' value={detail.userName} onInput={e => this.onInputName(e)}></Input>
                    </View>
                    <View className={s.item}>
                        <View className={s.item_label}>手机号</View>
                        <Input className={s.item_input} placeholder='手机号' type='number' maxLength={11} value={detail.mobile} onInput={e => this.onInputMobile(e)}></Input>
                    </View>
                    <View className={s.item}>
                        <View className={s.item_label}>省市区</View>
                        <Picker mode='region' className={s.item_input} onChange={e => this.onRegionChange(e)} value={detail.provinceName ? [detail.provinceName, detail.cityName, detail.areaName] : []}>
                            <Input placeholder='省市区' value={detail.provinceName + detail.cityName + detail.areaName} type='text' disabled></Input>
                        </Picker>
                        <Image src={arrowPng} className={s.item_arrow}></Image>
                    </View>
                    <View className={s.item}>
                        <View className={s.item_label}>详细地址</View>
                        <Input className={s.item_input} placeholder='详细地址（例如**街**号**）' type='text' confirm-type='done' value={detail.addressDetail} onInput={e => this.onInputDetailAddress(e)}></Input>
                    </View>
                    <View className={s.foot}>
                        <View>
                            <Switch color='#3F969D' className={s.foot_switch} checked={detail.isDefault === 1 ? true : false} onChange={e => this.onChangeDefault(e)}></Switch>
                            <Text className={s.foot_text}>默认寄件地址</Text>
                        </View>
                        <Text onClick={this.clearText}>清空</Text>
                    </View>
                </View>
                {
                    detail.id ? <View className={s.btn} onClick={this.handleDelete}>
                        <Text className={s.btn_del}>删除地址</Text>
                    </View> : null
                }
                <View className={s.bottom}>
                    <Button className={s.bottom_btn} onClick={this.save}>确定</Button>
                </View>
            </Block>
        )
    }
}