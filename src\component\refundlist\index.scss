.wgt-refundlist {
  background-color: #fff;
  padding: 30px;
}
.wgt-refundlist-tit {
  font-size: 34px;
  color: $color-title;
  font-weight: bold;
}
.wgt-refundlist-select{
  display: flex;
  align-items: center;
  font-size: 26px;
  padding-top: 30px;
  padding-bottom: 20px;
}
.wgt-refundlist-select-label{
  color: $color-title;
}
.wgt-refundlist-select-value{
  color: $color-text;
}
.wgt-refundlist-select-icon{
  font-size: 15px;
  color: $color-primary;
  margin-left: 10px;
}
.wgt-refundlist-box {
  position: relative;
  padding-top: 30px;
  padding-left: 30px;
  display: flex;
  flex-direction: column-reverse;
}
.wgt-refundlist-item {
  position: relative;
  font-size: 30px;
  color: $color-title;
  min-height: 72px;
  padding-bottom: 40px;
  z-index: 2;
}
.wgt-refundlist-item-icon {
  position: absolute;
  left: 0;
  top: 8px;
  width: 24px;
  height: 24px;
  background-color: $color-border;
  border-radius: 50%;
}
.wgt-refundlist-item-line {
  position: absolute;
  left: 10px;
  top: 0;
  bottom: -2px;
  transform: translateY(30px);
  width: 4px;
  background-color: $color-border;
}
.wgt-refundlist-item-title {
  padding-left: 60px;
}
.wgt-refundlist-item {
  &.active {
    z-index: 1;
    .wgt-refundlist-item-icon {
      background-color: $color-primary;
    }
    .wgt-refundlist-item-title{
      color: $color-primary;
    }
    &~.wgt-refundlist-item{
      .wgt-refundlist-item-icon, .wgt-refundlist-item-line {
        background-color: $color-primary;
      }
      
    }
  }
  &:first-child {
    min-height: 50px;
    padding-bottom: 0;
    .wgt-refundlist-item-line {
      display: none;
    }
  }
}