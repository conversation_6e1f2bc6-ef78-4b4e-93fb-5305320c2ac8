import Taro, { Component } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';
import Empty from '@/component/empty'
import s from './index.module.scss'
import * as API from '../../api'

const STATUE_MAP = {
  1: '进行中',
  2: '已结束',
  4: '未开始',
}

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      activityList: [],
    }
  }

  componentWillMount() {
    this.getActivityList()
  }

  getActivityList = async () => {
    const { code, data} = await API.getActivityList()
    if (code !== 0) return
    const activityList = (data || []).map(v => ({
      ...v,
      signBegin: v.signBegin ? v.signBegin.split(' ')[0] : '',
      signEnd: v.signEnd ? v.signEnd.split(' ')[0] : '',
    }))
    this.setState({ activityList })
  }

  toDetail(id) {
    const { isGuest = 0 } = this.props
    Taro.navigateTo({ url: `/pages/classroom/Child/Activity/detail?id=${id}&isGuest=${isGuest}` })
  }

  toSignRecods = () => {
    Taro.navigateTo({ url: '/pages/classroom/records/index' })
  }

  renderStatus(status) {
    return (<Text>{status}</Text>)
  }

  render() {
    const { activityList } = this.state
    const { isGuest = 0 } = this.props
    return (
      <View className={s.block}>

        <View className={[s.block_body, isGuest && s.is_guest]}>
          {
            activityList.map(item => (
              <View
                key={item.id}
                className={[s.block_item, 'f-row', 'f-c-stretch', 'f-m-between']}
                onClick={() => this.toDetail(item.id)}
              >
                <View className={['f-1', 'f-col', 'f-m-between']}>
                  <View>
                    <View className={s.block_item_title}>{item.title}</View>
                    { item.needSign ?
                      <View
                        className={[s.block_item_status, item.status === 1 && s.primary, item.status !== 1 && s.warn]}
                      >{STATUE_MAP[item.status]}</View>
                      : null
                    }
                  </View>
                  { item.needSign ? <View className={s.block_item_date_range}>
                    <Text>报名起止日期：</Text>
                    <Text className={s.block_item_time}>{item.signBegin}至</Text>
                    <Text className={s.block_item_time}>{item.signEnd}</Text>
                  </View> : null }
                  { !item.needSign && item.beginTime && item.endTime ? <View className={s.block_item_date_range}>
                    <Text>活动起止日期：</Text>
                    <Text className={s.block_item_time}>{item.beginTime}至</Text>
                    <Text className={s.block_item_time}>{item.endTime}</Text>
                  </View> : null }
                </View>
                <Image className={s.block_item_img} src={item.img} mode='aspectFill' />
              </View>
            ))
          }

          {
            (!activityList || !activityList.length) ? <Empty text='暂无培训活动' /> : null
          }
        </View>

        <View className={[s.block_footer]}>
          <Text className={s.block_footer_record} onClick={this.toSignRecods}>我的报名记录</Text>
        </View>
      </View>
    )
  }
}
