import Taro, { Component } from '@tarojs/taro';
import { View, Text, Image } from '@tarojs/components';

import './index.scss';

export default class Folding extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isExpand: true
    }
  }

  componentWillMount() { }

  componentDidMount() { }

  componentWillUnmount() { }

  componentDidShow() { }

  componentDidHide() { }

  toggleExpand = () => {
    const { isExpand } = this.state;
    this.setState({
      isExpand: !isExpand
    })
  }

  render() {
    const { children, title } = this.props;
    const { isExpand } = this.state;
    return (
      <View className='wgt-folding'>
        <View className='wgt-folding-tit' onClick={this.toggleExpand.bind(this)}>
          <Text name='title'>{title}</Text>
          <View className={`wgt-folding-expand ${ isExpand ? 'active' : ''}`}>
            <Image mode='widthFix' src={`${$CDN_DOMAIN}/icon-shouqi.png`}></Image>
          </View>
        </View>
        {
          isExpand &&
          <View className='wgt-folding-content'>
          {children}
          </View>
        }
      </View>
    )
  }
}

Folding.defaultProps = {
  title: '缴费详情',
}
