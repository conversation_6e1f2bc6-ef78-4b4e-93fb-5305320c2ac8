import { View, Image } from "@tarojs/components";
import Taro, { Component } from "@tarojs/taro";

import * as API from '../api'
import s from './index.module.scss'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      barCode: '',
      platform: '',
      productName: '',
      sonProductName: '',
      sampleTime: '',
      institutionName: '',
      submitHospitalName: '',
      submitDoctor: '',
      files: [],
    }
  }



  componentWillMount() {
    const { id } = this.$router.params
    this.getDetail(id)
  }

  async getDetail(id) {
    const { code, data } = await API.getSampleDetail({ id })
    if (code !== 0) return
    this.setState({
      barCode: data.sampleNumber,
      platform: data.platform,
      productName: data.productName,
      sonProductName: data.sonProductName,
      sampleTime: data.sampleTime,
      institutionName: data.institutionName,
      submitHospitalName: data.submitHospitalName,
      submitDoctor: data.submitDoctor,
      files: data.files ? data.files.split(',') : []
    })
    Taro.setNavigationBarTitle({ title: data.sampleNumber })
  }

  previewImage(current, urls) {
    Taro.previewImage({ current, urls })
  }


  render() {
    const { barCode, platform, productName, sonProductName, institutionName, submitHospitalName, submitDoctor, sampleTime, files } = this.state
    return (
      <View className={s.page}>
        <View className={s.block}>
          <View className={s.item}>
            <View className={s.item_title}>样本编号</View>
            <View className={s.item_value}>{barCode}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>平台</View>
            <View className={s.item_value}>{platform}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>产品线</View>
            <View className={s.item_value}>{productName}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>子产品线</View>
            <View className={s.item_value}>{sonProductName || '-'}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>采样时间</View>
            <View className={s.item_value}>{sampleTime}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>合作客户</View>
            <View className={s.item_value}>{institutionName || '-'}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>送检医院</View>
            <View className={s.item_value}>{submitHospitalName || '-'}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>送检医生</View>
            <View className={s.item_value}>{submitDoctor || '-'}</View>
          </View>
          <View className={s.item}>
            <View className={s.item_title}>知情同意书图片</View>
            <View className={s.item_files}>
              {
                files.map(url => (
                  <View key={url} className={s.item_files__item}>
                    <Image
                      src={url}
                      className={s.item_files__inner}
                      onClick={() => this.previewImage(url, files)}
                    />
                  </View>
                ))
              }
            </View>
          </View>
        </View>
      </View>
    )
  }
}
