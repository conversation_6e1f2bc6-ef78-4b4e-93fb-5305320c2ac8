import Taro, { Component } from '@tarojs/taro'
import { View, Text, Input, Image, Picker } from "@tarojs/components";
import PropTypes from 'prop-types';

import arrowPng from '@/resources/images/arrow-right.png'

import s from './index.module.scss'

export default class Field extends Component {
  constructor(props) {
    super(props)
  }

  render() {
    const { label, labelWidth, placeholder, placeholderStyle, required, border, labelStyle, disabled,
      mode, range, rangeKey, value, valueLabel, onChange } = this.props

    const showValueLabel = mode === 'selector' ?
      range[value] ? range[value][rangeKey] : valueLabel :
      value

    return (
      <Picker
        mode={mode}
        value={value}
        disabled={disabled}
        range={range}
        range-key={rangeKey}
        onChange={onChange}
      >
        <View className={[s.wx_field, border && s.border, required && s.required, disabled && s.disabled]}>
          <View className={[s.wx_field__body, 'f-row', 'f-c-center']}>
            { label ? <Text className={s.wx_field__label} style={{ width: labelWidth, ...labelStyle }}>{ label }</Text> : null }
            <Input
              className={[s.wx_field__input, 'f-1']}
              value={showValueLabel}
              placeholder={placeholder}
              placeholder-style={placeholderStyle}
              disabled
            />
            <Image className={s.wx_field__right} src={arrowPng} />
          </View>
        </View>
      </Picker>
    )
  }
}

Field.propTypes = {
  onSetValue: PropTypes.func,
  label: PropTypes.string,
  labelWidth: PropTypes.string,
  labelStyle: PropTypes.object,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  placeholderStyle: PropTypes.object,
  required: PropTypes.bool,
  border: PropTypes.bool,
  type: PropTypes.string,
  maxlength: PropTypes.number,
  disabled: PropTypes.bool,
  range: PropTypes.array,
  rangeKey: PropTypes.string,
  mode: PropTypes.string,
};
Field.defaultProps = {
  labelWidth: '180rpx',
  placeholder: '请输入',
  placeholderStyle: {
    fontSize: '14px',
  },
  required: false,
  border: true,
  type: 'text',
  maxlength: 3000,
  labelStyle: { color: '#000' },
  disabled: false,
  range: [],
  rangeKey: 'label',
  mode: 'selector'
};
