import Taro, { Component } from '@tarojs/taro'
import { View, Text, Input, Image, ScrollView, Button, Icon } from "@tarojs/components";
import PropTypes from 'prop-types';
import Empty from '@/component/empty'

import arrowPng from '@/resources/images/arrow-right.png'

import s from './index.module.scss'

export default class Field extends Component { 
  constructor(props) {
    super(props)
    this.state = {
      show: false,
      selectedValue: props.value || '',  
      keyword: '',
      list: [],
    }
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.value !== this.props.value) {
      this.setState({
        selectedValue: nextProps.value
      })
    }
  }
   
  search = (e) => {
    const keyword = e.target.value
    const { range } = this.props
    const list = range.filter(v => v.label && v.label.toLowerCase().includes(keyword.toLowerCase()))
    this.setState({ keyword, list })
  }

  maskChange = show => {
    this.setState({ show })
    const { onMaskChange, range, value } = this.props
    onMaskChange && onMaskChange(show)
    if (show) {
      this.setState({ list: range, keyword: '', selectedValue: value })
      // iOS Safari 修复：延迟强制重新渲染以修复定位问题
      setTimeout(() => {
        this.forceUpdate()
      }, 50)
    }
  }

  submit() {
    const { selectedValue } = this.state
    const { range } = this.props
    if (selectedValue === '') return Taro.showToast({ title: '请先选择', icon: 'none', duration: 1000 });
    const { onChange } = this.props
    const item = range.find(v => selectedValue !== '' && v.value == selectedValue)
    onChange && onChange(selectedValue, item || {})
    this.maskChange(false)
  }

  render() {
    const { show, keyword, list, selectedValue } = this.state
    const { label, labelWidth, placeholder, placeholderStyle, required, border, labelStyle, disabled,
    value, valueLabel, range } = this.props

    const currentItem = range.find(v => value !== '' && v.value == value)
    const showValue = currentItem ? currentItem.label : (valueLabel || value)

    return (
      <View>
        <View className={[s.wx_field, border && s.border, required && s.required, disabled && s.disabled]}>
          <View className={[s.wx_field__body, 'f-row', 'f-c-center']} onClick={() => !disabled && this.maskChange(true)}>
            { label ? <Text className={s.wx_field__label} style={{ width: labelWidth, ...labelStyle }}>{ label }</Text> : null }
            <Input
              className={[s.wx_field__input, 'f-1']}
              value={showValue}
              placeholder={placeholder}
              placeholder-style={placeholderStyle}
              disabled
            />
            <Image className={s.wx_field__right} src={arrowPng} />
          </View>
        </View>
        {/* 遮罩 */}
        { show ?
        <View className={s.page_mask}>
          <View className={s.select}>
            <View className={s.select_header}>
              <Input
                className={[s.select_header__input]}
                value={keyword}
                placeholder='请输入关键字搜索'
                onInput={this.search}
              />
            </View>
            { list.length ? <ScrollView className={s.select_body} scroll-y>
              {list.map(v =>
              <View
                className={[s.select_body__line, selectedValue !== '' && selectedValue == v.value && s.selected]}
                key={v.value}
                onClick={() => this.setState({ selectedValue: v.value })}
              >
                <Text className={s.select_body__line_text}>{v.label}</Text>
                { selectedValue !== '' && selectedValue == v.value ? <Icon type='success_no_circle' color='#308B91' size='14' /> : null }
              </View>
              )}
            </ScrollView> :
            <View className={s.select_body__empty}><Empty text='暂无数据' /></View>}
            <View className={s.select_footer}>
              <Button className={[s.select_footer_button, s.cancel]} onClick={() => this.maskChange(false)}>取 消</Button>
              { list.length ? <Button className={[s.select_footer_button, s.submit]} onClick={this.submit}>确 认</Button> : null }
            </View>
          </View>
        </View> : null }
      </View>
    )
  }
}


Field.propTypes = {
  label: PropTypes.string,
  labelWidth: PropTypes.string,
  labelStyle: PropTypes.object,
  value: [PropTypes.string, PropTypes.number],
  placeholder: PropTypes.string,
  placeholderStyle: PropTypes.object,
  required: PropTypes.bool,
  border: PropTypes.bool,
  disabled: PropTypes.bool,
  range: PropTypes.array,
};
Field.defaultProps = {
  labelWidth: '180rpx',
  placeholder: '请选择',
  placeholderStyle: {
    fontSize: '14px',
  },
  required: false,
  border: true,
  labelStyle: { color: '#000' },
  disabled: false,
  range: [],
};
