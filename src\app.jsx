import Taro, { Component } from '@tarojs/taro'
import Index from './pages/index';

// import * as Api from './api';
import './app.scss';

// 如果需要在 h5 环境中开启 React Devtools
// 取消以下注释：
// if (process.env.NODE_ENV !== 'production' && process.env.TARO_ENV === 'h5')  {
//   require('nerv-devtools')
// }

// preval`
//   if (process.env.NODE_ENV === 'development') {
//     module.exports = process.env.NODE_ENV
//   } else {
//     module.exports = null
//   }
// ` 
class App extends Component {

  state = {}

  componentWillMount() {
    // const SDKVersion = Taro.getSystemInfoSync().SDKVersion;
    // console.log('基础库版本', SDKVersion);
    // let isSDKVersion = false;
    // let isVersion = false;
    // // 基础库版本是否大于2.8.3
    // if (this.compareVersion(SDKVersion, '2.8.3') >= 0) {
    //   isSDKVersion = true;
    // } else {
    //   isSDKVersion = false;
    // }
    // // 微信版本是否大于7.0.0
    // const version = Taro.getSystemInfoSync().version;
    // if (this.compareVersion(version, '7.0.0') >= 0) {
    //   isVersion = true;
    // } else {
    //   isVersion = false;
    // }
    //
    // this.globalData.isSDKVersion = isSDKVersion;
    // this.globalData.isVersion = isVersion;
  }

  componentDidMount() {
    // this.setHrdInfo();
  }

  componentDidShow() {
    // this.getSystemTips();
  }

  componentDidHide() { }

  componentDidCatchError() { }

  config = {
    subPackages: [
      {
        root: 'package1',
        name: 'package1',
        pages: [
          /** 注册申请 */
          'regist/index',
          'regist/detail/index',
          'regist/list/index',
          /** webview */
          'webview/index',
          'consult/choosetype/index',
          'consult/chat/index',
          /** 地址管理 */
          'address/index',
          'address/edit/index'
        ]
      },
    ],
    pages: [
      'pages/patient/index',
      'pages/consultation-opinion/index',
      'pages/consultation-opinion/consultation-report/index',
      'pages/patient/health-record/index',
      'pages/index/index',
      'pages/index/change-pass',
      'pages/patient/health-record/visiting-record/consultation-application/index', // 会诊
      'pages/patient/health-record/visiting-record/add-visiting-record/index',

      'pages/patient/referral/unreferral/index', // 转诊
      'pages/patient/referral/referrallist/index', // 转诊
      'pages/patient/referral/referrecord/index', // 转诊
      'pages/patient/bill/unbill/index', // 会诊
      'pages/patient/bill/makebill/index', // 开单
      'pages/patient/bill/makebill/pay/index', // 支付
      'pages/patient/bill/makebill-list/index', // 开单记录
      'pages/patient/bill/makebill-detail/index', // 开单缴费详情页
      'pages/patient/bill/billlist/index', // 开单
      'pages/patient/bill/billrecord/index', // 开单
      'pages/patient/bill/perfectinformation/index', // 完善样本信息
      'pages/patient/bill/perfectinformationfetail/index',// 查看样本信息
      'pages/msg/index',
      'pages/consultation/video/index',
      'pages/consultation/chat/index',
      'pages/personal-center/index/index',
      'pages/personal-center/record-list/index',
      'pages/personal-center/record-detail/index',
      /** 修改密码 */
      'pages/personal-center/update-pass/index',

      /** 遗传课堂 */
      'pages/classroom/index',
      'pages/classroom/Child/Article/detail',
      'pages/classroom/Child/Activity/detail',
      'pages/classroom/records/index',
      'pages/classroom/records/detail',
      /** 活动报名 */
      'pages/classroom/application/index',
      /** 样本上传 */
      'pages/sample-upload/index',
      /** 游客页面 */
      'pages/guest/index',
      /** 报名活动列表 */
      'pages/guest/activity-list/index',
      /** 健康档案 */
      'pages/patient/health-record-v2/index',
      'pages/patient/health-record-v2/base-info/index',
      'pages/patient/health-record-v2/family-history/index',
      'pages/patient/health-record-v2/visit-records/index',
      'pages/patient/health-record-v2/examination/index',
      'pages/patient/health-record-v2/disease-desc/index',
      'pages/patient/health-record-v2/reports/index',
      /** 样本录入 */
      'pages/sample/index',
      /** 待上传列表 */
      'pages/sample/ready-list/index',
      /** 样本录入页面 */
      'pages/sample/check-in/index',
      /** 样本详情 */
      'pages/sample/detail/index',
      /** 已上传列表 */
      'pages/sample/already-list/index',
      'pages/consult/index',
      /** 样本寄送 */
      'pages/sample/send-out/index',
      'pages/sample/send-list/index',
      'pages/sample/send-detail/index',
      /** 样本详情展示 */
      'pages/sample/sample-detail/index',
      /** 问卷详情 */
      'pages/sample/questionnaire-detail/index',
      /** 问卷查看 */
      'pages/sample/questionnaire-view/index'
    ],
    window: {
      backgroundTextStyle: 'light',
      navigationBarBackgroundColor: '#fff',
      navigationBarTitleText: '登录33355',
      navigationBarTextStyle: 'black'
    },
    tabBar: {
      color: '#989898',
      selectedColor: '#30A1A6',
      backgroundColor: '#ffffff',
      borderStyle: 'white',
      current: 1,
      list: [
        {
          pagePath: 'pages/patient/index',
          iconPath: './resources/images/tarBar/patient.png',
          selectedIconPath: './resources/images/tarBar/patient-active.png',
          text: '我的患者'
        },
        // {
        //   pagePath: 'pages/consult/index',
        //   iconPath: './resources/images/tarBar/consult.png',
        //   selectedIconPath: './resources/images/tarBar/consult.png',
        //   text: '联盟咨询'
        // },
        {
          pagePath: 'pages/msg/index',
          iconPath: './resources/images/tarBar/chat.png',
          selectedIconPath: './resources/images/tarBar/chat.png',
          text: '联盟会诊'
        },
        {
          pagePath: 'pages/classroom/index',
          iconPath: './resources/images/tarBar/classroom.png',
          selectedIconPath: './resources/images/tarBar/classroom-active.png',
          text: '遗传课堂'
        },
        {
          pagePath: 'pages/personal-center/index/index',
          iconPath: './resources/images/tarBar/user.png',
          selectedIconPath: './resources/images/tarBar/user-active.png',
          text: '个人中心'
        }
      ]
    },
    permission: {
      "scope.userLocation": {
        desc: "你的位置信息将用于寄送样本"
      }
    },
    requiredPrivateInfos: ["getLocation", "chooseAddress"],//获取位置
    plugins: {
      //快递100
      kdPlugin: {
        version: "1.1.4",
        provider: "wx6885acbedba59c14"
      }
    }
  }

  globalData = {
    pageTips: null,
    appLaunchOption: {},
    hasEnterVideoRoomAgain: false, //判断一下是否进入过房间，关闭问诊时清掉
    videoIsMin: false, // 记录会诊的视频是否最小化了
  }

  // getTemplateId = async () => {
  //   const { code, data } = await Api.getTemplateId({
  //     moduleIds: JSON.stringify(MODULE_IDS)
  //   });
  //   if (code == 0 && data && data.length && data[0].status == 'init') {
  //     return data[0].templateId;
  //   }
  //   return '';
  // }

  // save = async (param) => {
  //   const { code, data } = await Api.saveTemplateStatus(param);
  //   if (code == 0 && data) {
  //     Taro.showToast({
  //       title: '授权成功',
  //       icon: 'success'
  //     });
  //   }
  // }

  // setHrdInfo = async () => {
  //   const hrdType = Taro.getStorageSync(`${Taro.getEnv()}_hospital_type_hrd_info`);
  //   if (hrdType) {
  //     return false;
  //   }
  //   const sysInfo = await Taro.getSystemInfoSync();
  //   const { model } = sysInfo || {};
  //   if (model.indexOf('iPhone X') > -1) {
  //     Taro.setStorageSync(`${Taro.getEnv()}_hospital_type_hrd_ipx`, 1);
  //   } else {
  //     Taro.setStorageSync(`${Taro.getEnv()}_hospital_type_hrd_ipx`, 2);
  //   }
  //   Taro.setStorageSync(`${Taro.getEnv()}_hospital_type_hrd_info`, JSON.stringify(sysInfo));
  //
  //   const headerBtnPosi = Taro.getMenuButtonBoundingClientRect();
  //   Taro.setStorageSync(`${Taro.getEnv()}_hospital_type_posi_info`, JSON.stringify(headerBtnPosi));
  // }

  // 比对版本
  // compareVersion = (v1, v2) => {
  //   v1 = v1.split('.');
  //   v2 = v2.split('.');
  //   const len = Math.max(v1.length, v2.length);
  //   while (v1.length < len) {
  //     v1.push('0');
  //   }
  //   while (v2.length < len) {
  //     v2.push('0');
  //   }
  //
  //   for (let i = 0; i < len; i++) {
  //     const num1 = parseInt(v1[i]);
  //     const num2 = parseInt(v2[i]);
  //     if (num1 > num2) {
  //       return 1;
  //     } else if (num1 < num2) {
  //       return -1;
  //     }
  //   }
  //   return 0;
  // }

  // getSystemTips = async () => {
  //   const reqData = { noAuthOn999: true, status: '0' };
  //   const { code, data = [] } = await Api.getSystemTips(reqData);
  //   if (code === 0) {
  //     const { appLaunchOption = {} } = this.globalData;
  //     const { query = {} } = appLaunchOption;
  //     const pageTips = {};
  //     const isPreView = query.version === 'trial';
  //     data.forEach(item => {
  //       pageTips[item.remindKey] =
  //         (isPreView ? item.preContent : item.content) || '';
  //     });
  //     this.globalData.pageTips = pageTips;
  //   }
  // }

  // getSystemTipsByKey = async (ags = []) => {
  //   const targetTip = {};
  //   if (Array.isArray(ags)) {
  //     let { pageTips } = this.globalData;
  //     if (!pageTips) {
  //       await this.getSystemTips();
  //       pageTips = this.globalData.pageTips;
  //       if (!pageTips) {
  //         return targetTip;
  //       }
  //     }
  //     ags.forEach(({key, param = {}}) => {
  //       let tTip = '';
  //       if (Object.keys(pageTips).length) {
  //         if (Object.keys(param).length) {
  //           tTip = this.setMessageVal(pageTips[key], param);
  //         } else {
  //           tTip = pageTips[key];
  //         }
  //       }
  //       targetTip[key] = tTip;
  //     });
  //   }
  //   return targetTip;
  // }

  // setMessageVal = (msgbase = '', realval) => {
  //   let msgStr = msgbase;
  //   for (let o in realval) {
  //     msgStr = msgStr.replace(new RegExp(`{{${o}}}`, 'g'), realval[o]);
  //   }
  //   return msgStr;
  // }

  // 在 App 类中的 render() 函数没有实际作用
  // 请勿修改此函数
  render() {
    return (
      <Index />
    )
  }
}

Taro.render(<App />, document.getElementById('app'))

