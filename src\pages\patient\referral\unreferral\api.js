import { post, uploadFile, cusUploadFile} from '@/utils/request';

//预创建会诊
export const createConsultation = (param) => post('/api/consultation/create', param);

//提交会诊申请
export const applyConsultation = (param) => post('/api/consultation/applyReferral', param);

// 根据pid获取患者信息
export const getPatientInfoByPid = (param) => post('/api/consultation/getPatientInfoByPid', param);

// 获取附件文件类型
// export const getFileTypeList = (param) => post('/api/consultation/getFileTypeList', param);

// 添加附件
export const addFile = (param) => post('/api/consultation/file/add', param);

export const uploadImages = (param, paramData) => cusUploadFile(param, paramData);
