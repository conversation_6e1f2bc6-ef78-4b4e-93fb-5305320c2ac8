import Taro, { Component } from "@tarojs/taro";
import { View, Input, Button } from '@tarojs/components';

import s from './index.module.scss';
import * as API from './api'

export default class UpdatePass extends Component {

  constructor(props) {
    super(props)
    this.state = {
      canLogin: false,
      password: '',
      newPassword1: '',
      newPassword2: '',
    }
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '修改密码',
    navigationBarTextStyle: 'black'
  };

  handleInput = (key, val) => {
    this.setState({
      [key]: val
    }, this.setCanLogin)
    
  }

  setCanLogin() {
    const { password, newPassword1, newPassword2 } = this.state
    this.setState({ canLogin: password && newPassword1 && newPassword2 })
  }

  showError(title, duration = 1500) {
    Taro.showToast({ title, icon: 'none', duration });
  }

  validate = () => {
    const { password, newPassword1, newPassword2 } = this.state
    if (!password) return Promise.resolve('请输入原始密码')
    if (!newPassword1) return Promise.resolve('请输入新密码')
    if (!newPassword2) return Promise.resolve('请确认新密码')
    const reg = /^(?=.*?[A-Za-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,18}$/;
    if (!reg.test(newPassword1)) return Promise.resolve('密码为长度为8-18位字符,需包含数字+字母+符号，空格除外')
    if (newPassword1 !== newPassword2) return Promise.resolve('两次密码不一致')
    return Promise.resolve()
  }

  submit = async () => {
    const { canLogin, password, newPassword1 } = this.state
    if (!canLogin) return
    const error = await this.validate()
    if (error) return this.showError(error)
    const { code } = await API.changePwd({ password, newPassword: newPassword1 })
    if (code !== 0) return
    Taro.showToast({ title: '修改成功' })
    setTimeout(() => Taro.navigateBack(), 1500)
  }

  render() {
    const { canLogin } = this.state
    return (
      <View className={s.page}>
        <View className={s.page_body}>
          <View className={s.inputItem}>
            <Input
              password
              placeholder='请输入原始密码'
              onInput={e => this.handleInput('password', e.detail.value)}
              maxLength='18'
            />
          </View>
          <View className={s.inputItem}>
            <Input
              password
              placeholder='请输入新密码'
              onInput={e => this.handleInput('newPassword1', e.detail.value)}
              maxLength='18'
            />
          </View>
          <View className={s.inputItem}>
            <Input
              password
              placeholder='请再次输入新密码'
              onInput={e => this.handleInput('newPassword2', e.detail.value)}
              maxLength='18'
            />
          </View>

          <Button
            className={[s.submit, !canLogin && s.disabled]}
            onClick={this.submit}
          >登 录</Button>
        </View>
      </View>
    )
  }
}
