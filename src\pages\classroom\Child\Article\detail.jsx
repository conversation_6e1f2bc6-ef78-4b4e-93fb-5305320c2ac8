import Taro, { Component } from '@tarojs/taro';
import { View, RichText, WebView } from '@tarojs/components';
import s from './index.module.scss'
import * as API from '../../api'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      contentType: 1,
      content: '',
    }
  }

  componentWillMount() {
    this.getDetail()
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '遗传知识',
    navigationBarTextStyle: 'black'
  };

  getDetail = async () => {
    const { id: articleId } = this.$router.params;
    const { code, data } = await API.getArticleDetail({ articleId })
    if (code !== 0) return
    const { content, contentType} = data || {}
    this.setState({ content, contentType })
  }

  render() {
    const {contentType, content} = this.state
    return (
      <View className={s.detail_page} >
        {
          contentType === 1 ?
            <RichText nodes={content}></RichText> :
            <WebView src={content}></WebView>
        }
      </View>
    )
  }
}
