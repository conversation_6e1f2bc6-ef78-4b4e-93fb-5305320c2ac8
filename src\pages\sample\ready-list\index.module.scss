.page{
  position: relative;
  padding: 24px;
  padding-bottom: 144px; /* 为底部按钮留出空间 */
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.header, .collectHeader{
  padding: 40px;
  border-radius: 8px;
  background-color: #FFF;
  display: flex;
  align-items: center;
  &_icon{
    width: 80px;
    height: 80px;
    margin-right: 24px;
  }
  &_title{
    flex: 1;
    color: rgba(0, 0, 0, 0.90);
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: 54px; /* 150% */
  }
  &_right{
    width: 24px;
    height: 24px;
  }
}

.collectHeader {
  margin-top: 24px;
  background-color: #FFF;
  .header_title {
    color: rgba(0, 0, 0, 0.90);
  }
}

.detailHeader {
  margin-top: 24px;
  padding: 40px;
  border-radius: 8px;
  background-color: #FFF;
  display: flex;
  align-items: center;

  .header_icon{
    width: 80px;
    height: 80px;
    margin-right: 24px;
  }
  .header_title{
    flex: 1;
    color: rgba(0, 0, 0, 0.90);
    font-size: 36px;
    font-style: normal;
    font-weight: 600;
    line-height: 54px; /* 150% */
  }
  .header_right{
    width: 24px;
    height: 24px;
  }
}

/* 扫描相机样式 */
.scannerOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.scannerContainer {
  width: 90%;
  max-width: 600px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.scannerTitle {
  padding: 20px;
  background-color: #3F969D;
  color: white;
  font-size: 32px;
  font-weight: 600;
  text-align: center;
}

.scanner {
  width: 100%;
  height: 400px;
}

.scannerControls {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.cancelButton {
  width: 400px;
  height: 80px;
  line-height: 80px;
  background: #ffffff;
  color: #3F969D;
  border-radius: 40px;
  font-size: 28px;
  font-weight: 500;
  box-shadow: none;
  margin: 10px 0;
  border: 1px solid #e0e0e0;
}

/* 用户信息卡片样式 */
.userInfoCard {
  margin-top: 24px;
  padding: 24px;
  background-color: #FFF;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cardTitle {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 12px;
}

.userInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.infoItem {
  display: flex;
  font-size: 28px;
  line-height: 42px;
}

.infoLabel {
  width: 160px;
  color: rgba(0, 0, 0, 0.6);
}

.infoValue {
  flex: 1;
  color: #333;
  font-weight: 500;
}

/* 步骤提示样式 */
.stepPrompt {
  margin-top: 24px;
  padding: 24px;
  background-color: #FFF;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stepTitle {
  font-size: 30px;
  font-weight: 600;
  color: #3F969D;
  margin-bottom: 24px;
}

.sampleInputContainer {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0 24px;
  margin-bottom: 24px;
}

.sampleInput {
  flex: 1;
  height: 90px;
  font-size: 30px;
}

.scanIcon {
  width: 48px;
  height: 48px;
}

.bindButton {
  width: 100%;
  height: 90px;
  line-height: 90px;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  color: white;
  font-size: 32px;
  font-weight: 600;
  border-radius: 45px;
}

.block{
  margin-top: 24px;
  padding: 32px;
  border-radius: 8px;
  background-color: #FFF;
  box-sizing: border-box;
  /* 移除高度限制，允许内容自然展开 */
  &_title{
    color: rgba(0, 0, 0, 0.90);
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 48px; /* 150% */
    &__inner {
      color: #3F969D;
    }
  }

  .list{
    margin-bottom: -32px;
    .item{
      padding: 24px 0;
      box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.06) inset;
      &:last-of-type{
        box-shadow: none;
      }
      &_header{
        display: flex;
        align-items: center;
        &__icon{
          margin-right: 12px;
          width: 32px;
          height: 32px;
        }
        &__title{
          color: #3F969D;
          font-size: 32px;
          font-weight: 600;
          line-height: 48px; /* 150% */
        }
      }
      &_footer{
        margin-top: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba(0, 0, 0, 0.40);
        font-size: 24px;
        font-weight: 400;
        line-height: 36px; /* 150% */
      }
    }
  }

  // 为Empty组件定制样式，减少空白区域
  :global(.wgt-empty-top) {
    height: auto;
    min-height: 200px;

    .wgt-empty-box {
      padding-top: 40px;
      padding-bottom: 40px;

      .wgt-empty-img {
        width: 120px;
        height: 120px;
      }

      .wgt-empty-txt {
        padding: 16px 0;
        font-size: 28px;
        color: #999999;
        line-height: 32px;
      }
    }
  }
}


.footer{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24px;
  background-color: #fff;
  z-index: 10;
  .button{
    height: 96px;
    width: 100%;
    line-height: 96rpx;
    font-size: 36px;
    border-radius: 76rpx;
    background: $color-primary;
    color: #fff;
    &::after{
      border: none;
    }
  }
}
