import Taro, { Component } from '@tarojs/taro';
import { Text, Textarea, View, Image } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';


export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '会诊申请',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);
    this.state = {
      institutionId: '',
      doctorId: '',
      patientName: '',
      relationPatientId: '',
      presentIll: '',
      mainSuit: '',
      pastIll: '',
      familyIssue: '',
      personalIssue: '',
      marriageIssue: '',
      periodIssue: '',
      reason: '',
      purpose: '',
      existDiagnosis: '',
      fileList: [],
    };
  }

  componentDidMount() {
    const { userId: doctorId = '', institutionId = '' } = JSON.parse(Taro.getStorageSync('userInfo') || '{}');
    this.setState({ doctorId, institutionId });
    this.getPatientInfoByPid();
    // this.getFileTypeList();
  }

  getPatientInfoByPid = async () => {
    const { patientPid } = this.$router.params;
    const { code, data } = await Api.getPatientInfoByPid({ pid: patientPid });
    if (code === 0) {
      const { patientName, id: relationPatientId } = data;
      console.log(relationPatientId);
      console.log(patientName);
      this.setState({ patientName, relationPatientId })
    }
  };

  // getFileTypeList = async () => {
  //   const {code, data} = await Api.getFileTypeList();
  //   if (code === 0) {
  //     const {patientName, id: relationPatientId} = data;
  //     this.setState({patientName, relationPatientId})
  //   }
  // }


  inputPresentIll = (e) => {
    this.setState({
      presentIll: e.target.value.trim()
    })
  };

  inputPatientName = (e) => {
    this.setState({
      patientName: e.target.value.trim()
    })
  };
  inputMainSuit = (e) => {
    this.setState({
      mainSuit: e.target.value.trim()
    })
  };

  inputPastIll = (e) => {
    this.setState({
      pastIll: e.target.value.trim()
    })
  };

  inputFamilyIssue = (e) => {
    this.setState({
      familyIssue: e.target.value.trim()

    })
  };

  inputPersonalIssue = (e) => {
    this.setState({
      personalIssue: e.target.value.trim()
    })
  };

  inputMarriageIssue = (e) => {
    this.setState({
      marriageIssue: e.target.value.trim()
    })
  };

  inputPeriodIssue = (e) => {
    this.setState({
      periodIssue: e.target.value.trim()
    })
  };

  inputexistDiagnosis = (e) => {
    this.setState({
      existDiagnosis: e.target.value.trim()
    })
  };

  inputPurpose = (e) => {
    this.setState({
      purpose: e.target.value.trim()
    })
  };

  inputReason = (e) => {
    this.setState({
      reason: e.target.value.trim()
    })
  };

  previewImage = (url) => {
    const { fileList } = this.state;
    Taro.previewImage({
      urls: fileList,
      current: url
    });
  };

  addImage = async () => {
    const chooseRes = await Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['camera', 'album']
    });
    if (chooseRes.errMsg == 'chooseImage:ok') {
      const tempFilePath = chooseRes.tempFilePaths[0];
      Taro.showLoading({ title: '上传中...', mask: true });
      let url = '';
      if (tempFilePath.length > 0) {
        url = await Api.uploadImages(tempFilePath);
        console.log(url);
      }
      Taro.hideLoading();
      if (url) {
        const { fileList } = this.state;
        fileList.push(url);
        this.setState({ fileList });
      }
    }
  };

  deleteImage = (index) => {
    let { fileList } = this.state;
    fileList.splice(index, 1);
    this.setState({ fileList });
  };

  attachFile = async (consultationId) => {
    const { fileList } = this.state;
    const requestArr = fileList.map(url => {
      return Api.addFile({ consultationId, url });
    });
    return Promise.all(requestArr);
  };

  validateData() {
    const requireList = [
      { key: 'mainSuit', value: '主诉' },
      { key: 'presentIll', value: '现病史' },
      { key: 'pastIll', value: '既往史' },
      { key: 'familyIssue', value: '家族史' },
      { key: 'existDiagnosis', value: '现有诊断' },
      { key: 'purpose', value: '会诊目的' },
      { key: 'reason', value: '会诊原因' }
    ];
    for (let item of requireList) {
      if (!this.state[item.key]) {
        Taro.showModal({
          title: '系统提示',
          icon: 'error',
          content: `${item.value}不能为空`,
          showCancel: false
        });
        return false;
      }
    }
    return true;
  }

  async submit() {
    if (!this.validateData()) return;
    const { doctorId, patientName, relationPatientId, institutionId } = this.state;
    const { pid } = this.$router.params;
    let param = {
      relationPatientId,
      patientPid: pid || '',
      patientName,
      doctorId,
      doctorHospitalId: institutionId,
    };
    const { code, data } = await Api.createConsultation(param);
    if (code == 0) {
      const { orderNo, consultationId } = data;

      await this.attachFile(consultationId);

      const { mainSuit, presentIll, pastIll, familyIssue, personalIssue, marriageIssue, periodIssue, existDiagnosis, reason, purpose } = this.state;
      let applyParam = {
        orderNo,
        mainIssue: mainSuit,
        nowIssue: presentIll,
        historyIssue: pastIll,
        familyIssue,
        personalIssue,
        marriageIssue,
        periodIssue,
        existDiagnosis,
        purpose,
        reason,
      };
      const { code: applyCode } = await Api.applyConsultation(applyParam);
      if (applyCode == 0) {
        Taro.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 1000,
        });
        setTimeout(function () {
          Taro.navigateTo({
            url: `/pages/personal-center/record-detail/index?id=${consultationId}&isNew=1`
          });
        }, 1000);
      }
    }
  }

  render() {
    const { presentIll, mainSuit, pastIll, familyIssue, personalIssue, marriageIssue, periodIssue, existDiagnosis, reason, purpose, patientName, relationPatientId, fileList } = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={`${s.tip}`}>注：为便于平台医生能提前掌握患者病情，更有效开展会诊，请详细描述患者相关信息。提交申请后，平台会审核，审核结果将发送微信通知消息给您，请您及时留意。</View>
        {relationPatientId ? (<View className={`${s.rowModule}`}>
          <View className={`${s.mainSuit}`}>患者</View>
          <View className={s.patientName}>{patientName}</View>
        </View>) : (<View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>患者</View>
          <Textarea value={patientName} onInput={(e) => this.inputPatientName(e)} placeholder={'请输入患者姓名'} maxlength={50} className={`${s.inputcss}`} autoHeight />
        </View>)}
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>主诉<Text>*</Text></View>
          <Textarea value={mainSuit} onInput={(e) => this.inputMainSuit(e)} placeholder={'请描述您对该次诊疗的临床诊断，采取何措施等情况，内容不超过100字'} maxlength={100} className={`${s.textarea}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>现病史<Text>*</Text></View>
          <Textarea value={presentIll} onInput={(e) => this.inputPresentIll(e)} placeholder={'请输入现病史'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>既往史<Text>*</Text></View>
          <Textarea value={pastIll} onInput={(e) => this.inputPastIll(e)} placeholder={'请输入既往史'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>家族史<Text>*</Text></View>
          <Textarea value={familyIssue} onInput={(e) => this.inputFamilyIssue(e)} placeholder={'请输入家族史'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>个人史<Text></Text></View>
          <Textarea value={personalIssue} onInput={(e) => this.inputPersonalIssue(e)} placeholder={'请输入个人史'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>婚育史<Text></Text></View>
          <Textarea value={marriageIssue} onInput={(e) => this.inputMarriageIssue(e)} placeholder={'请输入婚育史'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>月经史<Text></Text></View>
          <Textarea value={periodIssue} onInput={(e) => this.inputPeriodIssue(e)} placeholder={'请输入月经史'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>现有诊断<Text>*</Text></View>
          <Textarea value={existDiagnosis} onInput={(e) => this.inputexistDiagnosis(e)} placeholder={'请输入现有诊断'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>会诊目的<Text>*</Text></View>
          <Textarea value={purpose} onInput={(e) => this.inputPurpose(e)} placeholder={'请输入会诊目的'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>会诊原因<Text>*</Text></View>
          <Textarea value={reason} onInput={(e) => this.inputReason(e)} placeholder={'请输入会诊原因'} className={`${s.inputcss}`} autoHeight />
        </View>
        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>其他附件</View>
          <View className={s.fileContainer}>
            {
              fileList.map((url, index) => {
                return (
                  <View key={url} className={s.fileContent}>
                    <Image mode='aspectFit' src={url} onClick={() => this.previewImage(url)} />
                    <View className={s.deleteBtn} onClick={() => this.deleteImage(index)}>×</View>
                  </View>
                )
              })
            }
            <View className={s.addBtn} onClick={this.addImage}>+</View>
          </View>
        </View>
        <View className={`${s.suggestion}`} onClick={this.submit}><Text>提交</Text></View>
      </View>
    );
  }
}
