.bindcardList{
  background: #fff;
}

.bindcardListitem{
  padding: 30px;
  display: flex;
  position: relative;

  &.bindcardListitem_none{
    display: none;
  }

  &:before{
    content: ' ';
    position: absolute;
    left: 30px;
    right: 0;
    top: 0;
    border-top: 1px solid $color-border;
  }
  &:first-child:before{
    display: none;
  }

  .listitemHead{
    width: 200px;
    display: flex;
    align-items: center;
    @include textBreak;

    .list-title{
      flex: 1;
      font-size: 30px;
      color: $color-title;
      padding-right: 12px;
      position: relative;
      line-height: 1;

      &.listTitleSelect:before{
        content: ' ';
        position: absolute;
        right: 0;
        bottom: 0;
        box-sizing: border-box;
        border-bottom: 10px solid $color-title;
        border-right: 10px solid $color-title;
        border-top: 10px solid transparent;
        border-left: 10px solid transparent;
      }
    }
  }
  .listitemBody{
    flex: 1;
    padding-left: 30px;
    position: relative;
    @include textBreak;
  }
}

.listitemAccest{
  color: red;
}

.listitemAccest .listitemBody:before{
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  border-right: 2px solid $color-text;
  border-bottom: 2px solid $color-text;
  width: 16px;
  height: 16px;
  transform: translateY(-50%) rotate(-45deg);
}

.binduserRadio{
  margin-left: 110px;
  &:first-child{
    margin-left: 0;
  }
}
.binduserRadio_object{
  transform-origin: 0 30%;
  transform: scale(.7);
}
.binduserRadio_text{
  font-size: 30px;
}
.error{
  color: #ff613b;
}