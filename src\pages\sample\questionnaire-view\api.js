import { post } from '../../../utils/request';

/**
 * 获取问卷详情
 * @param {Object} param - 参数对象
 * @param {String} param.id - 问卷ID
 * @returns {Promise}
 */
export function getSurveyDetail(param = {}) {
  return post('/api/questionphone/getquestionscopeforid', param);
}

/**
 * 获取问卷答案
 * @param {Object} param - 参数对象
 * @param {String} param.id - 问卷ID
 * @param {String} param.questionUserId - 问卷用户ID
 * @returns {Promise}
 */
export function getSurveyAnswer(param = {}) {
  return post('/api/questionphone/getquestiondetailbyid', param);
}

/**
 * 获取问卷详情（医生端专用）
 * @param {Object} param - 参数对象
 * @param {String} param.id - 问卷ID
 * @param {String} param.questionUserId - 问卷用户ID
 * @param {String} param.orderId - 订单ID
 * @returns {Promise}
 */
export function getQuestionDetailById(param = {}) {
  return post('/api/questionphone/getquestiondetailbyid', param);
}
