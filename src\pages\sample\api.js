
import { post } from '@/utils/request';
import Taro from '@tarojs/taro'  // 添加 Taro 导入


/** 校验条码是否已存在 */
export const checkIsAlreadyExist = param => post('/api/sample/check-no-unique', param)

/** 根据样本编号获取产品信息 */
export const getContractByBarCode = param => post('/api/contract/get-contract-by-sampling-package', param, true, false)

export const logout = (param) => post('/api/doctor/logout', param);
/** 获取产品列表 */
export const getProductList = (param = {}) => post('/api/product/get-products', param)  
/** 获取医院列表 */
export const getHisList = () => post('/api/institution/get-by-page', { institutionType: 1, numPerPage: 9999 })

/** 保存样本 */
export const addSample = param => post('/api/sample/add-sample', param)

/** 获取草稿样品列表 */
export const getReadySampleList = (param) => post('/api/sample/get-by-page', { ...param, extfiled1: 1, numPerPage: 100, })

/** 获取已上传样品列表 */
export const getAlreadySampleList = (param) => post('/api/sample/get-by-page', { ...param, extfiled1s: [2, 3], numPerPage: 100, })

/** 获取所有样品列表 */
export const getAllSampleList = () => post('/api/sample/count-by-param', { }, false)

/** 确认收样 */
export const batchUpdateStatus = params => post('/api/sample/updateStatus', params)

/** 获取样品详情 */
export const getSampleDetail = params => post('/api/sample/get-by-id', params)

/** 获取样品详情 */
export const deleteSample = params => post('/api/sample/delete', params)

/** 获取样品详情 */
export const updateSample = params => post('/api/sample/update', params)

/** 获取合作客户 */
export const getInstitutionList = () => post('/api/institution/get-by-page', { numPerPage: 999999, })

/** 获取机构 */
export const getInstitutionListV2 = () => post('/api/sub-sample/get-institutions', { numPerPage: 999999, })

/** 根据用户ID获取用户信息 */
export const getUserInfoById = (userId) => post('/api/user/get-user-info', { userId })

/** 查询大健康样本订单详情 */
export const getHealthOrderById = (id) => post('/api/sample/getHealthOrderById', { id })

/** 绑定样本管 */
export const bindSample = (params) => post('/api/sample/bind-sample', params)

/** 保存样本编号 */
export const bindSampleNumber = (id, sampleNumber) => post('/api/sample/bindSampleNumber', { id, sampleNumber })

//////////样本寄送相关接口//////////////////////////

//获取地址列表
export const getAddressList = param => post('/api/address/addresslist', param);

//获取配置
export const getProfileByKey = param => post('/api/address/getProfileByKey', param);

//快递下单
export const createOrder = param => post('/api/mail/createorder', param);


//获取快递订单列表
export const getOrderList = param => post('/api/mail/getorderlist', param);

//获取快递订单详情
export const getOrderDetail = param => post('/api/mail/getorderdetail', param);

//取消快递订单
export const cancelOrder = param => post('/api/mail/cancelorder', param);

//获取账号的到付配置
export const getUserConfig = param => post('/api/personal/getUserConfig', param);







