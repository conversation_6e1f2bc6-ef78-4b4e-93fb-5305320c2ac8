import Taro from '@tarojs/taro';
import {
  REQUEST_QUERY
} from './config';
import * as Utils from './utils';

// 请求等待队列
const postQueue = [];
// 请求loading队列
const lodaingQueue = [];
// 是否有错误弹框
let hasErrorModal = false;

function resetState() {
  postQueue.length = 0;
  lodaingQueue.length = 0;
  hasErrorModal = false;
}

/**
 * 登录授权封装
 * @returns {*}
 */
const login = async () => {
  // 调用登录接口
  const {
    code: userInfoCode
  } = await Taro.login();
  if (!userInfoCode) {
    releasePostQueue(1);
    return false;
  }

  const userInfo = await Utils.getUserInfo();

  if (!userInfo || (userInfo.errMsg && userInfo.errMsg !== 'getUserInfo:ok')) {
    const _currentPages = Taro.getCurrentPages();
    let url = '';
    if (_currentPages.length) {
      const {
        route = '', options = {}
      } = _currentPages[_currentPages.length - 1];
      url = encodeURIComponent(`/${route}?${Utils.jsonToQueryString(options)}`);
    }
    resetState();
    await Taro.redirectTo({
      url: `/pages/common/auth/index?url=${url}`,
    });
    throw '警告：重定向到用户授权页进行主动授权';
  }
  const {
    iv,
    encryptedData
  } = userInfo;
  const queryUrl = `${$DOMAIN}/api/xcxcore/login`;
  // 请求后端，登录授权
  const {
    data: loginData = {}
  } = await Utils.httpRequest({
    url: queryUrl,
    data: {
      ...REQUEST_QUERY,
      code: userInfoCode,
      iv,
      encryptedData
    }
  });
  if (loginData.code !== 0) {
    Taro.removeStorageSync(`${Taro.getEnv()}_${$RUN_ENV}_login_access_token_${REQUEST_QUERY.hisId}`);
    releasePostQueue(1);
    return false;
  }
  const {
    login_access_token
  } = loginData.data || {};
  Taro.setStorageSync(login_access_token);
  releasePostQueue(0);
}

const releasePostQueue = (loginStatu, index) => { // loginStatu: 0 正常执行post, 1: 授权失败, 2: 跳转授权页面
  let loopLength = postQueue.length;
  if (Number.isInteger(index)) {
    loopLength = 1;
    const tmp = postQueue[index];
    postQueue.splice(index, 1);
    postQueue.unshift(tmp);
  }
  let errorKey = false;
  for (let i = 0; i < loopLength; i++) {
    const ret = {
      loginStatu
    };
    if (!errorKey && postQueue[0].showError && !postQueue[0].giveup) {
      errorKey = true;
      ret.showError = true;
    } else {
      ret.showError = false;
    }
    if (postQueue[0].resove) {
      postQueue[0].resove(ret);
    }
    postQueue.splice(0, 1);
  }
}

/**
 * 项目内post请求封装
 * @param url 请求链接，不带域名
 * @param options 请求体
 * @param showLoading 请求中显示loading，默认true
 * @param showError 请求错误或者code不为0显示错误，默认为true
 * @returns {*}
 */
export const post = async (url, options = {}, showLoading = true, showError = true) => {
  if (showLoading) {
    Taro.showLoading({
      title: '加载中',
      mask: true,
    });
    lodaingQueue.push('');
  }



  let response = {};

  const queueData = {
    url,
    options,
    showLoading,
    showError,
  };
  let awaitStatu = false;
  if (!options.noAuthOn999) {
    if (postQueue.length) {
      awaitStatu = true;
    } else {
      postQueue.push(queueData);
    }
  }
  if (awaitStatu) {
    if (!postQueue.length) {
      login(url);
    }
    const authData = await new Promise((res, rej) => {
      queueData.resove = res;
      queueData.reject = rej;
      postQueue.push(queueData);
    });
    if (authData.loginStatu === 1) {
      return handlePostData({
        data: {
          code: -1,
          msg: '获取授权失败',
        },
        statusCode: 200,
      }, url, options, showLoading, authData.showError);
    }
  }
  const login_access_token = url !== '/api/doctor/login/authorization' ? Taro.getStorageSync(`login_access_token`) : '';
  let fullPath = '';
  if (url.indexOf('?_route') > -1) {
    fullPath = `${url.startsWith('http') ? '' : $DOMAIN}${url}`;
  } else {
    fullPath = `${url.startsWith('http') ? '' : $DOMAIN}/doctorxcx${url}`;
  }
  const queryUrl = fullPath;
  try {
    response = await Utils.httpRequest({
      url: queryUrl,
      data: {
        ...REQUEST_QUERY,
        ...options,
        login_access_token
      },
    });
  } catch (e) {
    response = {
      statusCode: 600,
    };
  }
  return handlePostData(response, url, options, showLoading, showError);
};

const handlePostData = async (response = {}, url, options, showLoading, showError) => {
  if (showLoading) {
    lodaingQueue.pop();
    if (!lodaingQueue.length) {
      Taro.hideLoading();
    }
  }
  
  let data = {};
  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 请求成功
    data = response.data;
    if (data.code === 999 && !options.noAuthOn999) {
      const TAROENV = Taro.getEnv();
      if (TAROENV === Taro.ENV_TYPE.WEB) {
        const {
          protocol,
          host,
          pathname,
          search,
          hash
        } = window.location;
        const returnUrl = encodeURIComponent(`${protocol}//${host}${pathname}?${search.replace(/(\?)|(&returnRandomParam=\d*)/g, '')}&returnRandomParam=${Date.now()}${hash}`);
        window.location.href = `/oauth?platformId=${REQUEST_QUERY.platformId}&platformSource=${REQUEST_QUERY.platformSource}&url=${returnUrl}`;
        throw new Error('重定向页面授权');
      } else {
        hasErrorModal = true
        Taro.showModal({
          title: '系统提示',
          content: '您的账号在别处登录',
          showCancel: false,
          success: (res) => {
            Taro.clearStorageSync();
            console.log('res', res)
            hasErrorModal = false
            if (res.confirm) {
              Taro.reLaunch({
                url: '/pages/index/index'
              });
            }
          }
        })
      }
    } else if (data.code === 998) {
      // 授权失败，其他接口触发授权后重新请求数据
      const logined = await Taro.login();
      const info = await Taro.getUserInfo();
      if (info.encryptedData && info.iv) {
        const loginRes = await post('/doctorxcx/api/doctor/login/authorization', {
          code: logined.code,
          encryptedData: info.encryptedData,
          iv: info.iv
        });
        if (loginRes.code != 0) {
          data = loginRes.data;
          await Taro.redirectTo({
            url: '/pages/index/index'
          });
        } else {
          Taro.setStorageSync('login_access_token', loginRes.data.login_access_token);
          return await post(url, Taro);
        }
      } else {
        Taro.showToast({
          title: '获取微信授权信息失败！',
          icon: 'none',
          duration: 2000
        });
      }
    } else if (data.code == 886) {
      Taro.showToast({
        title: '您的账号已在别处登录',
        icon: 'none',
        duration: 2000
      });
      Taro.relaunch({
        url: '/pages/index/index'
      })
    } else if (data.code === 996) {
      hasErrorModal = true
      Taro.showModal({
        title: '系统提示',
        content: data.msg || '请设置密码',
        showCancel: false,
        success: (res) => {
          hasErrorModal = false
          if (res.confirm) {
            Taro.redirectTo({
              url: '/pages/index/change-pass'
            });
          }
        }
      })
    }
    if (!options.noAuthOn999) {
      releasePostQueue(0);
    }
  } else {
    // 请求失败
    data = {
      code: 10000,
      msg: response.statusCode,
    };
    postQueue.splice(0, 1);
    if (postQueue.length) {
      const {
        resove
      } = postQueue[0];
      if (typeof resove === 'function') {
        resove({
          loginStatu: 0
        });
      }
    }
  }
  const {
    reqPage,
    resPage
  } = response;
  if (data.code !== 0 && data.code !== 10000 && showError && reqPage === resPage && !hasErrorModal) {
    hasErrorModal = true;
    Taro.showModal({
      title: '系统提示',
      content: data.msg || '网络请求失败',
      showCancel: false,
      success() {
        // if (data.code == '10171004') {
        //   Taro.navigateBack();
        // }
        hasErrorModal = false;
      }
    });
  }
  return data;
}

/**
 * 第三方链接post请求封装
 * @param url 请求链接，为完整链接
 * @param options 请求体
 * @param showLoading 请求中显示loading，默认true
 * @param showError 请求错误或者code不为0显示错误，默认为true
 * @returns {*}
 */
export const thirdPost = async (url = '', options = {}, showLoading = true, showError = true) => {
  if (showLoading) {
    Taro.showLoading({
      title: '加载中',
      mask: true,
    });
  }
  let data = {};
  let response = {};
  try {
    response = await Taro.request({
      url,
      data: options,
      method: 'POST',
      header: {
        "content-type": "application/x-www-form-urlencoded",
      },
    });
  } catch (e) {
    response = {
      statusCode: 600,
    };
  }
  Taro.hideLoading();
  if (response.statusCode >= 200 && response.statusCode < 300) {
    // 请求成功
    data = response.data;
  } else {
    // 请求失败
    data = {
      code: response.statusCode || 10000,
      msg: `抱歉，请求失败，请检查网络后重试 (CODE:${response.statusCode})`,
    };
  }
  if (showError && data.code !== 0) {
    await Taro.showModal({
      title: '提示',
      content: data.msg || '',
      showCancel: false
    });
  }
  return data;
};

/**
 * 上传文件，传入 Array 可多个文件一起上传
 *
 * @param {*} files String||Array
 */
export const uploadFile = async (files = '') => {
  // 上传的请求
  const uploadReq = (filePath) => {
    const queryStr = Utils.jsonToQueryString(REQUEST_QUERY || {});
    const queryUrl = `${$DOMAIN}/api/files/uploadpic`;
    return new Promise(async (resolve, reject) => {
      const uploadRes = await Taro.uploadFile({
        url: queryUrl,
        filePath,
        name: 'upfile',
        formData: {
          serviceType: 'idcardocr',
          isPte: 'false',
          needCompress: 'false',
        },
        header: {
          "content-type": "application/x-www-form-urlencoded",
        },
      });
      if (uploadRes.statusCode >= 200 && uploadRes.statusCode < 300) {
        const uploadResData = JSON.parse(uploadRes.data);
        return uploadResData.code == 0 ? resolve(uploadResData.data.url) : reject(uploadResData);
      } else {
        reject(uploadRes);
      }
    })
  };
  try {
    let res = '';
    if (typeof files === 'string') {
      res = await uploadReq(files);
    } else if (typeof files === 'object' && Array.isArray(files)) {
      const promiseMap = [];
      files.map(item => {
        promiseMap.push(uploadReq(item));
      })
      //核心，使用promise.all处理多个请求
      // res = await Promise.all(promiseMap);
    }
    Taro.hideLoading();
    return res;
  } catch (error) {
    Taro.hideLoading();
    Taro.showModal({
      title: '提示',
      content: `抱歉，请求失败，请检查网络后重试 (CODE:${error.statusCode})`,
      showCancel: false,
    });
    return Promise.reject(error);
  }
}

/**
 * 上传文件，传入 Array 可多个文件一起上传
 *
 * @param {*} files String||Array
 */
 export const cusUploadFile = async (files = '', param) => {
  // 上传的请求
  const uploadReq = (filePath) => {
    const queryStr = Utils.jsonToQueryString(REQUEST_QUERY || {});
    const queryParam = Utils.jsonToQueryString(param || {});
    console.log('queryParam', queryParam);
    const queryUrl = `${$DOMAIN}/api/customize/uploadFile?_route=h242`;
    return new Promise(async (resolve, reject) => {
      const uploadRes = await Taro.uploadFile({
        url: `${queryUrl}&${queryParam}`,
        filePath,
        name: 'upfile',
        formData: {
          serviceType: 'idcardocr',
          isPte: 'false'
        },
        header: {
          "content-type": "application/x-www-form-urlencoded",
        },
      });
      if (uploadRes.statusCode >= 200 && uploadRes.statusCode < 300) {
        const uploadResData = JSON.parse(uploadRes.data);
        return uploadResData.code == 0 ? resolve(uploadResData.data.fileUrl) : reject(uploadResData);
      } else {
        reject(uploadRes);
      }
    })
  };
  try {
    let res = '';
    if (typeof files === 'string') {
      res = await uploadReq(files);
    } else if (typeof files === 'object' && Array.isArray(files)) {
      const promiseMap = [];
      files.map(item => {
        promiseMap.push(uploadReq(item));
      })
      //核心，使用promise.all处理多个请求
      // res = await Promise.all(promiseMap);
    }
    Taro.hideLoading();
    return res;
  } catch (error) {
    Taro.hideLoading();
    Taro.showModal({
      title: '提示',
      content: `抱歉，请求失败，请检查网络后重试 (CODE:${error.statusCode})`,
      showCancel: false,
    });
    return Promise.reject(error);
  }
}