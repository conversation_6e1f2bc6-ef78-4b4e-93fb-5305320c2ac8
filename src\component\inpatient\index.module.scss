.wgtUserBox{
  position: relative;
  z-index: 1;
  padding: 30px;
  background-color: #fff;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.05);
}
.wgtUserMain{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.wgtUserMainInfo{
  display: flex;
  align-items: center;
  flex: 1;
}
.wgtUserMainInfoTxt{
  font-size: 37px;
  color:#2d2d2d;
}
.wgtUserMainInfoLabel{
  flex: 1;
}
.wgtUserMainBtn{
  padding: 0 25px;
  font-size: 24px;
  height: 52px;
  line-height: 52px;
  color:#3ECEB6;
  border:1px solid #3ECEB6;
  border-radius: 999px;
}
.wgtUserExtra{
  margin-top: 20px;
  font-size: 28px;
  color:#989898;
  word-wrap: break-word;
  word-break: break-all;
}

// 就诊人切换弹窗，需要抽离组件出去
.wgtUserPopBox{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -999;
  visibility: hidden;
  transform: translateY(-1000%);
  &.active{
    z-index: 999;
    visibility: visible;
    transform: translateY(0);
    .wgtUserPopMask{
      background-color: rgba(0, 0, 0, .6);
    }
    .wgtUserPop {
      transform: translateY(0%);
    }
  }
}
.wgtUserPopMask{
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  transition: background .3s;
}
.wgtUserPop{
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  max-height: 90%;
  overflow-y: auto;
  background-color: #fff;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  transform: translateY(100%);
  transition: transform .3s 0.1s;
}

.wgtUserPopTitle{
  padding: 30px;
  font-size: 30px;
  color:#2d2d2d;
  text-align: center;
  border-bottom:1px solid #e5e5e5;
}
.wgtUserPopList{
  margin: 0 30px;
}
.wgtUserPopListItem{
  position: relative;
  padding: 25px 0;
  border-top:1px solid #e5e5e5;
  padding-right: 120px;
  &:first-child{
    border-top: none;
  }
}
.wgtUserPopListItemMain{
  display: flex;
  align-items: center;
  flex-direction: row;
}
.wgtUserPopListItemName{
  font-size: 37px;
  color:#2d2d2d;
}
.wgtUserPopListItemLabel{
  margin-left: 20px;
}
.wgtUserPopListItemNum{
  margin-top: 10px;
  font-size: 28px;
  color:#989898;
  word-wrap: break-word;
  word-break: break-all;
}
.wgtUserPopListItemIpt{
  display: none;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 38px;
  height: 27px;
  .image{
    width: 100%;
    height: 100%;
    vertical-align: top;
  }
  &.active{
    display: block;
  }
}
.wgtUserPopOpt{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  border-top:1px solid #e5e5e5;
}
.wgtUserPopOptItem{
  font-size: 34px;
  text-align: center;
  flex: 1;
  color:#3ECEB6;
  border-left:1px solid #e5e5e5;
  &:first-child{
    border-left:none;
  }
}
.wgtUserPopListItem{
  &.active{
    .wgtUserPopListItemIpt{
      display: block;
    }
  }
}
.wgtUserPopClose{
  position: absolute;
  right: 0;
  top:0;
  padding: 30px;
  width: 36px;
  height: 36px;
  background: url(#{$cdn}/close.png) no-repeat 50% 50%;
  background-size: 36px 36px;
  z-index: 9;
}

.noUserBox {
  background-color: #fff;
  width: 100%;
  height: 220px;
  padding: 30px;
  box-sizing: border-box;
}

.noUser {
  background-color: #fff;
  width: 690px;
  height: 160px;
  border-radius: 4px;
  box-sizing: border-box;
  text-align: center;
  background-image: url(#{$cdn}/bg-top.png);
  background-size: cover;

  .noUserBtn {
    display: inline-block;
    vertical-align: top;
    padding: 0 20px;
    line-height: 160px;
    color: #fff;
    font-size: 34px;
  }
}