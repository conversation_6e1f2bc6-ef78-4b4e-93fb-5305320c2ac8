import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import cn from 'classnames';
import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  render () {
    const { data = [] } = this.props;
    return data.map((list, key) => {
      return (
        <View className={styles.list} key={key}>
          {
            list.map((item) => {
              const { onClick = () => {} } = item;
              return (
                <View className={styles.item} key={item.label} onClick={() => onClick(item)}>
                  <View className={cn(styles.label, styles[item.label.classType])}>{item.label.text}</View>
                  <View className={styles.content}>
                    {
                      item.content.map((content) => {
                        return <View className={styles[content.classType]} key={content.text}>{content.text}</View>;
                      })
                    }
                  </View>
                </View>
              );
            })
          }
        </View>
      );
    });
  }
}
