.container {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 24px;
  box-sizing: border-box;
  .loginPageNotice{
    padding: 32rpx 24rpx;
    background: #FFFAF1;
    color: #BE8014;
    font-size: 24px;
    font-weight: 400;
    white-space: nowrap;
  }
  .header {
    margin: 80rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 42rpx;
    color: $color-title;
    font-weight: 600;
    image {
      margin-bottom: 24rpx;
      width: 150rpx;
      height: 150rpx;
    }
  }
  .inputItem {
    margin: 0 40px;
    margin-bottom: 24px;
    &.passConfirm{
      margin-top: 96px;
    }
    font-size: 32px;
    color: $color-title;
    background-color: $color-bg;
    border-radius: 16rpx;
    input {
      margin-left: 24rpx;
      height: 96rpx;
      line-height: 96rpx;
    }
    .inputItemSplit{
      margin: 0 24rpx;
      height: 40rpx;
      width: 1px;
      background-color: #E1E1E3;
    }
    .inputItemCode {
      margin-right: 24rpx;
      color: $color-primary;
      font-size: 24px;
      &::after{
        border: none;
      }
    }
  }
  .loginPageFooter{
    margin-top: 96rpx;
    padding: 0 40rpx;
  }
  .aggree{
    margin-bottom: 24px;
    color: rgba(0, 0, 0, 0.60);
    font-size: 28px;
    line-height: 48px;
    &_link{
      color: #2D666F;
      font-weight: 500;
    }
  }
  .btn{
    margin-bottom: 24rpx;
    height: 96rpx;
    line-height: 96rpx;
    font-size: 36px;
    border-radius: 76rpx;
    font-weight: 600;
    &::after{
      border: none;
    }
  }
  .loginBtn {
    background: $color-primary;
    color: #fff;
    &.disabled{
      opacity: 0.6;
      background-color: $color-primary !important;
      color: #FFF !important;
    }
  }
  .switchBtn{
    color: #000000B2;
    background: #0000000A;
  }
  .rePwd {
    margin: 40px;
    text-align: center;
    font-size: 24px;
    color: $color-text;
  }

  .register{
    margin-top: 24px;
    padding: 12px;
    text-align: center;
    color: $color-primary;
    font-size: 32px;
  }
}
