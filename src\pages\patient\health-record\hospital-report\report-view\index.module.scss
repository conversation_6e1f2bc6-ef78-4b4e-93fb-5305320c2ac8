.reportTitle {
  display: flex;
  background-color: #fff;
  width: 90%;
  justify-content: space-between;
  padding: 0 30px;
  box-sizing: border-box;
  margin: 20px auto 0;
  align-items: center;

  .viewShow {
    color: #4ECEB6;
    font-size: 30px;
  }

  .titleInfo {
    padding: 30px 0;
    width: 82%;

    .title {
      color: #000000;
      font-size: 34px;
      font-weight: 600;
    }

    text {
      color: #888888;
      font-size: 28px;
    }
  }
}

.itemModule {
  display: flex;
  background-color: #fff;
  width: 90%;
  justify-content: space-around;
  margin: auto;
  font-size: 30px;
  color: #888888;
  border-top: 1px solid #E2E9E8;
  border-bottom: 1px solid #E2E9E8;
  padding: 20px 0;
}

.itemCard{
  display: flex;
  flex: 1 1;
  background-color: #fff;
  width: 90%;
  // justify-content: space-around;
  margin: auto;
  font-size: 30px;
  color: #888888;
  border-top: 1px solid #E2E9E8;
  border-bottom: 1px solid #E2E9E8;
  padding: 20px 0;
  .cardTit{
    min-width: 160px;
    margin-left: 10px;
  }
  .cardConetnt{
    word-break: break-all;
    margin-left: 10px;
  }
}

.itemData {
  display: flex;
  background-color: #fff;
  width: 90%;
  justify-content: space-around;
  margin: auto;
  font-size: 30px;
  color: #353535;
  padding: 20px 0;
}


.item {
  flex: 1;
  text-align: center;
}
