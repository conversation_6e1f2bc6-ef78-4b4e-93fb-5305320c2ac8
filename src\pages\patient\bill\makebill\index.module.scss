.make_bill_page{
  background-color: $color-bg;
  height: 100vh;
}
.page_notice{
  padding: 32px 24px;
  background: #FFFAF1;
  color: #BE8014;
  font-size: 24px;
  font-weight: 400;
  white-space: nowrap;
}

.page_body{
  padding: 24px;
  .project_block{
    padding: 24px;
    background-color: #FFF;
    border-radius: 8px;
    &__label{
      position: relative;
      padding-left: 12px;
      color: #000;
      font-weight: 500;
      // font-size: 32px;
      &::before{
        content: "*";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translate(0, -50%);
        color: $color-warn;
      }
    }
    &__list{
      margin-top: 36px;
    }
    &__item{
      margin-bottom: 24px;
      padding: 24px;
      background-color: $color-bg;
      border-radius: 8px;
      font-size: 28px;
      color: #000;
      &_label{
        @include ellipsisLn()
      }
      &_price{
        margin-left: 24px;
        color: #CC8F24;
      }
    }
  }

  .page_footer{
    margin-top: 56px;
    .btn{
      margin-bottom: 24px;
      height: 96px;
      line-height: 96px;
      font-size: 36px;
      border-radius: 76px;
      font-weight: 600;
      &::after{
        border: none;
      }
      &.submit{
        background: $color-primary;
        color: #fff;
      }
      &.cancel{
        background: var(--grey-grey-04, rgba(0, 0, 0, 0.04));
      }
    }
  }
}

.tips{
  font-size: 24px;
  font-weight: 400;
  color: $color-text;
}

.input_item {
  padding: 0 24px;
  font-size: 28px;
  background-color: $color-bg;
  border-radius: 16px;
  &__inner {
    height: 96px;
    line-height: 96px;
  }
}
.ratio_item{
  padding: 0 24px;
  box-sizing: border-box;
  background-color: $color-bg;
  &__inner {
    width: 100%;
    height: 96px;
    line-height: 96px;
    font-size: 28px;
    color: #000;
  }
}

.modalContent{
  padding: 0 80px;
  text-align: center;
}
