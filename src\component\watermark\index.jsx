import Taro, { Component } from '@tarojs/taro';
import { View, Canvas } from '@tarojs/components';

import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {}

  componentDidMount () {
    const { text = [] } = this.props;
    if (text.length) {
      this.draw(text);
    }
  }
  
  componentWillReceiveProps(nextProp) {
    if (Array.isArray(nextProp.text)) {
      if (!this.drawText || nextProp.text.join() !== this.drawText.join()) {
        this.draw(nextProp.text);
      }
    }
  }

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  draw = (text) => {
    this.drawText = text;
    const query = Taro.createSelectorQuery().in(this.$scope);
    query.select('#myCanvas1')
      .fields({ node: true, size: true })
      .exec((res) => {
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        const dpr = Taro.getSystemInfoSync().pixelRatio;
        canvas.width = res[0].width * dpr;
        canvas.height = res[0].height * dpr;
        ctx.scale(dpr, dpr);

        ctx.font = '12px sans-serif';
        ctx.fillStyle = 'rgba(152,152,152,.2)';
        ctx.rotate(-12 * Math.PI / 180); // 设置文字的旋转角度，角度为45°；
        
        for (let i = 0; i < 2; i++) {
          ctx.beginPath();
          const pointX = 60 - (i * 50);
          const begY = 120 + (i * 250);
          for (let j = 0; j < this.drawText.length; j++) {
            ctx.fillText(this.drawText[j], pointX, begY + (20 * j));
          }
        }
      })
  }

  render () {
    return (
      <View className={styles.waterTop}> 
        <Canvas id='myCanvas1' type='2d' style={{ width: '100%', height: '100%' }} />
      </View>
    )
  }
}
