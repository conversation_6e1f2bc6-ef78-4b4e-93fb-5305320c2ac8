import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import PropTypes from 'prop-types'
import s from './index.module.scss'

export default class Tabs extends Component {

  constructor(props) {
    super(props)
    this.state = {

    }
  }

  render() {
    const { list = [], height, value, setValue } = this.props
    return (
      <View className={[s.tabs, 'f-row', 'f-c-center']}>
        {
          list.map(v => (
            <View
              key={v.value}
              className={[s.tabs_item, 'f-1', 'f-center', value === v.value && s.active]}
              style={{ height }}
              onClick={() => setValue(v.value)}
            >{v.label}</View>
          ))
        }
      </View>
    )
  }
}

Tabs.propTypes = {
  list: PropTypes.array,
  value: PropTypes.number,
  setValue: PropTypes.func,
  height: PropTypes.string
};
Tabs.defaultProps = {
  height: '66rpx'
};
