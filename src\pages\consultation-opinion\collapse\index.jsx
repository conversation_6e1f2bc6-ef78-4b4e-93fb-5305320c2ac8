import Taro, { Component } from '@tarojs/taro';
import {View, Text} from '@tarojs/components';
import s from './index.module.scss';


export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '会诊意见',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);
    this.state = {
      status: true,
    };
  }

  illTextShow = () => {
    const { status } = this.state;
    this.setState({
      status: !status
    })
  }

  render() {
    const {
      status,
    } = this.state;
    const { content, type, title, bottomView = false, doctorName = '', time = '' } = this.props;
    return (
      <View className={`${s.illnessDescription}`} >
        <View onClick={this.illTextShow} className={`${s.rowModule}`} style={{ borderBottom: 'none' }}>
          <View className={`${s.rowTitle}`}>{title}</View>
          <View>{status ? 'v' : '>'}</View>
        </View>
        {status && <View className={`${s.illText}`}>
          {type == 1 ? <View className={s.content}>{content || '暂无'}</View> :
            content.map((item, index) => {
              return (
                <View key={index} className={`${s.baseInfo}`}>
                  <View>{item.label}</View>
                  <Text>{item.value || ''}</Text>
                </View>
              )
            })
          }
          {bottomView && <View className={`${s.bottomView}`}>
            <View>医生姓名：<Text>{doctorName}</Text></View>
            <View>提交时间：<Text>{time}</Text></View>
          </View>}
        </View>
        }
      </View>
    );
  }
}
