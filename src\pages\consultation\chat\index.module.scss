page {
  height: 100%;
}
.container {
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
  background: $color-bg;
  position: relative;
  &.pt140 {
    padding-top: 140px;
  }
  scroll-view {
    // margin-bottom: 180px;
  }
  .header {
    position: fixed;
    left: 0;
    top: 20px;
    width: 100%;
    box-sizing: border-box;
    padding: 0 30px;

    .headerContent {
      height: 112px;
      line-height: 112px;
      width: 100%;
      padding: 0 20px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      background: $color-white;
      box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.1);
      border-radius: 12px;

      .patName {
        &::before {
          content: '';
          width: 10px;
          height: 10px;
          display: inline-block;
          position: relative;
          top: -5px;
          border-radius: 50%;
          background-color: $color-primary;
          margin-right: 15px;
        }

        color: $color-title;
      }

      .document {
        display: flex;
        justify-content: space-between;
        word-break: keep-all;
        color: $color-primary;
        margin-right: 10px;
        .divide {
          color: $color-border;
          margin: 0 20px;
        }
      }
    }
  }
  .endChatBtn {
    width: 150px;
    height: 80px;
    font-size: 28px;
    color: #3eceb6;
    padding: 0 20px;
    background: #fff;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
    text-align: center;
    border-radius: 12px;
    z-index: 999;
    position: fixed;
    right: 20px;
    top:10px;
    line-height: 80px;
  }
  .operationBox {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 9;
    background: $color-bg;
    .videoMinBox{
      position: absolute;
      top: -150px;
      right: 20px;
      height: 120px;
      width: 100px;
      background:rgba(255,255,255,1);
      box-shadow:0px 2px 12px 0px rgba(0,0,0,0.06);
      border-radius:6px;
      color: #3eceb6;
    }
    .top {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 16px 20px 16px 30px;
      border-bottom: 1px solid #d8d8d8;
      border-top: 1px solid #d8d8d8;
      position: relative;
      .input {
        position: relative;
        flex: 1;
        height: 100%;
        min-height: 40px;
        max-height: 120px;
        border: 1px solid #d8d8d8;
        font-size: 30px;
        color: #505050;
        padding: 10px 20px;
        background: #fff;
        border-radius: 4px;
        white-space: pre-wrap;
      }
      image {
        width: 60px;
        height: 60px;
        margin-left: 30px;
      }
    }
    .bottom {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      height: 419px;
      box-sizing: border-box;
      padding: 27px 70px 0;
      > view,
      navigator {
        width: 110px;
        margin-bottom: 30px;
        font-size: 26px;
        &:not(:nth-child(4n + 0)) {
          margin-right: 56px;
        }
        image {
          width: 110px;
          height: 110px;
        }
        > view {
          margin-top: 10px;
          text-align: center;
        }
      }
    }
  }
  .content {
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    padding: 16px 20px;
    > view,
    > navigator {
      margin-bottom: 30px;
      &.msg {
        background: #d7d9db;
        border-radius: 15px;
        padding: 10px 30px;
        font-size: 26px;
        color: #fff;
        text-align: center;
      }
      &.date {
        margin-bottom: 20px;
        text-align: center;
        font-size: 28px;
        color: #b2b2b2;
      }
    }
    .left,
    .right {
      // display: flex;
      align-items: flex-start;
      .img {
        min-width: 90px;
        width: 90px;
        height: 90px;
        border-radius: 50%;
        margin-right: 40px;
        overflow: hidden;
        background: #3eceb6;
        text-align: center;
        font-size: 30px;
        line-height: 90px;
        color: #fff;
      }
      .img image,
      .img > open-data {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
      .text {
        position: relative;
        padding: 20px 28px;
        background: #fff;
        border-radius: 10px;
        font-size: 30px;
        max-width: 480px;
        box-sizing: border-box;
        color: $color-title;
        white-space: normal;
        word-break: break-all;
        word-wrap: break-word;
        .linkBox {
          display: flex;
          image {
            width: 48px;
            height: 48px;
            margin-right: 15px;
          }
          > view {
            flex: 1;
            > view:last-child {
              font-size: 26px;
              overflow: hidden;
              display: -webkit-box;
              text-overflow: ellipsis;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
            }
          }
        }
      }
      .image {
        position: relative;
        border-radius: 10px;
        font-size: 30px;
        color: #505050;
        width: 210px;
        height: 218px;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .tip {
        position: absolute;
        top: -70px;
        right: 0;
        height: 60px;
        min-width: 292px;
        padding: 10px 0;
        box-sizing: border-box;
        background: #505050;
        border-radius: 8px;
        font-size: 28px;
        color: #fff;
        &.no {
          min-width: 196px;
          &.nocopy {
            min-width: 100px;
          }
        }
        text {
          display: inline-block;
          line-height: 40px;
          padding: 0 20px;
          &:not(:last-child) {
            border-right: 2px solid #fff;
          }
        }
        i {
          border: 20px solid transparent;
          border-top-color: #505050;
          position: absolute;
          top: 60px;
          right: 30px;
        }
      }
      .flex {
        flex: 1;
      }
    }
    .left {
      .text {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        i {
          border: 20px solid transparent;
          border-right-color: #fff;
          position: absolute;
          top: 20px;
          left: -35px;
        }
      }
    }
    .right {
      .img {
        margin-left: 40px;
        margin-right: 0;
      }
      .text {
        background: #98E165;
        color: #fff;
        box-shadow: 0 2px 12px 0 rgba(62, 206, 182, 0.5);
        > i {
          border: 20px solid transparent;
          border-left-color: #98E165;
          position: absolute;
          top: 20px;
          right: -35px;
        }
      }
    }
  }
  .listView {
    transform: rotate(180deg);
    .content {
      transform: rotate(-180deg);
    }
  }
  .modal {
    .modalBody {
      width: 82%;
      .modal-content{
        text-align: left;
        textarea{
          width: 100%;
          height: 150px;
        }
      }
    }
    .msgItem {
      margin-bottom: 30px;
      font-size: 34px;
      color: $color-title;
      display: flex;
      align-items: center;
      > view {
        flex: 1;
        text-align: left;
      }
      image {
        width: 140px;
      }
    }
    .tip {
      font-size: 28px;
      color: $color-text;
      text-align: left;
      margin-bottom: 10px;
    }
    input {
      height: 72px;
      line-height: 72px;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      margin-bottom: 30px;
      text-align: left;
      padding: 0 30px;
    }
  }
  .expressionBlock {
    max-height: 379px;
    background: #f5f5f5;
    position: relative;
    overflow-y: auto;
    padding: 20px 10px;
    text-align: left;

    .expressionItem {
      display: inline-flex;
      justify-content: center;
      // align-items: center;
      width: 80px;
      height: 80px;
      padding: 12px;

      image {
        width: 60px;
        // max-width: 80px;
        max-height: 60px;
        transform-origin: 0 100%;
        // height: 80px;
      }
    }
  }
}
