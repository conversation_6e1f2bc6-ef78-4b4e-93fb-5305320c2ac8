import Taro, { Component } from "@tarojs/taro";
import { View, Image } from "@tarojs/components";
import s from "./index.module.scss";
import * as Api from "./api";

const resultConfig = {
  '01': '未获卵',
  '02': '未移植',
  '03': '生化妊娠',
  '04': '未妊娠',
  '05': '妊娠',
  '99': '待定'
}

export default class Index extends Component {
  config = {
    backgroundTextStyle: "light",
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "健康档案",
    navigationBarTextStyle: "black",
  };

  constructor(props) {
    super(props);
    this.state = {
      status: true,
      manInfo: { sex: "M" },
      femalInfo: { sex: "F" },
      description: "", //病情描述，在manInfo里面
      diseaseF: [], //女方疾病
      diseaseM: [], //男方疾病
      showHistoryF: [],
      showHistoryM: [],
      record: {},
    };
  }

  componentDidMount() {
    const { patientPid: pid } = this.props;
    this.getPatientSubscrip(pid);
    this.hasReferral(pid);
  }

  getPatientSubscrip = async (pid) => {
    const param = {
      pid: pid,
    };
    const { code, data } = await Api.getPatientSubscrip(param);
    if (code === 0) {
      this.setState({ record: data });
    }
  };

  hasReferral = async (pid) => {
    const param = {
      pid: pid,
    };
    const { code, data } = await Api.hasReferral(param);
    if (code === 0) {
      const { hasReferral = '' } = data;
      this.setState({ hasReferral: hasReferral });
    }
  };

  NavTo = (url) => {
    console.log("11");
    Taro.navigateTo({ url: url });
  };

  render() {
    const { record = {} } = this.state;
    const { patientPid: pid } = this.props;
    return (
      <View className={`${s.container}`}>
        {Number(record.isSign) === 1 ? (
          <View className={s.cardBox}>
            <View className={s.cardItem}>
              <View className={s.title}>签约状态:</View>
              <View className={s.content}>已签约</View>
            </View>
            <View className={s.cardItem}>
              <View className={s.title}>签约医生:</View>
              <View className={s.content}>{record.qyys}</View>
            </View>
            <View className={s.cardItem}>
              <View className={s.title}>助孕节点:</View>
              <View className={s.content}>
                <View>{record.qysj}    签约</View>
                {record.jzsj && <View>{record.jzsj}    进周</View>  }
              </View>
            </View>
            <View className={s.cardItem}>
              <View className={s.title}>助孕结果:</View>
              <View className={s.content}>{resultConfig[record.zyjg] || '暂无'}</View>
            </View>
          </View>
        ) : (
          <View className={`${s.imgBox}`}>
            <Image
              src='https://static.med.gzhc365.com/miniprogram-static/fe-his-wxapp/p2043/hc-baby-unauth.png'
              mode='aspectFill'
              width={180}
              height={180}
            />
            <View className={`${s.sign}`}>暂未签约</View>
          </View>
        )}

        {
          Number(record.isSign) !== 1 &&
          <View className={`${s.btnBox}`}>
            {
              hasReferral !== '1' &&
              <View
                className={`${s.submit}`}
                onClick={() =>
                  this.NavTo(
                    `/pages/patient/referral/unreferral/index?patientPid=${this.props.patientPid}`
                  )
                }
              >
                申请转诊
              </View>
            }
            <View
              className={`${s.submit}`}
              onClick={() =>
                this.NavTo(
                  `/pages/patient/bill/unbill/index?patientPid=${this.props.patientPid}`
                )
              }
            >
              申请会诊
            </View>
            <View
              className={`${s.submit}`}
              onClick={() =>
                this.NavTo(
                  `/pages/patient/bill/makebill/index?patientPid=${this.props.patientPid}`
                )
              }
            >
              申请开单
            </View>
            {/* <View
              className={`${s.submit}`}
              onClick={() =>
                this.NavTo(
                  `/pages/patient/health-record/visiting-record/consultation-application/index?patientPid=${this.props.patientPid}`
                )
              }
            >
              申请会诊
            </View> */}
          </View>
        }
      </View>
    );
  }
}
