import Taro, { Component } from '@tarojs/taro';
import { View, Text, Input, RadioGroup, Radio, Label, Block } from '@tarojs/components';

import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  renderInput = (item) => {
    const { onChange = () => {}, onFocus = () => {} } = this.props;
    const { value, error, maxLength, name, label, onChange: tChange, onFocus: tFocus } = item;
    return (
      <View className={styles.bindcardListitem}>
        <View className={styles.listitemHead}>
          <Text className={styles.listTitle}>{label}</Text>
        </View>
        <View className={styles.listitemBody}>
          <Input
            className={`${styles.content} ${error ? styles.error : ''}`} placeholder={`请输入${label}`}
            maxLength={maxLength} id={name} value={value}
            onInput={tChange || onChange} onFocus={tFocus || onFocus}
          />
        </View>
      </View>
    );
  }

  renderRadio = (item) => {
    const { onChange = () => {} } = this.props;
    const { label, options, onChange: tChange } = item;
    return (
      <View className={styles.bindcardListitem}>
        <View className={styles.listitemHead}>
          <Text className={styles.listTitle}>{label}</Text>
        </View>
        <View className={styles.listitemBody}>
          <View className={styles.content}>
            <RadioGroup onChange={tChange || onChange}>
              {
                options.map((opt) => {
                  return (
                    <Label className={styles.binduserRadio} key={opt.key}>
                      <Radio value={opt.key} className={styles.binduserRadio_object} color={$PRIMARY_COLOR} />
                      <Text className={styles.binduserRadio_text}>{opt.value}</Text>
                    </Label>
                  );
                })
              }
            </RadioGroup>
          </View>
        </View>
      </View>
    );
  }

  renderMsCode = (item) => {
    const { onChange = () => {}, onFocus = () => {} } = this.props;
    const { value, error, maxLength, name, label, onChange: tChange, onFocus: tFocus } = item;
    return (
      <View className={styles.bindcardListitem}>
        <View className={styles.listitemBody}>
          <Input
            className={`${styles.content} ${error ? styles.error : ''}`} placeholder={`请输入${label}`}
            maxLength={maxLength} id={name} value={value}
            onInput={tChange || onChange} onFocus={tFocus || onFocus}
          />
        </View>
        <View className={styles.listitemHead}>
          <Text className={styles.listTitle}>{label}</Text>
        </View>
      </View>
    );
  }

  render () {
    const { list = [] } = this.props;
    return (
      <View className={styles.bindcardList}>
        {
          list.map((item) => {
            return (
              <Block key={item.name}>
                {
                  item.renderType === 'Input' ? this.renderInput(item) : null
                }
                {
                  item.renderType === 'Radio' ? this.renderRadio(item) : null
                }
              </Block>
            );
          })
        }
      </View>
    )
  }
}