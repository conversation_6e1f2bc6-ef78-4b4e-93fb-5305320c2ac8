.tab{
  background-color: #fff;
  height: 100px;
  display: flex;
  width: 100%;
  overflow-y: hidden;
  overflow-x: auto;

  .tabLi {
    z-index: 1;
    display: block;
    flex: auto;
    font-size: 32px;
    text-align: center;
    
    .liText{
      display: inline-block;
      color: $color-title;
      height: 100px;
      line-height: 100px;
      word-break: keep-all;
      white-space: nowrap;
      overflow: hidden;
      user-select: none;
      position: relative;
      padding: 0 20px;
      &:after {
        content: '';
        position: absolute;
        left: .5em;
        right: .5em;
        bottom: 0;
        display: block;
        height: 6px;
        background-color: $color-primary;
        transform: scaleX(0);
        transition: all ease-out 0.2s 0.1s;
      }
    }
    &.active {
      .liText{
        color: $color-primary;
        font-weight: 600;
        &:after {
          transform: scale(1);
        }
      }
    }
  }
}
