import Taro, { Component } from '@tarojs/taro';
import { View, Block, Image } from '@tarojs/components';
import Empty from '../empty/index';

import './index.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    const { defaultIndex = -1 } = this.props;
    this.state = {
      tagopen: false,
      activeIdx: defaultIndex,
    };
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  bindChangeIndex = (activeIdx) => {
    this.setState({ activeIdx });
  }

  bindTapDoctor = (item) => {
    const { onDoctorClick = () => {} } = this.props;
    onDoctorClick(item);
  }

  bindTapDept = (item) => {
    const { onDeptClick = () => {} } = this.props;
    onDeptClick(item);
  }

  tagOpen = () => {
    this.setState({ tagopen: !this.state.tagopen });
  }

  render () {
    const {
      deptList = {}, favoriteList = [],
      hisDoctorList = [], showHistory
    } = this.props;
    const { tagopen, activeIdx } = this.state;

    const deptListArr = deptList.deptList || [];
    
    const hasData = deptListArr.length + favoriteList.length + hisDoctorList.length;
    if (hasData.length === 0) {
      return <Empty text='暂未查询到科室数据' />;
    }

    return (
      <Block>
        {
          (deptList.levelDept == 1 && (favoriteList.length + hisDoctorList.length) > 0) ?
            <View className={`m-suggest ${tagopen ? 'tagopen' : ''}`}>
              <View className='suggest-title' onClick={this.tagOpen}>历史记录</View>
              <View className='m-tags'>
                {
                  hisDoctorList.map((item) => {
                    return (
                      <View
                        className='tag-item'
                        key={item.doctorId}
                        onClick={() => this.bindTapDoctor(item)}
                      >{item.doctorName}</View>
                    );
                  })
                }
                {
                  favoriteList.map((item) => {
                    return (
                      <View
                        className='tag-item'
                        key={item.doctorId}
                        onClick={() => this.bindTapDoctor(item)}
                      >{item.doctorName}</View>
                    );
                  })
                }
              </View>
            </View> : null
        }
        <View className='m-list'>
          <View className='list-box'>
            <View className='list-lt-box'>
              <View className='list-lt'>
                {
                  (hisDoctorList.length > 0 || favoriteList.length > 0) ?
                    <View
                      className={`lt-item ${activeIdx == -1 ? 'active' : ''}`}
                      onClick={() => this.bindChangeIndex(-1)}
                    >历史挂号
                    </View> : null
                }
                {
                  deptList.levelDept > 1 ?
                    (deptList.deptList || []).map((item, index) => {
                      return (
                        <View
                          className={`lt-item ${activeIdx === index ? 'active' : ''}`}
                          onClick={() => this.bindChangeIndex(index)}
                          key={item.deptId}
                        >{item.deptName}</View>
                      );
                    }) : null
                }
              </View>
            </View>
            <View className='list-rt-box'>
              <View className='list-rt'>
                {
                  (showHistory && deptList.levelDept > 1) ?
                    <View className={`rt-history-box ${activeIdx == -1 ? 'active' : ''}`}>
                      {
                        hisDoctorList.length > 0 ?
                          <View className='rt-history'>
                            <View className='his-tit'>挂过的医生</View>
                            {
                              hisDoctorList.map((item) => {
                                return (
                                  <View
                                    className='his-item'
                                    onClick={() => this.bindTapDoctor(item)}
                                    key={item.doctorId}
                                  >
                                    <View className='item-hd'>
                                      <Image
                                        mode='widthFix'
                                        src={item.doctorImg || `${$CDN_DOMAIN}/doc-header-man.png`}
                                        alt=''
                                      />
                                    </View>
                                    <View className='item-bd'>
                                      <View className='unit-color-title'>{item.doctorName}</View>
                                      <View className='unit-color-text'>{item.deptName}</View>
                                    </View>
                                    <View className='unit-arrow' />
                                  </View>
                                );
                              })
                            }
                          </View> : null
                      }
                      {
                        favoriteList.length > 0 ?
                          <View className='rt-history'>
                            <View className='his-tit'>收藏的医生</View>
                            {
                              favoriteList.map((item) => {
                                return (
                                  <View
                                    className='his-item'
                                    onClick={() => this.bindTapDoctor(item)}
                                    key={item.doctorId}
                                  >
                                    <View className='item-hd'>
                                      <Image
                                        mode='widthFix'
                                        src={item.doctorImg || `${$CDN_DOMAIN}/doc-header-man.png`}
                                        alt=''
                                      />
                                    </View>
                                    <View className='item-bd'>
                                      <View className='unit-color-title'>{item.doctorName}</View>
                                      <View className='unit-color-text'>{item.deptName}</View>
                                    </View>
                                    <View className='unit-arrow' />
                                  </View>
                                );
                              })
                            }
                          </View> : null
                      }
                    </View> : null
                }
                {
                  deptList.levelDept == 1 ?
                    <View className='rt-sec active'>
                      {
                        (deptList.deptList || []).map((item) => {
                          return (
                            <View
                              className='sec-li'
                              onClick={() => this.bindTapDept(item)}
                              key={item.deptId}
                            >
                              <View className='sec-li-wrap'>
                                <View className='sec-bd'>{item.deptName}</View>
                                <View className='unit-arrow' />
                              </View>
                            </View>
                          );
                        })
                      }
                    </View> : null
                }
                {
                  (deptList.levelDept > 1 && activeIdx !== -1) ?
                    <View className='rt-sec active'>
                      {
                        deptList.deptList[activeIdx].hasChild == 1 ?
                          <View
                            className='sec-li'
                            onClick={() => this.bindTapDept(deptList.deptList[activeIdx])}
                          >
                            4444
                            <View className='sec-li-wrap'>
                              <View className='sec-bd'>{deptList.deptList[activeIdx].deptName}</View>
                              <View className='unit-arrow' />
                            </View>
                          </View> : null
                      }
                      {
                        // deptList.deptList[activeIdx].hasChild == 0 ?
                        //   (deptList.deptList[activeIdx].deptList || []).map((item) => {
                        //     return (
                        //       <View
                        //         className='sec-li'
                        //         onClick={() => this.bindTapDept(item)}
                        //         key={item.deptId}
                        //       >
                        //         33333
                        //         {
                        //           item.hasChild == 1 ?
                        //             <View className='sec-li-wrap'>
                        //               <View className='sec-bd'>{item.deptName}</View>
                        //               <View className='unit-arrow'></View>
                        //             </View> : null
                        //         }
                        //       </View>
                        //     );
                        //   }) : null
                      }
                      {
                        deptList.deptList[activeIdx].hasChild == 0 ?
                          (deptList.deptList[activeIdx].deptList || []).map((item) => {
                            return (
                              <Block key={item.deptId}>
                                <View
                                  className='sec-li'
                                  onClick={() => this.bindTapDept(item)}
                                >
                                  {
                                    item.hasChild == 1 ?
                                      <View className='sec-li-wrap'>
                                        <View className='sec-bd'>{item.deptName}</View>
                                        <View className='unit-arrow'></View>
                                      </View> : null
                                  }
                                </View>
                                {
                                  item.hasChild == 0 ?
                                    <View className='sec-li active'>
                                      <View className='sec-li-wrap'>
                                        <View className='sec-bd'>{item.deptName}</View>
                                        <View className='trd-arrow' />
                                      </View>
                                      <View className='trd-box'>
                                        {
                                          item.deptList.map((itm) => {
                                            return (
                                              <View
                                                className='trd-li'
                                                onClick={() => this.bindTapDept(itm)}
                                                key={itm.deptId}
                                              >
                                                <View className='trd-bd'>{itm.deptName}</View>
                                                <View className='unit-arrow' />
                                              </View>
                                            );
                                          })
                                        }
                                      </View>
                                    </View> : null
                                }
                              </Block>
                            );
                          }) : null
                      }
                    </View> : null
                }
              </View>
            </View>
          </View>
        </View>
      </Block>
    )
  }
}
