const path = require('path');
// const ReplacePulgin = require(`${process.cwd()}/node_modules/webpack-plugin-replace`);

const run_env = process.env.RUN_ENV;
// const projectName = process.env.npm_config_dir;

const config = {
  projectName: 'h242',
  date: '2020-02-20',
  designWidth: 750,
  deviceRatio: {
    '640': 2.34 / 2,
    '750': 1,
    '828': 1.81 / 2
  },
  sourceRoot: `src`,
  outputRoot: `dist`,
  babel: {
    sourceMap: true,
    presets: [
      ['env', {
        modules: false,
      }]
    ],
    plugins: [
      'transform-decorators-legacy',
      'transform-class-properties',
      'transform-object-rest-spread',
      ['transform-runtime', {
        "helpers": false,
        "polyfill": false,
        "regenerator": true,
        "moduleName": 'babel-runtime'
      }]
    ]
  },
  plugins: [
    '@tarojs/plugin-sass',
  ],
  defineConstants: {
    $RUN_ENV: `"${run_env}"`,
    $IPX_BOTTOM_HEIGHT: '"16"',
    $PRIMARY_COLOR: '"#30A1A6"',
    $HIS_NAME: '"湖南旺旺医院"',
  },
  alias: {
    '@': path.resolve(__dirname, '..', ''),
  },
  sass: {
    resource: [
      './static/style/mixin.scss',
      './static/style/flex.scss',
    ],
    projectDirectory: path.resolve(__dirname, '..', ''),
  },
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {}
      },
      url: {
        enable: true,
        config: {
          limit: 10240 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    compile: {
      exclude: [
        path.resolve(__dirname, '..', 'static/image')
      ]
    },
    webpackChain(chain, webpack) {
      // chain.module
        // .rule('replace')
        // .enforce('pre')
        // .test(/\.jsx$/)
        // .use('0')
        // // .before('0')
        // .loader('js-conditional-compile-loader').options({ isDebug: false })

        // const modulesConfig = {
        //   HEALTHCARD: false
        // }

        // chain
        //   .plugin('normalModule')
        //   .before('definePlugin')
        //   .use(webpack.NormalModuleReplacementPlugin, [/(.*)\$[a-zA-Z]+?\$(\.*)/, (resource) => {
        //     let sourcePath = resource.request;
        //     const matchArr = sourcePath.match(/\$[a-zA-Z]+?\$/g);
        //     if (matchArr && matchArr.length) {
        //       matchArr.forEach((item) => {
        //         const tkey = item.replace('$', '');
        //         if (!modulesConfig[tkey]) {
        //           sourcePath = sourcePath.replace(item, '');
        //         } else {
        //           sourcePath = sourcePath.replace(item, tkey);
        //         }
        //       });
        //     }
        //     resource.request = sourcePath;
        //   }]);
    }
  },
  h5: {
    publicPath: `/`,
    staticDirectory: `./src/static`,
    esnextModules: ['taro-ui'],
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
          browsers: [
            'last 3 versions',
            'Android >= 4.1',
            'ios >= 8'
          ]
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    devServer: {
      proxy: {
        '/api': {
          target: 'https://wechatdev.jiahuiyiyuan.com',
          secure: false,
          changeOrigin: true
        }
      }
    }
  }
}

module.exports = function (merge) {
  if (!run_env) {
    return config;
  }
  return merge({}, config, require(`./${run_env}`))
}
