import Taro, { Component } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';
import * as Api from './api';
import styles from './index.module.scss';

export default class Index extends Component {
  
  constructor(props) {
    super(props);
    this.state = {
      patient: {
        cardList: [],
        activePatient: {},
      },
      show: false
    }
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () {
    this.getCardList(false);
  }

  componentDidHide () { }

  getCardList = async (flag) => { // flag 是否绑卡后再次刷新卡列表
    this.setState({ patient: { cardList: [], activePatient: {} } });
    const { data = {}, code } = await Api.getPatientList();
    if (code != 0) {
      return false;
    }
    const { list = [] } = data;
    if (!list.length) {
      if (!flag) {
        await this.outPatientToInPatient();
        this.getCardList(true);
      }
    } else {
      const activePatient = list.find(item => item.isDefault === 1) || {};
      let extra = {};
      if ((activePatient.admissionWard || '').indexOf('{') > -1) {
        extra = JSON.parse(activePatient.admissionWard);
        activePatient.admissionWard = '';
      }
      const patient = { ...data, activePatient, ...extra };
      this.setState({
        patient
      });
      
      const { onCardClick = () => {} } = this.props;
      onCardClick(activePatient);
    }
  }

  outPatientToInPatient = async () => {
    const { data = {}, code } = await Api.getOutPatientList();
    if (code == 0) {
      const { cardList = [] } = data;
      const posList = [];
      cardList.forEach((item) => {
        posList.push(
          new Promise(async (resove) => {
            await Api.bindCard({ patCardNo: item.patCardNo });
            resove();
          })
        );
      });
      await Promise.all(posList);
    }
    return true;
  }

  getOutPatientList = async () => {
    const { data = {} } = await Api.getOutPatientList();
    
    const { list = [] } = data;

    const activePatient = list.find(item => item.isDefault === 1) || {};
    const patient = { ...data, activePatient };
    this.setState({
      patient
    });
  }

  toggleShow = () => {
    const { show } = this.state;
    this.setState({ show: !show })
  }

  localChangeUser = async (item) => {
    const { patient: { activePatient = {} } } = this.state;
    if (item.patientId === activePatient.patientId) {
      return false;
    }
    const { code } = await Api.setDefault(item);
    if (code !== 0) {
      return false;
    }
    this.getCardList();
  }

  navTo = (url) => {
    Taro.navigateTo({ url });
  }

  render () {
    const { patient, show } = this.state;

    const { list = [] } = patient;
    if (!list.length) {
      return (
        <View className={styles.noUserBox}>
          <View className={styles.noUser}>
            <View className={styles.noUserBtn} onClick={() => this.navTo('/pages/usercenter/bindscan/index?from=inpatient')}>+添加住院人</View>
          </View>
        </View>
      );
    }

    return (
      <View className={styles.wgtUserBox}>
        <View className={styles.wgtUserMain}>
          <View className={styles.wgtUserMainInfo}>
            <View className={styles.wgtUserMainInfoTxt}>{patient.activePatient.inpatientName}</View>
            <View className={styles.wgtUserMainInfoLabel} />
          </View>
          <View className={styles.wgtUserMainBtn} onClick={() => this.toggleShow()}>切换住院人</View>
        </View>
        <View className={styles.wgtUserExtra}>
          住院号：{patient.activePatient.admissionNum || patient.activePatient.patCardNo}
        </View>
        <View className={`${styles.wgtUserPopBox} ${show ? styles.active : ''}`}>
          <View className={styles.wgtUserPopMask} onClick={() => this.toggleShow()} />
          <View className={styles.wgtUserPop}>
            <View className={styles.wgtUserPopTitle}>切换住院人</View>
            <View className={styles.wgtUserPopList}>
              {
                list.map(item => {
                  return (
                    <View className={styles.wgtUserPopListItem} key={item.patientId} onClick={() => this.localChangeUser(item)}>
                      <View className={styles.wgtUserPopListItemMain}>
                        <View className={styles.wgtUserPopListItemName}>{item.inpatientName}</View>
                        <View className={styles.wgtUserPopListItemLabel} />
                      </View>
                      <View className={styles.wgtUserPopListItemNum}>住院号：{item.admissionNum || item.patCardNo}</View>
                      <View
                        className={`${styles.wgtUserPopListItemIpt} ${item.inpatientId === patient.activePatient.inpatientId ? styles.active : ''}`}
                      >
                        <Image className={styles.image} src={`${$CDN_DOMAIN}/select-single.png`}></Image>
                      </View>
                    </View>
                  )
                })
              }
            </View>
            <View className={styles.wgtUserPopOpt}>
              {
                patient.leftAppendNum > 0 ?
                  <View
                    className={styles.wgtUserPopOptItem}
                    onClick={() => this.navTo('/pages/usercenter/bindscan/index?from=inpatient')}
                  >
                    添加住院人
                  </View> : null
              }
              <View
                className={styles.wgtUserPopOptItem}
                onClick={() => this.navTo('/pages/inpatient/userlist/index')}
              >
                管理住院人
              </View>
            </View>
            <View className={styles.wgtUserPopClose} onClick={() => this.toggleShow()}></View>
          </View>
        </View>
      </View>
    )
  }
}
