import Taro, { Component } from '@tarojs/taro';
import { View, RichText, Image } from '@tarojs/components';

import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
  }

  componentWillMount () {}

  componentDidMount () {}

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  render () {
    const { message } = this.props;
    return (
      <View className={styles.messageOpertip}>
        <View className={styles.messageMsg}>
          <RichText nodes={message} />
        </View>
        {/* <View className={styles.messageImgbox}>
          <Image className={styles.messageImg} mode='widthFix' src={`${$CDN_DOMAIN}/hc-baby-tip.png`} />
        </View> */}
      </View>
    )
  }
}
