.page {
  background: #fff;
  height: 100vh;
}
.header {
  padding: 24px;
  background-color: #f2f4f4;
  &_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgba(0, 0, 0, 0.9);
    font-size: 28px;
    font-weight: 600;
    line-height: 42px; /* 150% */
    &__icon {
      width: 40px;
      height: 40px;
    }
  }
  &_camera {
    margin-top: 24px;
    width: 100%;
    height: 220px;
    border-radius: 11px;
    border: 1px solid #308b91;
  }
}

.field_wrapper {
  padding: 16px 30px;
  background: #fff;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    left: 30px;
    right: 0;
    bottom: 0;
    height: 1px;
    background-color: #eee;
  }
}

.small_camera_wrapper {
  position: fixed !important; // 添加important确保优先级
  top: 30% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 80%;
  height: 400px; // 保持与下方一致
  border-radius: 10px;
  overflow: hidden;
  z-index: 999;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  background: #fff;


  .small_camera {
    width: 100%;
    height: 100%;
  }

  .small_camera_mask {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: rgba(0, 0, 0, 0.7);
    text-align: center;

    .close_text {
      color: #fff;
      font-size: 28px;
      padding: 20px;
      display: inline-block;
    }
  }
}

.form_item {
  padding: 0 30px;
  background: #fff;
  display: flex;
  align-items: center;
  height: 100px;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    left: 30px;
    right: 0;
    bottom: 0;
    height: 1px;
    background-color: #eee;
  }

  .form_label {
    width: 200px;
    font-size: 32px;
    color: #000000;
  }

  .input_box {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
  }

  .form_input {
    flex: 1;
    height: 100%;
    padding-right: 70px;
    padding-left: 23px;
    font-size: 32px;
    color: #333;

    &::placeholder {
      color: #999;
      font-size: 28px;
    }
  }

  .scan_icon {
    width: 40px;
    height: 40px;
    position: absolute;
    right: 10px;
  }
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.small_camera {
  width: 100%;
  height: 100%;
}

.body {
  padding: 32px 24px;
  border-radius: 12px 12px 0px 0px;
  background: #fff;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.08);

  .search {
    display: flex;
    align-items: center;
    &_input {
      flex: 1 1 auto;
      padding: 0 32px;
      border-radius: 8px;
      border: 1px solid #30a1a6;
      display: flex;
      align-items: center;
      height: 72px;
      &__icon {
        width: 32px;
        height: 32px;
        flex: 0 0 32px;
      }
      &__inner {
        flex: 1 1 auto;
        margin-left: 16px;
        color: #000;
        font-size: 28px;
        font-weight: 400;
        line-height: 48px; /* 171.429% */
      }
    }
    &_button {
      flex: 0 0 auto;
      margin-left: 16px;
      height: 72px;
      padding: 0px 16px;
      border-radius: 8px;
      background: #308b91;
      color: #fff;
      text-align: center;
      font-size: 32px;
      font-style: normal;
      font-weight: 500;
      line-height: 72px; /* 150% */
    }
  }

  .header2 {
    display: flex;
    align-items: center;
    &_icon,
    &_delete {
      width: 36px;
      height: 36px;
    }
    &_title {
      margin: 0 12px;
      flex: 1;
      color: rgba(0, 0, 0, 0.9);
      font-size: 32px;
      font-weight: 600;
      line-height: 48px; /* 150% */
    }
  }

  .title {
    margin-top: 24px;
    color: rgba(0, 0, 0, 0.9);
    font-size: 28px;
    font-weight: 600;
    line-height: 42px; /* 150% */
  }
}

.footer {
  padding: 64px 24px;
  display: flex;
  gap: 36px;
  .button {
    flex: 1 1 auto;
    height: 96rpx;
    line-height: 96rpx;
    font-size: 36px;
    border-radius: 76rpx;
    &::after {
      border: none;
    }
    &.submit {
      background: $color-primary;
      color: #fff;
      &.disabled {
        opacity: 0.6;
        background-color: $color-primary !important;
        color: #fff !important;
      }
    }
    &.cancel {
      background: rgba(0, 0, 0, 0.04);
      color: #000;
    }
  }
}
