import Taro, { Component } from "@tarojs/taro";
import {
  Text,
  Textarea,
  Input,
  View,
  Image,
  Switch,
  RadioGroup,
  Label,
  Radio,
  Checkbox,
} from "@tarojs/components";
import s from "./index.module.scss";
import * as Api from "./api";

export default class Index extends Component {
  config = {
    backgroundTextStyle: "light",
    navigationBarBackgroundColor: "#fff",
    navigationBarTitleText: "转诊申请",
    navigationBarTextStyle: "black",
  };

  constructor(props) {
    super(props);
    this.state = {
      list: [
        {
          value: 1,
          text: '有',
          checked: false
        },
        {
          value: 0,
          text: '无',
          checked: true
        }
      ],
      doctorId: "",
      patientName: "",
      relationPatientId: "",
      fileList: [],

      isTubeHistory: 0,
      fileUrlJsonArray: {
        medicalHistoryFile: [],
        graphyFemaleFile: [],
        pelvicOperationHistoryFile: [],
        spermMaleFile: [],
        hereditaryDiseasesFile: [],
        otherFile: [],
      }, // 所有图片集合
      infectiousDiseasesList: [
        {
          value: "乙肝",
          text: "乙肝",
          checked: false,
        },
        {
          value: "丙肝",
          text: "丙肝",
          checked: false,
        },
        {
          value: "艾滋",
          text: "艾滋",
          checked: false,
        },
        {
          value: "梅毒",
          text: "梅毒",
          checked: false,
        },
        {
          value: "结核",
          text: "结核",
          checked: false,
        },
        {
          value: "其他",
          text: "其他",
          checked: false,
        },
      ],
      diagnosisFemaleList: [
        {
          value: "原发不孕",
          text: "原发不孕",
          checked: false,
        },
        {
          value: "继发不孕",
          text: "继发不孕",
          checked: false,
        },
        {
          value: "多囊卵巢",
          text: "多囊卵巢",
          checked: false,
        },
        {
          value: "反复流产",
          text: "反复流产",
          checked: false,
        },
        {
          value: "不良孕史",
          text: "不良孕史",
          checked: false,
        },
        {
          value: "其他",
          text: "其他",
          checked: false,
        },
      ],
      diagnosisMaleList: [
        {
          value: "原发不育",
          text: "原发不育",
          checked: false,
        },
        {
          value: "继发不育",
          text: "继发不育",
          checked: false,
        },
        {
          value: "少弱精症",
          text: "少弱精症",
          checked: false,
        },
        {
          value: "无精症",
          text: "无精症",
          checked: false,
        },
      ],
    };
  }

  componentDidMount() {
    const { userId: doctorId = "" } = JSON.parse(
      Taro.getStorageSync("userInfo") || "{}"
    );
    this.setState({ doctorId });
    this.getPatientInfoByPid();
    // this.getFileTypeList();
  }

  getPatientInfoByPid = async () => {
    const { patientPid: pid } = this.$router.params;
    const { code, data } = await Api.getPatientInfoByPid({ pid });
    if (code === 0) {
      const { patientName, id: relationPatientId } = data;
      this.setState({ patientName, relationPatientId });
    }
  };

  inputMainSuit = (keyValue, e) => {
    this.setState({
      [keyValue]: e.target.value.trim(),
    });
  };

  // previewImage = (url) => {
  //   const { fileList } = this.state;
  //   Taro.previewImage({
  //     urls: fileList,
  //     current: url,
  //   });
  // };

  addImage = async (keyValue) => {
    const { fileUrlJsonArray = {} } = this.state;
    const chooseRes = await Taro.chooseImage({
      count: 5 - fileUrlJsonArray[`${keyValue}File`].length,
      sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ["camera", "album"],
    });
    if (chooseRes.errMsg == "chooseImage:ok") {
      console.log('chooseRes', chooseRes);
      const tempFilePath = chooseRes.tempFilePaths || [];
      Taro.showLoading({ title: "上传中...", mask: true });
      console.log('tempFilePath', tempFilePath);
      let url = "";
      if (tempFilePath.length > 0) {
        const { patientPid } = this.$router.params;
        tempFilePath.map( async (item) =>{
          url = await Api.uploadImages(item, {
            pid: patientPid,
            grid: patientPid,
          });
          Taro.hideLoading();
          if (url) {
            console.log('11');
            const { fileUrlJsonArray = {} } = this.state;
            const item = { fileUrl: url };
            fileUrlJsonArray[`${keyValue}File`].push(item);
            
            this.setState({ fileUrlJsonArray });
          }
        })
      }
      
    }
  };

  deleteImage = (index, keyValue) => {
    console.log('index', index);
    console.log('keyValue', keyValue);
    let { fileUrlJsonArray } = this.state;
    console.log('fileUrlJsonArray', fileUrlJsonArray);

    fileUrlJsonArray[`${keyValue}`].splice(index, 1);
    this.setState({ fileUrlJsonArray });
  };

  attachFile = async (consultationId) => {
    const { fileList } = this.state;
    const requestArr = fileList.map((url) => {
      return Api.addFile({ consultationId, url });
    });
    return Promise.all(requestArr);
  };

  validateData() {
    const { fileUrlJsonArray = {} } = this.state;
    const requireList = [
      { key: "medicalHistory", value: "基本病史" },
      { key: "graphyFemale", value: "女方造影结果" },
      { key: "pelvicOperationHistory", value: "盆腔手术史" },
      { key: "hereditaryDiseases", value: "是否有遗传性疾病" },
    ];
    for (let item of requireList) {
      if (!this.state[item.key] && fileUrlJsonArray[`${item.key}File`].length <= 0) {
        Taro.showModal({
          title: "系统提示",
          icon: "error",
          content: `${item.value}不能为空`,
          showCancel: false,
        });
        return false;
      }
    }
    return true;
  }

  async submit() {
    if (!this.validateData()) return;
    const { doctorId, patientName, relationPatientId } = this.state;
    const {
      medicalHistory = "",
      leftFolliclesNum = "",
      rightFolliclesNum = "",
      amh = "",
      bmi = "",
      graphyFemale = "",
      pelvicOperationHistory = "",
      spermMale = "",
      ovulateCycle = "",
      isTubeHistory = "",
      hereditaryDiseases = "",
      fileUrlJsonArray = {},
      infectiousDiseasesList = [],
      diagnosisFemaleList = [],
      diagnosisMaleList = [],
    } = this.state;
    
    const { patientPid } = this.$router.params;
    let param = {
      relationPatientId,
      patientPid,
      patientName,
      doctorId,
      consultationType: "referral",
    };
    const { code, data } = await Api.createConsultation(param);
    if (code == 0) {
      const { orderNo, consultationId } = data;

      // await this.attachFile(consultationId);



      let infectiousDiseases = [];
      let diagnosisFemale = [];
      let diagnosisMale = [];
      diagnosisFemaleList.map((item) => {
        if (item.checked) {
          diagnosisFemale.push(item.value);
        }
      });
      diagnosisMaleList.map((item) => {
        if (item.checked) {
          diagnosisMale.push(item.value)
        }
      });
      infectiousDiseasesList.map((item) =>
        {if (item.checked) {
          infectiousDiseases.push(item.value)
        }}
      );

      if (diagnosisFemale.length === 0 && diagnosisMale.length === 0) {
        Taro.showModal({
          title: "系统提示",
          icon: "error",
          content: `男方和女方疾病诊断不能同时为空`,
          showCancel: false,
        });
        return false;
      }

      let applyParam = {
        orderNo,
        medicalHistory,
        leftFolliclesNum,
        rightFolliclesNum,
        amh,
        bmi,
        graphyFemale,
        pelvicOperationHistory,
        spermMale,
        ovulateCycle,
        isTubeHistory,
        infectiousDiseases,
        hereditaryDiseases,
        diagnosisFemale: diagnosisFemale && diagnosisFemale.join(",") || '',
        diagnosisMale: diagnosisMale && diagnosisMale.join(",") || '',
        fileUrlJsonArray: fileUrlJsonArray && JSON.stringify(fileUrlJsonArray) || '',
      };
      const { code: applyCode } = await Api.applyConsultation(applyParam);
      if (applyCode == 0) {
        Taro.showToast({
          title: "提交成功",
          icon: "success",
          duration: 1000,
        });
        setTimeout(function () {
          Taro.navigateTo({
            url: `/pages/patient/referral/referrecord/index?id=${consultationId}&isNew=1`,
          });
        }, 1000);
      }
    }
  }

  previewImage = (idx, keyValue) => {
    const { fileUrlJsonArray = {} } = this.state;
    const urls =
      fileUrlJsonArray[keyValue].length &&
      fileUrlJsonArray[keyValue].map((item) => item.fileUrl);
    console.log("urls", urls);
    Taro.previewImage({ current: urls[idx], urls });
  };

  checkboxChange = (e, idx, keyValue) => {
    const list = this.state[keyValue];
    list &&
      list.length > 0 &&
      list.map((item, index) => {
        if (idx === index) {
          item.checked = !item.checked;
        }
      });
    this.setState({
      [keyValue]: list,
    });
  };

  changeTubeHistory = (e) => {
    const isTubeHistory = e.detail.value;
    this.setState({ isTubeHistory });
  };

  getInput(item1 = {}) {
    const { tittle = "", keyValue = "", placeholder = "", value = "", fileList = [], required = '' } = item1;
    const { fileUrlJsonArray = {}} = this.state;
    return (
      <View className={s.inputModule}>
        <View className={`${s.between}`}>
          <View className={`${s.mainSuit}`}>{ required && <Text className={s.colorRed} style={{ color: "red" }}>*</Text>}{tittle}</View>
          {
            fileUrlJsonArray[`${fileList}`].length < 6 &&
            <View
              className={`${s.patientName} ${s.medicalImgs}`}
              onClick={this.addImage.bind(this, keyValue)}
            >
              添加照片
            </View>
          }
          
        </View>
        <Textarea
          value={value}
          onInput={this.inputMainSuit.bind(this, keyValue)}
          placeholder={placeholder}
          maxlength={100}
          className={`${s.textarea}`}
          autoHeight
        />
      </View>
    );
  }

  showPicList(fileList = [], keyValue) {
    return (
      <View className={s.fileContainer}>
        {fileList &&
          fileList.length > 0 &&
          fileList.map((item, index) => {
            return (
              <View key={item.fileUrl} className={s.fileContent}>
                <Image
                  mode="aspectFit"
                  src={item.fileUrl}
                  onClick={this.previewImage.bind(
                    this,
                    index,
                    keyValue
                  )}
                />
                <View
                  className={s.deleteBtn}
                  onClick={this.deleteImage.bind(this, index, keyValue)}
                >
                  ×
                </View>
              </View>
            );
          })}
      </View>
    );
  }

  getOnlyInput({ tittle, placeholder, keyValue, value, type }) {
    return (
      <View className={`${s.rowModule}`}>
        <View className={`${s.mainSuit}`}>{tittle}</View>
        <View className={s.patientName}>
          <Input
            type={ type || "text"}
            style={{ textAlign: "right" }}
            placeholder={placeholder}
            // focus
            onInput={this.inputMainSuit.bind(this, keyValue)}
          />
        </View>
      </View>
    );
  }

  render() {
    const {
      mainSuit,
      patientName,
      fileList,
      fileUrlJsonArray = {},
    } = this.state;
    return (
      <View className={`${s.container}`}>
        <View className={`${s.tip}`}>
          注：为便于平台医生能提前掌握患者病情，更有效的提供服务，请详细描述患者相关信息。提交申请后，平台会审核，审核结果将发送给您，请您及时留意。
        </View>
        <View className={`${s.rowModule}`}>
          <View className={`${s.mainSuit}`}>患者</View>
          <View className={s.patientName}>{patientName}</View>
        </View>

        {this.getInput({
          tittle: "基本病史",
          required: true,
          keyValue: "medicalHistory",
          placeholder: "含现病史、婚育史、月经史、既往病史等",
          value: "",
          fileList: "medicalHistoryFile"
        })}
        {this.showPicList(
          fileUrlJsonArray.medicalHistoryFile,
          "medicalHistoryFile"
        )}
        {this.getOnlyInput({
          tittle: "左卵泡数",
          placeholder: "左卵泡个数",
          keyValue: "leftFolliclesNum",
          type: 'number'
        })}
        {this.getOnlyInput({
          tittle: "右卵泡数",
          placeholder: "右卵泡个数",
          keyValue: "rightFolliclesNum",
          type: 'number'
        })}
        {this.getOnlyInput({
          tittle: "AMH(ng/ml)",
          placeholder: "请输入AMH",
          keyValue: "amh",
          type: 'digit'
        })}

        {this.getOnlyInput({
          tittle: "BMI(体重指数)",
          placeholder: "请输入BMI",
          keyValue: "bmi",
          type: 'digit'
        })}

        <View className={s.title}>试管指征</View>
        {this.getInput({
          tittle: "女方造影结果",
          required: true,
          keyValue: "graphyFemale",
          placeholder: "请输入或上传病历照片",
          value: "",
          fileList: "graphyFemaleFile"
        })}
        {this.showPicList(
          fileUrlJsonArray.graphyFemaleFile,
          "graphyFemaleFile"
        )}

        {this.getInput({
          tittle: "盆腔手术史",
          required: true,
          keyValue: "pelvicOperationHistory",
          placeholder: "子宫、输卵管、卵巢等",
          value: "",
          fileList: "pelvicOperationHistoryFile"
        })}
        {this.showPicList(
          fileUrlJsonArray.pelvicOperationHistoryFile,
          "pelvicOperationHistoryFile"
        )}

        {this.getInput({
          tittle: "男方精液问题",
          keyValue: "spermMale",
          placeholder: "如严重少弱畸精症、梗阻性无精等",
          value: "",
          fileList: "spermMaleFile",
          // required: true,
        })}
        {this.showPicList(fileUrlJsonArray.spermMaleFile, "spermMaleFile")}

        {this.getOnlyInput({
          tittle: "促排周期(次数)：",
          placeholder: "请输入促排周期(次数)",
          keyValue: "ovulateCycle",
          type: 'number'
        })}

        <View className={`${s.rowModule}`}>
          <View className={`${s.mainSuit}`}>是否有试管助孕史:</View>
          <View className={s.patientName}>
            {/* <Switch
              checked={isTubeHistory}
              onChange={(e) => this.changeTubeHistory(e)}
            /> */}
            <RadioGroup onChange={this.changeTubeHistory}>
                {this.state.list.map((item, i) => {
                  return (
                    <Label for={i} key={i}>
                      <Radio style={{ marginLeft: '10px' }} value={item.value} checked={item.checked}>{item.text}</Radio>
                    </Label>
                  )
                })}
              </RadioGroup>
          </View>
        </View>

        <View className={s.title}>其他</View>

        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>
            是否有以下感染性疾病（可多选）:
          </View>
          <View className={s.patientName}>
            <View className={s.radioList}>
              {/* <RadioGroup> */}
              {this.state.infectiousDiseasesList.map((item, i) => {
                return (
                  <Label className={s.radioListLabel} for={i} key={i}>
                    <Checkbox
                      className={s.radioListRadio}
                      value={item.value}
                      checked={item.checked}
                      onClick={(e) =>
                        this.checkboxChange(e, i, "infectiousDiseasesList")
                      }
                    >
                      {item.text}
                    </Checkbox>
                  </Label>
                );
              })}
              {/* </RadioGroup> */}
            </View>
          </View>
        </View>

        {this.getInput({
          tittle: "是否有遗传性疾病",
          required: true,
          keyValue: "hereditaryDiseases",
          placeholder: "如染色体基因、基因、家族遗传疾病、遗传性肿瘤等",
          value: "",
          fileList: "hereditaryDiseasesFile"
        })}
        {this.showPicList(
          fileUrlJsonArray.hereditaryDiseasesFile,
          "hereditaryDiseasesFile"
        )}

        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}><Text className={s.colorRed} style={{ color: "red" }}>*</Text>疾病诊断:</View>
          <View className={`${s.mainSuit}`}>女方:</View>
          <View className={s.patientName}>
            <View className={s.radioList}>
              {/* <RadioGroup> */}
              {this.state.diagnosisFemaleList.map((item, i) => {
                return (
                  <Label className={s.radioListLabel} for={i} key={i}>
                    <Checkbox
                      className={s.radioListRadio}
                      value={item.value}
                      checked={item.checked}
                      onClick={(e) =>
                        this.checkboxChange(e, i, "diagnosisFemaleList")
                      }
                    >
                      {item.text}
                    </Checkbox>
                  </Label>
                );
              })}
              {/* </RadioGroup> */}
            </View>
          </View>
          <View className={`${s.mainSuit}`}>男方:</View>
          <View className={s.patientName}>
            <View className={s.radioList}>
              {/* <RadioGroup> */}
              {this.state.diagnosisMaleList.map((item, i) => {
                return (
                  <Label className={s.radioListLabel} for={i} key={i}>
                    <Checkbox
                      className={s.radioListRadio}
                      value={item.value}
                      checked={item.checked}
                      onClick={(e) =>
                        this.checkboxChange(e, i, "diagnosisMaleList")
                      }
                    >
                      {item.text}
                    </Checkbox>
                  </Label>
                );
              })}
              {/* </RadioGroup> */}
            </View>
          </View>
        </View>

        <View className={`${s.inputModule}`}>
          <View className={`${s.mainSuit}`}>其他附件</View>
          <View className={s.fileContainer}>
            {fileUrlJsonArray.otherFile.length > 0 &&
              fileUrlJsonArray.otherFile.map((item, index) => {
                return (
                  <View key={item.fileUrl} className={s.fileContent}>
                    <Image
                      mode="aspectFit"
                      src={item.fileUrl}
                      onClick={this.previewImage.bind(
                        this,
                        index,
                        'otherFile'
                      )}
                    />
                    <View
                      className={s.deleteBtn}
                      onClick={this.deleteImage.bind(this, index, "otherFile")}
                    >
                      ×
                    </View>
                  </View>
                );
              })}
            {fileUrlJsonArray.otherFile.length < 6 &&
              <View
              className={s.addBtn}
              onClick={this.addImage.bind(this, "other")}
            >
              +
            </View>}
          </View>
        </View>
        <View className={`${s.suggestion}`} onClick={this.submit}>
          <Text>提交</Text>
        </View>
      </View>
    );
  }
}
