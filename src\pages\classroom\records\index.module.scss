.page{
  padding: 24px;
}
.list_item{
  margin-bottom: 16px;
  padding: 24px;
  background-color: #FFF;
  border-radius: 8px;
  &_title{
    margin-bottom: 24px;
    font-size: 36px;
    font-weight: 600;
    color: #000;
    @include ellipsisLn();
  }
  &_time{
    font-size: 20px;
    font-weight: 400;
    color: $color-text;
  }
  &_status{
    flex: 0 0 120px;
    width: 120px;
    text-align: right;
    font-size: 24px;
    font-weight: 600;
  }
}

.detail_page{
  padding: 36px;
  padding-top: 48px;
}
.herder_title{
  font-size: 48px;
}
.herder_tips{
  margin-top: 16px;
  margin-bottom: 24px;
  font-size: 28px;
  font-weight: 400;
  line-height: 42px;
  color: $color-text;
}
.page_body{
  padding: 1px 24px 24px;
  background-color: #FFF;
  border-radius: 8px;
  .block{
    // border-bottom: 1px dotted $color-border;
  }
  .title{
    margin: 24px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28px;
    line-height: 42px;
    font-weight: 600;
    color: #000;
  }
  .title_icon{
    width: 24px;
    height: 24px;
    &.collapsed{
      transform: rotate(180deg);
    }
  }

  .line{
    margin: 24px 0;
    display: flex;
    align-items: center;
    font-size: 32px;
    line-height: 48px;
    font-weight: 400;
    &_label{
      flex: 0 0 210px;
      color: #00000066;
    }
    &_value{
      color: #000000E5;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
  .line2{
    margin: 16px 0;
    display: flex;
    flex-direction: column;
    font-size: 32px;
    line-height: 48px;
    &_label{
      margin-bottom: 16px;
      color: #00000066;
    }
    &_value{
      color: #000000E5
    }
  }
  .line3{
    margin: 16px 0;
    margin-left: -16px;
    display: flex;
    flex-direction: column;
    font-size: 32px;
    line-height: 48px;
    &_label{
      margin-left: 16px;
      margin-bottom: 16px;
      color: #00000066;
    }
    &_value{
      display: flex;
      flex-wrap: wrap;
    }
    &_image{
      padding: 16px;
      &_inner{
        width: 160px;
        height: 160px;
      }
    }
  }
}

.page_footer {
  margin-top: 48px;
}
.btn{
  margin-bottom: 24px;
  height: 96px;
  line-height: 96px;
  font-size: 36px;
  border-radius: 76px;
  &::after{
    border: none;
  }
  &.submit{
    background: $color-primary;
    color: #fff;
  }
  &.cancel{
    background: var(--grey-grey-04, rgba(0, 0, 0, 0.04));
    color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));;
  }
}
