import Taro, { Component } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import s from './index.module.scss';

export default class TimeAxis extends Component {

  constructor(props) {
    super(props);
    this.state = {
      judge: this.props.status || false
    };
  }

  componentWillMount() {
  }

  componentDidMount() {

  }

  clickEvent = () => {
    const { judge } = this.state;
    this.setState({
      judge: !judge
    })
  }

  render() {
    const { judge } = this.state;
    const { item } = this.props;
    // console.log(item.createTime)
    return (
      <View className={`${s.allRecord}`}>
        <View className={`${s.recordModule}`} onClick={this.clickEvent}>
          {/* {item&&item.createTime&&<text>{`${item.createTime.substr(0, 4)} \n ${item.createTime.substr(5, 5)} `}</text>} */}
          {item&&item.createTime&&<text>{`${item.createTime} `}</text>}
          {/* <View className={`${s.timeAxis}`}>
            <View className={`${s.circle}`} />
            <View className={`${ s.noLine}`} />
          </View> */}
          <View className={`${s.name}`}>
            <Text> {item.accountName}</Text>
            {
              item.type === '3' &&  <View className={`${s.label}`}>仅自己可见</View>
            }
          </View>
          <View className={`${s.icon}`} >{judge ?
           <View className={`${s.top}`}></View> 
           :  <View className={`${s.right}`}></View> 
           }</View>
        </View>
        {judge && <View className={`${s.content}`}>
          {/* <View style={{ display: 'flex' }}>
            <View style={{ flex: 1, color: '#989898' }}>现病史： </View>
            <View style={{ flex: 5 }}>这里是一段文字</View>
          </View>
          <View style={{ display: 'flex' }}>
            <View style={{ flex: 1, color: '#989898' }}>诊断： </View>
            <View style={{ flex: 5 }}>这里是一段文字里是一段文字里是一段文字里是一段文字里是一段文字里是一段文字</View>
          </View> */}
          {item.content || '无'}
        </View>}
      </View>
    );
  }
}
