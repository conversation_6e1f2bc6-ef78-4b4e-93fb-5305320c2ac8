import Taro, {Component} from '@tarojs/taro';
import {Block, Button, Image, Input, View } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import logoPng from '../../static/image/logo.png'
import {_config} from '../../utils/config'

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      newPassword: '',
      newCheckPassword: '',
    };
  }

  config = {
    navigationBarTitleText: '登录'
  };

  onShareAppMessage() {
    return {
      title: `湖南家辉遗传专科医院`,
      complete: res => {
        console.log(res);
      }
    };
  }

  changeNPwd(e) {
    this.setState({newPassword: e.detail.value});
  }

  checkNewPwd(e) {
    this.setState({newCheckPassword: e.detail.value});
  }

  async getUserInfo () {
    const { code, data } = await Api.getUserInfo();
    if (code !== 0) return Promise.reject()
    Taro.setStorageSync('userInfo', JSON.stringify(data));
    Promise.resolve()
  }

  rePwd() {
    const {newPassword, newCheckPassword} = this.state;
    const reg =
      '^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,}$';
    if (newPassword !== newCheckPassword) {
      Taro.showToast({
        title: '两次密码不一致', //提示的内容,
        icon: 'none', //图标,
        duration: 1400, //延迟时间,
        mask: true, //显示透明蒙层，防止触摸穿透,
        success: () => {
        }
      });
    } else if (!RegExp(reg).test(newPassword)) {
      Taro.showToast({
        title: '请输入符合要求的密码', //提示的内容,
        icon: 'none', //图标,
        duration: 1400, //延迟时间,
        mask: true, //显示透明蒙层，防止触摸穿透,
        success: () => {
        }
      });
    } else {
      this.change({newPassword});
    }
  }

  async change(param) {
    Taro.showLoading({title: '加载中', mask: true});
    const info = await Api.changeNewPassWord(param);
    if (info.code !== 0) return
    Taro.hideLoading();
    Taro.setStorageSync('password', param.newPassword);
    await this.getUserInfo()
    const url = `/pages/patient/index`;
    Taro.switchTab({url});
  }

  render() {
    const { newPassword, newCheckPassword } = this.state;
    return (
      <View className={s.container}>
        <View className={s.loginPageNotice}>您已经登录成功，为了您的账号安全，请设置新的登录密码，并牢记。</View>
        <View className={s.header}>
          <Image src={logoPng} />
          <View>{_config.name}</View>
        </View>
        
        <Block>
          <View className={s.inputItem}>
            <Input
              password
              placeholder='请输入新密码'
              value={newPassword}
              onInput={this.changeNPwd}
              maxLength='18'
            />
          </View>
          <View className={s.inputItem}>
            <Input
              password
              placeholder='请再次输入新密码'
              value={newCheckPassword}
              onInput={this.checkNewPwd}
              maxLength='18'
            />
          </View>
          <View className={s.rePwd}>请输入密码，8~18位字母、数字、特殊字符组合。</View>
          <View className={s.loginPageFooter}>
            <Button className={s.loginBtn} onClick={this.rePwd}>
              确 认
            </Button>
          </View>
        </Block>
      </View>
    );
  }
}
