.wx_field{
  padding: 0 24px;
  background-color: #FFF;
  &.border .wx_field__body{
    border-bottom: 1px solid $color-border;
  }
  &.required .wx_field__label::before{
    content: "*";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(0, -50%);
    color: #FF613B;
  }
  &.disabled{
    background: rgba(0, 0, 0, 0.05);
  }
  &__body{
    height: 96px;
  }
  &__label {
    position: relative;
    margin-right: 24px;
    padding-left: 24px;
    font-size: 32px;
    font-weight: 400;
  }
  &__input{
    font-size: 32px;
    height: 100%;
    color: #000;
  }
  &__right{
    width: 32px;
    height: 32px;
  }
}

.wx_textarea{
  padding: 0 24px;
  background-color: #FFF;
  &.required .wx_textarea__label::before{
    content: "*";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(0, -50%);
    color: $color-warn;
  }
  &__label {
    position: relative;
    margin-bottom: 12px;
    padding-left: 12px;
    font-size: 32px;
    font-weight: 400;
  }
  &__input{
    padding: 24px;
    font-size: 32px;
    height: 240px;
    width: calc(100% - 48px);
    background-color: $color-bg;
    color: #000;
  }
}


.page_mask{
  position: fixed;
  z-index: 999;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  /* iOS Safari 修复 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

.select{
  position: fixed;
  top: 0;
  left: 120px;
  right: 0;
  bottom: 0;
  padding: 20px 20px 60px 20px;
  background-color: #FFF;
  /* iOS Safari 修复 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
  &_footer {
    height: 120px;
    display: flex;
    &_button{
      flex: 1 1 auto;
      height: 96px;
      padding: 0px 16px;
      border-radius: 8px;
      text-align: center;
      font-size: 32px;
      font-style: normal;
      font-weight: 500;
      line-height: 96px;
      &.submit{
        margin-left: 36px;
        background: #308B91;
        color: #FFF;
      }
      &.cancel{
        background: var(--grey-grey-04, rgba(0, 0, 0, 0.04));
      }
    }
  }
  &_header{
    height: 80px;
    &__input{
      padding: 0 12px;
      width: 100%;
      box-sizing: border-box;
      height: 72px;
      border: 1px solid #308B91;
      border-radius: 12px;
    }
  }
  &_body{
    height: calc(100vh - 280px);
    margin: 20px 0;
    flex: 1 1 auto;
    &__line {
      height: 96px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid $color-border;
      color: #989898;
      font-size: 32px;
      &.selected{
        color: #308B91;
      }
      &_text{
        flex: 1 1 auto;
        margin-right: 36px;
        @include ellipsisLn();
      }
    }
  }
  &_body__empty{
    height: calc(100vh - 280px);
  }
}

/* iOS Safari 特殊修复 */
@supports (-webkit-touch-callout: none) {
  .page_mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    /* 强制使用硬件加速 */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .select {
    position: absolute;
    /* 强制使用硬件加速 */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}
