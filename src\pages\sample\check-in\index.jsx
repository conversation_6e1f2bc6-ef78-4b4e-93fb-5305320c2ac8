import Taro, { Component } from "@tarojs/taro";
import { View, Text, Image, Input, Button, Camera } from "@tarojs/components";
import FieldPicker from '@/component/field/picker';
import FieldPicker2 from '@/component/field/picker2';
import Field from '@/component/field';
import Upload from '@/component/upload';

import scandPng from '@/resources/images/sample/scan.png'
import searchPng from '@/resources/images/search.png'
import barcodePng from '@/resources/images/sample/barcode.png'
import deletePng from '@/resources/images/sample/delete.png'


import * as API from '../api'

import s from './index.module.scss'

const filterNullParams = params => Object.keys(params).reduce((res, key) => {
  if (params[key] !== null && typeof params[key] !== 'undefined') {
    res[key] = params[key]
  }
  return res
}, {})


export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      hasMask: false,
      isEdit: false,
      hideCamera: true,
      productDisabled: true,
      hideCamera2: true, // 新增控制第二个相机的状态

      outSampleNumber: '',

      lastBarCode: '',
      barCode: '',
      submitHospital: '',
      /** 合作客户 */
      institutionId: '',
      institutionName: '',
      institutionDisabled: false,
      secendInstitutionId: '',
      secendInstitutionName: '',

      contractId: '',
      productId: '',
      productName: '',
      sonProductId: '',
      sampleTime: '',
      submitDoctor: '',
      platform: '',//平台：华大 贝瑞

      files: [],

      platformList: [{ label: '华大', value: '华大' }, { label: '贝瑞', value: '贝瑞' }, { label: '其他', value: '其他' }],

      /** 产品线下拉 */
      productList: [],
      // 子产品线下拉
      subProductList: [],
      hisList: [],
      /** 合作客户 */
      institutionList: [],

      canSubmit: false,
    }
  }

  componentWillMount() {
    const { id } = this.$router.params
    if (id) {
      this.setState({ isEdit: true })
      this.getDetail(id)
    } else {
      this.initCameraAuth()
      Taro.showLoading()
    }
    this.handleGetProductList()
    this.handleGetInstitutionList()
    this.handleGetHisList()
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.barCode !== this.state.barCode) {
      this.setPlatform(this.state.barCode)
    }
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '收样本',
    navigationBarTextStyle: 'black'
  };

  async handleGetHisList() {
    const { code, data = [] } = await API.getInstitutionListV2()
    if (code !== 0) return
    const hisList = (data || []).map(v => ({
      label: v.institutionName,
      value: v.id,
    }))
    this.setState({
      hisList,
    })
  }

  async getDetail(id) {
    const { code, data } = await API.getSampleDetail({ id })
    if (code !== 0) return
    this.setState({
      barCode: data.sampleNumber,

      contractId: data.contractId,
      platform: data.platform,
      productId: data.productId,
      productName: data.productName,
      sonProductId: data.sonProductId,
      institutionId: data.institutionId || null,
      institutionName: data.institutionName || '',
      secendInstitutionId: data.secendInstitutionId || null,
      secendInstitutionName: data.secendInstitutionName || '',
      submitHospital: data.submitHospital,
      sampleTime: data.sampleTime,
      submitDoctor: data.submitDoctor,
      files: data.files ? data.files.split(',') : []
    })
    this.handleGetSubProductList({ parentId: data.productId })
  }

  getSetting = () => new Promise((resolve, reject) => {
    Taro.getSetting({
      success: res => {
        const auth = res.authSetting
        const scopeCamera = auth['scope.camera']
        resolve(scopeCamera)
      },
      fail: reject
    })
  })

  initCameraAuth = async () => {
    const scopeCamera = await this.getSetting()
    /** 已授权摄像头 */
    if (scopeCamera !== false) {
      this.setState({ hideCamera: false,  hideCamera2: true})
    } else {
      Taro.showToast({ title: '请点击右上角图标授权相机', icon: 'none' })
    }
  }

  openSetting = async () => {
    if (!this.state.hideCamera) return
    const scopeCamera = await this.getSetting()
    if (scopeCamera) {
      this.setState({ hideCamera: false })
      return
    }
    Taro.openSetting({
      success: res => {
        if (res.authSetting['scope.camera']) {
          this.setState({ hideCamera: false })
        }
      }
    })
  }

  onCameraInitDone = () => {
    Taro.hideLoading()
  }

  handleCameraError = (e, type) => {
    console.error(e)
    Taro.hideLoading()
    Taro.showToast({ icon: 'none', title: e.detail.errMsg || '摄像机调用失败' })
    this.setState({ [type]: true })
  }

  onCameraError = e => {
    this.handleCameraError(e)
  }

  onCameraScanCode = e => {
    Taro.showToast({ title: '扫描成功', icon: 'none' })
    this.setState({
      hideCamera: true,
      barCode: e.detail.result,
    })
  }

  onCameraScanCode2 = e => {
    Taro.showToast({ title: '外部样本编号扫描成功', icon: 'none' })
    this.setState({
      hideCamera2: true,
      outSampleNumber: e.detail.result,
    })
  }


  //根据条码号自动设置平台类型
  setPlatform = barcode => {
    // 统一转为大写进行比较
    const code = barcode.toUpperCase();
    
    // 华大特殊产品：JHNF/JHNFP/JHNFN
    if (code.indexOf('JHNF') !== -1) {
      this.onPlatformChange('华大')
      return
    }
    
    // 华大其他产品：JHZX/JHECS/JHYS/JHSMAQ/JHEL
    if (code.indexOf('JHZX') !== -1 || 
        code.indexOf('JHECS') !== -1 || 
        code.indexOf('JHYS') !== -1 || 
        code.indexOf('JHSMAQ') !== -1 || 
        code.indexOf('JHEL') !== -1) {
      this.onPlatformChange('华大')
      return
    }
    
    // 华大本部：JHBN/JHBP/JHBC
    if (code.indexOf('JHBN') !== -1 || 
        code.indexOf('JHBP') !== -1 || 
        code.indexOf('JHBC') !== -1) {
      this.onPlatformChange('华大')
      return
    }
    
    // 华大新疆：JHFXN/JHFXP
    if (code.indexOf('JHFXN') !== -1 || 
        code.indexOf('JHFXP') !== -1) {
      this.onPlatformChange('华大')
      return
    }
    
    // 贝瑞：JHN/JHP/JHC（但要排除已经判断为华大的情况）
    if ((code.indexOf('JHN') !== -1 && code.indexOf('JHNF') === -1) || 
        code.indexOf('JHP') !== -1 || 
        code.indexOf('JHC') !== -1) {
      this.onPlatformChange('贝瑞')
      return
    }
    
    // 其他
    this.onPlatformChange('其他')
  }

  onPlatformChange = value => {
    this.setState({
      platform: value,
    }, this.checkStatus)
  }

  onProductChange = (_, item) => {
    this.setState({
      productId: item.value,
      productName: item.label,
      sonProductId: '',
      subProductList: [],
    }, this.checkStatus)
    this.handleGetSubProductList({ parentId: item.value })
  }

  onSubProductChange = value => {
    this.setState({
      sonProductId: value,
    })
  }

  onHisChange = value => {
    this.setState({
      submitHospital: value,
    }, this.checkStatus)
  }

  onInstitutionChange = (_, item) => {
    this.setState({
      institutionId: item.value,
      institutionName: item.label,
    }, this.checkStatus)
  }
  onSecendInstitutionChange = (_, item) => {
    this.setState({
      secendInstitutionId: item.value,
      secendInstitutionName: item.label,
    }, this.checkStatus)
  }

  onSampleTimeChange = e => {
    const value = e.detail.value
    this.setState({ sampleTime: value }, this.checkStatus)
  }

  handleSetState = (key, val) => {
    this.setState({
      [key]: val
    }, this.checkStatus)
  }

  showError(title, duration = 1500) {
    Taro.showToast({ title, icon: 'none', duration });
  }

  async handleScanBarCode() {
    const res = await Taro.scanCode({ scanType: ['barCode'], onlyFromCamera: true, })
    Taro.showToast({ title: '扫描成功', icon: 'none' })
    this.setState({
      barCode: res.result,
    }, this.handleSearch)
  }
  handleScanoutSampleNumber = async () => {
    const scopeCamera = await this.getSetting()
    if (scopeCamera !== false) {
      this.setState({ 
        hideCamera2: false ,
        hideCamera: true 
    })
    } else {
      Taro.showToast({ title: '请点击右上角图标授权相机', icon: 'none' })
    }
  }


  async handleSearch() {
    const { barCode, lastBarCode } = this.state
    if (!barCode) return this.showError('请扫码或输入样本编号')
    if (barCode === lastBarCode) return
    const { code: code1 } = await API.checkIsAlreadyExist({ sampleNumber: barCode })
    console.log("checkIsAlreadyExist222", code1)
    this.setState({ lastBarCode: barCode })
    if (code1 !== 0) return
    const { data: data2, code: code2, msg } = await API.getContractByBarCode({ samplingNumber: barCode })
    console.log("getContractByBarCode333", code2)
    if (code2 === -10000) return this.showError(msg)
    const { contractId = '', productId, productName, institutionId = '', institutionName = '' } = data2 || {}
    if (productId) {
      this.setState({
        contractId,
        institutionId,
        institutionName,
        productId,
        productName,
        sonProductId: '',
        productDisabled: true,
      }, () => {
        // 在state更新后执行
        this.setPlatform(barCode)
        this.handleGetSubProductList({ parentId: productId })
      })

    } else {
      this.setState({
        contractId,
        productId: '',
        productName: '',
        sonProductId: '',
        subProductList: [],
        productDisabled: false,
      })
    }
    this.setState({ institutionDisabled: !!institutionId })
  }

  handleGetProductList = async () => {
    const { code, data } = await API.getProductList()
    if (code !== 0) return
    this.setState({
      productList: (data || []).map(v => ({ label: v.productName, value: v.id })),
    })
  }

  handleGetInstitutionList = async () => {
    const { code, data } = await API.getInstitutionListV2()
    if (code !== 0) return
    this.setState({
      institutionList: (data || []).map(v => ({ label: v.institutionName, value: v.id })),
    })
  }

  async handleGetSubProductList(params) {
    const { code, data } = await API.getProductList(params)
    if (code !== 0) return
    const subProductList = (data || []).map(v => ({ label: v.productName, value: v.id }))
    this.setState({
      subProductList,
    })
  }

  async validate() {
    const { platform, productId, sampleTime, submitHospital, files, institutionId } = this.state
    if (!platform) return Promise.resolve('请选择平台')
    if (!productId) return Promise.resolve('请选择产品线')
    if (!sampleTime) return Promise.resolve('请选择采样时间')
    if (!institutionId) return Promise.resolve('请填写合作客户')
    if (!submitHospital) return Promise.resolve('请填写采样医院')
    if (!files || !files.length) return Promise.resolve('请上传知情同意书')
    return Promise.resolve()
  }

  /** type: 1 新增 2 更新 */
  /**
   * 提交样本信息
   * @param {number} [type=1] - 操作类型：1=新增样本，2=更新样本
   * @returns {Promise<void>} 无返回值
   * @description 
   * - 先进行表单验证，失败则显示错误信息
   * - 根据操作类型调用不同API（新增/更新）
   * - 提交成功后返回上一页
   * - 自动过滤空参数
   */
  submit = async (type = 1) => {
    const error = await this.validate()
    if (error) return this.showError(error)
    const { barCode, outSampleNumber, platform, productId, sonProductId, sampleTime, submitHospital,
      submitDoctor, files, contractId, institutionId, secendInstitutionId, secendInstitutionName } = this.state

    if (type === 1) {
      const { code: code1 } = await API.checkIsAlreadyExist({ sampleNumber: barCode })
      console.log("checkIsAlreadyExist111", code1)
      if (code1 !== 0) return
    }

    let params = {
      institutionId: institutionId || null,
      secendInstitutionId: secendInstitutionId || null,
      secendInstitutionName: secendInstitutionName || null,
      contractId: contractId || null,
      sampleNumber: barCode,
      outSampleNumber: outSampleNumber || null,//外部样本编号
      platform,
      productId,
      sonProductId: sonProductId || null,
      sampleTime,
      submitHospital,
      submitDoctor,
      files: files.join(',')
    }

    console.log("params", params)

    params = filterNullParams(params)

    if (type === 2) params.id = this.$router.params.id
    const fn = type === 1 ? API.addSample : API.updateSample
    const { code } = await fn(params)

    if (code !== 0) return
    Taro.navigateBack()
  }

  handleDelete = () => {
    Taro.showModal({
      title: '确认删除',
      content: '请确认是否删除当前样本删除后不可恢复',
      confirmColor: '#882F38',
      success: res => {
        if (res.confirm) {
          this.sampleDelete()
        }
      }
    })
  }

  async sampleDelete() {
    const { code } = await API.deleteSample({ id: this.$router.params.id })
    if (code !== 0) return
    Taro.showToast({ title: '删除成功', icon: 'none' })
    setTimeout(Taro.navigateBack, 1500)
  }

  checkStatus = () => {
    const { barCode, platform, productId, sampleTime, submitHospital, files, institutionId } = this.state
    const canSubmit = barCode && platform && productId && sampleTime && submitHospital && files.length && institutionId
    this.setState({ canSubmit })
  }

  onMaskChange = flag => {
    this.setState({ hasMask: flag })
  }

  render() {
    const { isEdit, barCode, hideCamera, productId, productName, platform, platformList, productList, subProductList, sonProductId,
      sampleTime, submitHospital, hisList, submitDoctor, files, productDisabled, canSubmit,
      institutionList, institutionId, institutionName, secendInstitutionId, secendInstitutionName, institutionDisabled, hasMask } = this.state
    return (
      <View className={s.page} style={{ overflow: hasMask ? 'hidden' : 'auto' }}>
        {!isEdit ?
          <View className={s.header}>
            <View className={s.header_title}>
              <Text>请将条形码对准扫码单</Text>
              <Image
                className={s.header_title__icon}
                src={scandPng}
                open-type='openSetting'
                onClick={this.openSetting}
              />
            </View>
            {
              hideCamera ? null :
                <Camera
                  className={s.header_camera}
                  mode='scanCode'
                  device-position='back'
                  resolution='high'
                  frame-size='large'
                  flash='off'
                  onInitDone={this.onCameraInitDone}
                  onError={this.onCameraError}
                  onScanCode={this.onCameraScanCode}
                />}
          </View> : null}

        <View className={s.body}>
          {
            isEdit ?
              <View className={s.header2}>
                <Image className={s.header2_icon} src={barcodePng} />
                <Text className={s.header2_title}>{barCode}</Text>
                <Image className={s.header2_delete} src={deletePng} onClick={this.handleDelete} />
              </View> :

              <View className={s.search}>
                <View className={s.search_input}>
                  <Image className={s.search_input__icon} src={searchPng} />
                  <Input
                    value={barCode}
                    className={s.search_input__inner}
                    placeholder='请扫码或输入样本编号'
                    onInput={e => this.handleSetState('barCode', e.detail.value)}
                  />
                </View>
                <Button className={s.search_button} onClick={this.handleSearch}>检索</Button>
                {/* <Button className={s.search_button} onClick={this.handleScanBarCode}>扫码</Button> */}
              </View>}


          <View className={s.title}>基本信息</View>

          <View className={s.form_item}>
            <Text className={s.form_label}>外部样本编号</Text>
            <View className={s.input_box}>
              <Input
                value={outSampleNumber}
                className={s.form_input}
                placeholder='扫码后自动录入'
                maxlength={20}
                onInput={e => this.handleSetState('outSampleNumber', e.detail.value)}
              />
              <Image
                className={s.scan_icon}
                src={scandPng}
                onClick={this.handleScanoutSampleNumber}
              />
            </View>
            {/* 新增第二个相机组件 */}
            {!hideCamera2 && (
              <View className={s.mask} >
                <View className={s.small_camera_wrapper}>
                  <Camera
                    className={s.small_camera}
                    mode='scanCode'
                    device-position='back'
                    resolution='high'
                    frame-size='medium'
                    flash='off'
                    onInitDone={this.onCameraInitDone}
                    onError={(e) => this.handleCameraError(e, 'hideCamera2')}
                    onScanCode={this.onCameraScanCode2}
                  />
                  <View className={s.small_camera_mask}>
                    <Text className={s.close_text} onClick={() => this.setState({ hideCamera2: true })}>点击关闭扫描</Text>
                  </View>
                </View>
              </View>
            )}

          </View>

          <FieldPicker2
            required
            label='平台'
            value={platform || ''}
            placeholder='检索后自动选择'
            range={platformList}
            onChange={this.onPlatformChange}
            forceUpdate
          />
          <FieldPicker2
            required
            label='产品线'
            value={productId}
            valueLabel={productName}
            placeholder='请选择'
            disabled={productDisabled || isEdit}
            range={productList}
            onChange={this.onProductChange}
          />
          {/* console.log("产品线",productId, sonProductId) */}
          <FieldPicker2
            label='子产品线'
            value={sonProductId}
            placeholder='请选择'
            range={subProductList}
            onChange={this.onSubProductChange}
          />
          <FieldPicker
            mode='date'
            required
            label='采样时间'
            value={sampleTime}
            placeholder='请选择'
            onChange={this.onSampleTimeChange}
          />
          <FieldPicker2
            required
            label='送检医院'
            value={submitHospital}
            placeholder='请选择'
            range={hisList}
            onChange={this.onHisChange}
          />
          <FieldPicker2
            required
            label='合作客户'
            value={institutionId}
            valueLabel={institutionName}
            // disabled={institutionDisabled}
            placeholder='请选择'
            range={institutionList}
            onChange={this.onInstitutionChange}
          />
          <FieldPicker2
            label='二级合作客户'
            value={secendInstitutionId}
            valueLabel={secendInstitutionName}
            placeholder='请选择'
            range={hisList}
            onChange={this.onSecendInstitutionChange}
          />
          <Field
            value={submitDoctor}
            label='送检医生'
            placeholder='请输入'
            maxlength={20}
            onSetValue={v => this.handleSetState('submitDoctor', v)}
          />

          <Upload
            title='知情同意书图片'
            fileList={files}
            limit={9}
            sizeType={['original']}
            updateFileList={list => this.setState({ files: list }, this.checkStatus)}
          />

          {
            isEdit ?
              <View className={s.footer}>
                <Button className={[s.button, s.submit]} onClick={() => this.submit(2)}>确 定</Button>
                <Button className={[s.button, s.cancel]} onClick={Taro.navigateBack}>取 消</Button>
              </View> :
              <View className={s.footer}>
                <Button className={[s.button, s.submit, !canSubmit && s.disabled]} disabled={!canSubmit} onClick={() => this.submit(1)}>录入样本</Button>
              </View>
          }
        </View>
      </View>
    )
  }
}
