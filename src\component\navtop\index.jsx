import Taro, { Component } from '@tarojs/taro';
import { View, Image } from '@tarojs/components';

import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      navHeight: Taro.pxTransform(52)
    }
  }

  componentWillMount () {}

  componentDidMount () {
    const res = Taro.getSystemInfoSync();
    this.setState({ navHeight: 44 + res.statusBarHeight + 'px' });
  }

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  backTo = () => {
    Taro.navigateBack({
      delta: 1
    });
  }

  goHome = () => {
    Taro.reLaunch({
      url: '/pages/home/<USER>'
    });
  }

  render () {
    const { navHeight } = this.state;
    const { children, bgColor, color, isBack } = this.props;
    return (
      <View className={styles['nav-top']} style={{ paddingBottom: navHeight }}>
        <View className={styles['nav-box']} style={{ height: navHeight, backgroundColor: bgColor, color }}>
          {
            isBack ?
              <View className={styles['capsule']}>
                <View className={styles['nav-back']} onClick={this.backTo}>
                  <View className={styles['back-confont']} />
                </View>
                <View className={styles['back-home']} onClick={this.goHome}>
                  <Image className={styles.image} src={`${$CDN_DOMAIN}/home.png`} />
                </View>
              </View> : null
          }
          <View className={styles['nav-title']} style={{ paddingRight: isBack ? Taro.pxTransform(190) : '' }}>
            {children}
          </View>
        </View>
      </View>
    )
  }
}
