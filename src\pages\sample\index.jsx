import { View, Image } from "@tarojs/components";
import Taro, { Component } from "@tarojs/taro";
import readyPng from '@/resources/images/sample/ready.png'
import alreadyPng from '@/resources/images/sample/already.png'
import s from './index.module.scss'

import * as API from './api'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      ready: 0,
      today: 0,
      curMonth: 0,
      all: 0,
    }
  }

  componentDidShow() {
    this.getSummary()
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '样本录入',
    navigationBarTextStyle: 'black'
  };

  async getSummary() {
    const { code, data } = await API.getAllSampleList()
    if (code !== 0) return
    const { shouyangben: ready, jinri: today, dangyue: curMonth, leiji: all } = data

    this.setState({
      ready,
      all,
      today,
      curMonth,
    })
  }

  // 跳转到样本寄送页面
  goToSampleSendOut = () => {
    Taro.navigateTo({ url: '/pages/sample/send-out/index' })
  }

  render() {
    const { ready, today, curMonth, all } = this.state
    return (
      <View className={s.page}>
        <View className={s.block} onClick={() => Taro.navigateTo({ url: '/pages/sample/ready-list/index' })}>
          <View className={s.block_wrapper}>
            <Image className={s.block__icon} src={readyPng} />
            <View className={s.block__value}>收样本({ready})</View>
          </View>
        </View>

        <View className={[s.block, s.block_2]} onClick={() => Taro.navigateTo({ url: '/pages/sample/already-list/index' })}>
          <View className={s.block_wrapper}>
            <Image className={s.block__icon} src={alreadyPng} />
            <View className={s.block__value}>已上传样本</View>
          </View>
        </View>

        <View className={s.summary}>
          <View className={s.summary_title}>已上传样本数统计：</View>
          <View className={s.list}>
            <View className={s.list_item}>
              <View className={s.list_item__top}>{today}</View>
              <View className={s.list_item__bottom}>今日</View>
            </View>
            <View className={s.list_item}>
              <View className={s.list_item__top}>{curMonth}</View>
              <View className={s.list_item__bottom}>当月</View>
            </View>
            <View className={s.list_item}>
              <View className={s.list_item__top}>{all}</View>
              <View className={s.list_item__bottom}>累计</View>
            </View>
          </View>
        </View>

        {/* 底部样本邮寄按钮 */}
        <View className={s.footer}>
          <View className={s.send_button} onClick={this.goToSampleSendOut}>
            样本邮寄
          </View>
        </View>
      </View>
    )
  }
}
