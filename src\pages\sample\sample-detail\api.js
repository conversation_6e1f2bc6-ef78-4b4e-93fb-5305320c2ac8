import { request, post } from '../../../utils/request';

/**
 * 获取样本详情信息
 * @param {Object} params - 请求参数
 * @param {String} params.id - 样本ID
 * @param {String} params.sampleCode - 样本编码
 * @returns {Promise}
 */
export function getSampleDetailInfo(params = {}) {
  // 如果传入的是样本编码，使用不同的接口
  if (params.sampleCode) {
    return request({
      url: '/api/sample/detailByCode',
      method: 'GET',
      data: params
    });
  }

  return request({
    url: '/api/sample/detail',
    method: 'GET',
    data: params
  });
}



/**
 * 提交问卷基础信息
 * @param {Object} data - 基础信息数据
 * @returns {Promise}
 */
export function submitBasicInfo(data = {}) {
  return request({
    url: '/api/survey/basicInfo',
    method: 'POST',
    data
  });
}



/**
 * 签署知情同意书
 * @param {Object} params - 请求参数
 * @param {String} params.id - 样本ID
 * @returns {Promise}
 */
export function signConsentForm(params = {}) {
  return request({
    url: '/api/sample/signConsent',
    method: 'POST',
    data: params
  });
}

/**
 * 签署知情同意书文件
 * @param {Object} params - 请求参数
 * @param {String} params.id - 样本ID
 * @param {String} params.sampleId - 样本ID
 * @returns {Promise}
 */
export function signSampleFile(params = {}) {
  return post('/api/sample/signSampleFile', params);
}

/**
 * 获取知情同意书内容
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getConsentContent(params = {}) {
  return request({
    url: '/api/sample/consentContent',
    method: 'GET',
    data: params
  });
}

/**
 * 更新样本产品类型
 * @param {Object} data - 更新数据
 * @param {String} data.id - 样本ID
 * @param {String} data.type - 产品类型 ('normal' | 'child')
 * @returns {Promise}
 */
export function updateSampleType(data = {}) {
  return request({
    url: '/api/sample/updateType',
    method: 'POST',
    data
  });
}

/**
 * 获取健康订单详情
 * @param {Object} params - 请求参数
 * @param {String} params.id - 订单ID
 * @param {Boolean} [showLoading=true] - 是否显示加载提示，默认为true
 * @returns {Promise}
 */
export function getHealthOrderById(params = {}, showLoading = true) {
  return post('/api/sample/getHealthOrderById', params, showLoading);
}

/**
 * 根据Key获取配置信息
 * @param {Object} params - 请求参数
 * @param {String} params.hisId - 医院ID
 * @param {String} params.platformId - 平台ID
 * @param {String} params.key - 问卷ID
 * @returns {Promise}
 */
export function getProfileByKey(params = {}) {
  return post('/api/address/getProfileByKey', params);
}

/**
 * 获取问卷详情
 * @param {Object} params - 请求参数
 * @param {String} params.id - 问卷ID
 * @returns {Promise}
 */
export function getSurveyDetail(params = {}) {
  return post('/api/questionphone/getquestionscopeforid', params);
}

/**
 * 获取问卷答案
 * @param {Object} params - 请求参数
 * @param {String} params.id - 问卷ID
 * @param {String} params.questionUserId - 问卷用户ID
 * @returns {Promise}
 */
export function getSurveyAnswer(params = {}) {
  return post('/api/questionphone/getquestiondetailbyid', params);
}

/**
 * 保存问卷答案
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function saveQuestion(params) {
  return post('/api/questionphone/savequestion', params);
}

/**
 * 获取样本问卷列表
 * @param {Object} params - 请求参数
 * @param {String} params.userId - 用户ID，可选
 * @param {String} params.patientId - 患者ID，可选
 * @returns {Promise}
 */
export function getSampleSurveyList(params = {}) {
  return post('/api/sample/getSampleSurveyList', params);
}

/**
 * 获取问卷记录列表
 * @param {Object} params - 请求参数
 * @param {String} params.id - 问卷ID
 * @returns {Promise}
 */
export function getAnswerList(params = {}) {
  return post('/api/sample/getAnswerList', params);
}

/**
 * 获取问卷类型列表
 * @param {Object} params - 请求参数
 * @returns {Promise}
 */
export function getQuestionTypes(params = {}) {
  return request({
    url: '/api/survey/types',
    method: 'GET',
    data: params
  });
}

/**
 * 绑定样本管编号
 * @param {String} id - 订单ID
 * @param {String} sampleNumber - 样本管编号
 * @returns {Promise}
 */
export function bindSampleNumber(id, sampleNumber) {
  return post('/api/sample/bindSampleNumber', { id, sampleNumber });
}
