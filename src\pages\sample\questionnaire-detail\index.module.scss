/* 页面容器 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120px;
}

/* 问卷头部样式 */
.questionnaireHeader {
  padding: 30px 40px 20px;
  background-color: #fff;
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 0 20px 20px 20px;
}

.questionnaireTitle {
  font-size: 36px;
  color: rgba(0, 0, 0, 0.9);
  font-weight: 600;
  text-align: center;
  margin-bottom: 30px;
  line-height: 52px;
}

.questionnaireDescription {
  font-size: 32px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 46px;
}

.descText {
  text-indent: 2em;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 15px;
  display: block;
  font-size: 32px;
}

/* 问卷内容容器 */
.questionnaireContent {
  padding: 0 20px 60px;
}

/* 问题项样式 */
.questionItem {
  position: relative;
  margin-bottom: 30px;
  padding: 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.questionTitle {
  font-size: 32px;
  color: rgba(0, 0, 0, 0.9);
  font-weight: 600;
  line-height: 46px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.questionType {
  font-size: 28px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
  margin-left: 10px;
}

.required {
  color: #ff4d4f;
  font-size: 32px;
  margin-left: 5px;
}

/* 单选题样式 */
.radioGroup {
  width: 100%;
}

.radioGroupItem {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    background-color: rgba(48, 161, 166, 0.05);
    border: 1px solid rgba(48, 161, 166, 0.2);
  }

  &:hover {
    background-color: rgba(48, 161, 166, 0.02);
  }
}

/* 多选题样式 */
.checkboxGroup {
  width: 100%;
}

.checkboxGroupItem {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    background-color: rgba(48, 161, 166, 0.05);
    border: 1px solid rgba(48, 161, 166, 0.2);
  }

  &:hover {
    background-color: rgba(48, 161, 166, 0.02);
  }
}

/* 选项内容 */
.optionContent {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.optionLabel {
  flex: 1;
  font-size: 32px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 46px;
  font-weight: 400;
}

/* 二级内容容器 */
.secondaryContentContainer {
  margin-top: 15px;
  margin-left: 35px;
  width: calc(100% - 35px);
  box-sizing: border-box;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 10px;
    width: 3px;
    height: 3px;
    background-color: #30A1A6;
    border-radius: 50%;
  }
}

.secondaryTextarea {
  width: 100%;
  min-height: 80px;
  padding: 12px 0 10px;
  font-size: 30px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 1.4;
  background-color: #fff;
  box-sizing: border-box;
  border: none;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 0;
  transition: border-color 0.3s ease;

  &:focus {
    border-bottom: 2px solid #30A1A6;
    outline: none;
  }
}

/* 二级复选框组样式 */
.secondaryCheckboxGroup {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 10px 0;
  margin-top: 10px;
  background-color: rgba(248, 248, 248, 0.5);
  border-radius: 8px;
  padding: 16px;
  box-sizing: border-box;
}

.secondaryCheckboxItem {
  display: flex;
  align-items: center;
  margin-right: 30px;
  margin-bottom: 15px;
  width: 45%;

  &:nth-child(2n) {
    margin-right: 0;
  }

  checkbox {
    transform: scale(0.85);
    margin-right: 6px;
  }

  text {
    font-size: 30px;
    color: rgba(0, 0, 0, 0.7);
    flex: 1;
    word-break: break-all;
    line-height: 1.4;
  }
}

/* 二级多值填空样式 */
.secondaryFillBlankContainer {
  display: block;
  width: 100%;
  margin-top: 10px;
  background-color: rgba(248, 248, 248, 0.5);
  border-radius: 8px;
  padding: 16px;
  box-sizing: border-box;
  line-height: 2.2;
  word-wrap: break-word;
  font-size: 32px;
  text-align: justify;
}

.secondaryFillBlankPart {
  display: inline;
  vertical-align: baseline;
  margin: 0;
  padding: 0;
}

.secondaryFillBlankText {
  display: inline;
  vertical-align: baseline;
  line-height: inherit;
  margin: 0;
  padding: 0;
  font-size: 32px;
  color: rgba(0, 0, 0, 0.9);
  white-space: pre-wrap;
}

.secondaryFillBlankInput {
  display: inline-block;
  vertical-align: baseline;
  min-width: 80px;
  max-width: 160px;
  height: 46px;
  line-height: 46px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0 12px;
  margin: 0 4px;
  font-size: 32px;
  background-color: #fff;
  box-sizing: border-box;
  text-align: center;
}

/* 填空题样式 */
.fillBlankContainer {
  width: 100%;
}

.fillBlankItem {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.fillBlankPart {
  display: inline-block;
  vertical-align: middle;
}

.fillBlankText {
  font-size: 32px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 46px;
  font-weight: 400;
}

.fillBlankInput {
  display: inline-block;
  min-width: 120px;
  height: 46px;
  line-height: 46px;
  padding: 0 12px;
  margin: 0 5px;
  border: none;
  border-bottom: 2px solid #e0e0e0;
  font-size: 32px;
  color: rgba(0, 0, 0, 0.9);
  background-color: transparent;
  transition: border-color 0.3s ease;

  &:focus {
    border-bottom-color: #30A1A6;
    outline: none;
  }
}

/* 文件上传样式 */
.fileUploadSection {
  margin-top: 30px;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 12px;
  border: 1px solid #eee;
}

.fileUploadTitle {
  margin-bottom: 20px;
}

.fileUploadTitleText {
  font-size: 34px;
  color: rgba(0, 0, 0, 0.9);
  font-weight: 500;
  display: block;
  margin-bottom: 10px;
  line-height: 48px;
}

.fileUploadTitleDesc {
  font-size: 28px;
  color: rgba(0, 0, 0, 0.6);
  display: block;
  line-height: 40px;
}

.fileUploadContent {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 10px;
}

.fileItem {
  position: relative;
  width: 160px;
  height: 160px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  border: 2px solid #e0e0e0;
}

.fileItem .img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.deleteBtn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 32px;
  height: 32px;
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  z-index: 10;
  cursor: pointer;
}

.pdfItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: #fff;
  height: 100%;
  box-sizing: border-box;
}

.pdfIcon {
  font-size: 28px;
  font-weight: bold;
  color: #ff6b6b;
  background: #ffe0e0;
  padding: 8px 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  line-height: 1;
}

.pdfName {
  font-size: 26px;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.fileUploadBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f7f7f7;
  border: 1px dashed #d9d9d9;
  cursor: pointer;
  transition: border-color 0.3s, background-color 0.3s;
  width: 160px;
  height: 160px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 12px;

  &:active {
    background-color: #f0f0f0;
    border-color: #30A1A6;
  }
}

.uploadIconPlus {
  font-size: 48px;
  color: #8c8c8c;
  font-weight: bold;
  line-height: 1;
}

.uploadBtnText {
  font-size: 28px;
  color: #8c8c8c;
  margin-top: 8px;
  line-height: 40px;
}

/* 底部按钮样式 */
.questionnaireBottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px 0 40px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.submitBtn {
  margin: 20px auto;
  background: linear-gradient(90deg, #30A1A6 0%, #2F848B 100%);
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(48, 161, 166, 0.3);
  transition: all 0.3s ease;
  height: 88px;
  line-height: 88px;
  text-align: center;
  color: #fff;
  border-radius: 44px;
  font-size: 36px;
  width: 90%;
  border: none;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 6px rgba(48, 161, 166, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: 0 2px 6px rgba(48, 161, 166, 0.2);
  }
}

/* 响应式设计 */
@media screen and (max-width: 750px) {
  .questionnaireHeader {
    padding: 20px 30px 15px;
    margin: 0 15px 15px 15px;
  }

  .questionnaireTitle {
    font-size: 32px;
    margin-bottom: 20px;
  }

  .questionnaireDescription {
    font-size: 28px;
  }

  .descText {
    font-size: 28px;
  }

  .questionnaireContent {
    padding: 0 15px 60px;
  }

  .questionItem {
    padding: 20px;
    margin-bottom: 20px;
  }

  .questionTitle {
    font-size: 30px;
    margin-bottom: 15px;
    padding-bottom: 12px;
  }

  .questionType {
    font-size: 26px;
  }

  .required {
    font-size: 30px;
  }

  .radioGroupItem,
  .checkboxGroupItem {
    padding: 12px;
    margin-bottom: 15px;
  }

  .optionLabel {
    font-size: 30px;
  }

  .secondaryContentContainer {
    margin-top: 12px;
    margin-left: 30px;
    width: calc(100% - 30px);
  }

  .secondaryTextarea {
    min-height: 60px;
    padding: 10px 0 8px;
    font-size: 28px;
  }

  .secondaryCheckboxGroup {
    padding: 12px;
  }

  .secondaryCheckboxItem {
    margin-right: 20px;
    margin-bottom: 12px;
    width: 48%;

    text {
      font-size: 28px;
    }
  }

  .secondaryFillBlankContainer {
    padding: 12px;
    font-size: 30px;
  }

  .secondaryFillBlankText {
    font-size: 30px;
  }

  .secondaryFillBlankInput {
    font-size: 30px;
    height: 42px;
    line-height: 42px;
  }

  .secondaryFillBlankPart {
    display: inline-block;
    vertical-align: middle;
  }

  .fillBlankText {
    font-size: 30px;
  }

  .fillBlankInput {
    min-width: 100px;
    height: 42px;
    line-height: 42px;
    padding: 0 10px;
    font-size: 30px;
  }

  .fileUploadSection {
    margin-top: 20px;
    padding: 20px;
  }

  .fileUploadTitleText {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .fileUploadTitleDesc {
    font-size: 26px;
  }

  .fileItem {
    width: 120px;
    height: 120px;
    margin-right: 15px;
    margin-bottom: 15px;
  }

  .deleteBtn {
    width: 28px;
    height: 28px;
    font-size: 18px;
  }

  .pdfIcon {
    font-size: 24px;
    padding: 6px 12px;
    margin-bottom: 6px;
  }

  .pdfName {
    font-size: 24px;
  }

  .fileUploadBtn {
    width: 120px;
    height: 120px;
    margin-right: 15px;
    margin-bottom: 15px;
  }

  .uploadIconPlus {
    font-size: 40px;
  }

  .uploadBtnText {
    font-size: 26px;
    margin-top: 6px;
  }

  .submitBtn {
    height: 80px;
    line-height: 80px;
    font-size: 32px;
    border-radius: 40px;
  }
}
