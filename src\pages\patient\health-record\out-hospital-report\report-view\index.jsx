import Taro, { Component } from '@tarojs/taro';
import { Image, Picker, View, Input } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';

export default class Index extends Component {

  constructor(props) {
    super(props);
    this.state = {
      status: true,
      abnormal: []
    };
  }

  componentDidMount = () => {
    this.sortData();
  }

  sortData = () => {
    const { data = [] } = this.props;
    let abnormal = [];
    data.map((item) => {
      if (item.result == 0) {
        abnormal.push(item);
      }
    });
    this.setState({ abnormal })
  }

  clickEvent = () => {
    const { status } = this.state;
    this.setState({
      status: !status,
    })
  }

  statusToText = (val)=>{
    switch(val){
      case 1:
        return '阴性';
      case 2:
        return '阳性';
      case 3:
        return '偏高';
      case 4:
        return '偏低';
    }
  }

  render() {
    const {
      status,
    } = this.state;
    const { data = [], title } = this.props;
    return (
      <View>
        <View className={`${s.reportTitle}`} style={{ borderRadius: status ? '6px 6px 0 0' : '6px' }} onClick={this.clickEvent}>
          <View className={`${s.titleInfo}`}>
            <text>{title}</text>
          </View>
          <View className={`${s.viewShow}`}>{!status ? '展开 v' : '收起 >'}</View>
        </View>
        {
          status && <View>
            <View className={`${s.itemModule}`}>
              <View className={s.item} >检查项目</View>
              <View className={s.item}>状态</View>
              <View className={s.item}>结果</View>
              <View className={s.item}>报告时间</View>
            </View>
            {data && data.length > 0 && data.map((item, index) => {
              return <View key={index} className={`${s.itemData}`} >
                <View className={s.item}>{item.inspCode}</View>
                <View className={s.item}>{this.statusToText(item.resultVal)}</View>
                <View className={s.item}>{item.result == 0?'异常':'正常'}</View>
                <View className={s.item}>{item.checkTime}</View>
              </View>
            })}
            {
              data && data.length <= 0 && 
              <View className={`${s.itemData}`} >
                <View className={s.isNo}>暂无内容</View>
              </View>
            }
          </View>
        }
      </View>
    );
  }
}
