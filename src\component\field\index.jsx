import Taro, { Component } from '@tarojs/taro'
import { View, Text, Input } from "@tarojs/components";
import PropTypes from 'prop-types';
import s from './index.module.scss'

export default class Field extends Component {
  constructor(props) {
    super(props)
  }

  handleInput = e => {
    this.props.onSetValue(e.detail.value)
  }

  render() {
    const { label, labelWidth, value, placeholder, placeholderStyle, required, border, type,
      maxlength, labelStyle, disabled, renderSuffix } = this.props
    return (
      <View className={[s.wx_field, border && s.border, required && s.required, disabled && s.disabled]}>
        <View className={[s.wx_field__body, 'f-row', 'f-c-center']}>
          { label ? <Text className={s.wx_field__label} style={{ width: labelWidth, ...labelStyle }}>{ label }</Text> : null }
          <Input
            className={[s.wx_field__input, 'f-1']}
            value={value}
            type={type}
            maxlength={maxlength}
            placeholder={placeholder}
            placeholder-style={placeholderStyle}
            onInput={this.handleInput}
            disabled={disabled}
          />
          { renderSuffix() }
        </View>
      </View>
    )
  }
}

Field.propTypes = {
  onSetValue: PropTypes.func,
  label: PropTypes.string,
  labelWidth: PropTypes.string,
  labelStyle: PropTypes.object,
  value: PropTypes.string,
  placeholder: PropTypes.string,
  placeholderStyle: PropTypes.object,
  required: PropTypes.bool,
  border: PropTypes.bool,
  type: PropTypes.string,
  maxlength: PropTypes.number,
  disabled: PropTypes.bool,
};
Field.defaultProps = {
  labelWidth: '180rpx',
  placeholder: '请输入',
  placeholderStyle: {
    fontSize: '14px',
  },
  required: false,
  border: true,
  type: 'text',
  maxlength: 3000,
  labelStyle: { color: '#000' },
  disabled: false,
};
