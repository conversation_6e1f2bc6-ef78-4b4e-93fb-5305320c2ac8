import Taro, { Component } from '@tarojs/taro'
import { View } from '@tarojs/components'
import * as API from '../api'
import s from './index.module.scss'

export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      patientPid: '',
      familyHistory: [],
    }
  }

  componentWillMount() {
    const { patientPid } = this.$router.params
    this.setState({ patientPid }, this.getDetail)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '家族病史',
    navigationBarTextStyle: 'black'
  };

  async getDetail() {
    const { code, data } = await API.getFamilyInfo({ patients: this.state.patientPid })
    if (code !== 0) return
    let { familyHistory } = data || {}
    try {
      familyHistory = JSON.parse(familyHistory)
    } catch (error) {
      console.error(error)
    }
    this.setState({ familyHistory: [familyHistory || {}] })
  }

  render() {
    const { familyHistory } = this.state
    return (
      <View className={s.page}>
        {
          familyHistory.map(v => (
            <View key={v.info} className={s.page_card}>
              <View className={s.title}>{v.updateTime} {v.updateName}保存</View>
              <View className={s.content}>{v.info}</View>
            </View>
          ))
        }

      </View>
    )
  }
}
