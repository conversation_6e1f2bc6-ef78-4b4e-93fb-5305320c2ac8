import Taro, {Component} from '@tarojs/taro';
import {CoverView, Image, LivePlayer, LivePusher, View} from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import rtcUtils from './webrtc';

const app = Taro.getApp();
export default class Index extends Component {
  config = {
    navigationBarTitleText: '视频中'
  };

  constructor(props) {
    super(props);

    this.state = {
      id: '',
      // list: [],
      pushUrl: '',
      // playUrl: '',
      allowPush: false,
      userList: [],
      isMuted: false,
      positionIsFront: true,
      fullScreenId: -1,
      playUrls: {},
      otherList: [],
      intervalStatus: null,
    };
  }

  async componentWillMount() {
    const options = this.$router.params;
    const { id = '' } = options;
    await this.setState({id});
    this.getUsers(id);
    this.txRTC();
  }

  componentDidMount() {
  }

  componentWillUnmount() {
    clearInterval(this.state.intervalStatus);
  }

  componentDidShow() {
  }

  componentDidHide() {
  }

  onTapFullScreen(id, canFull) {
    console.log(id, canFull);
    let {fullScreenId} = this.state;
    fullScreenId = fullScreenId != id && !!canFull ? id : -1;
    this.setState({fullScreenId});
  }
  onTapForMuted() {
    this.setState({isMuted: !this.state.isMuted});
  }
  onTapForCamera() {
    const pusher = Taro.createLivePusherContext();
    pusher.switchCamera({
      success: () => {
        this.setState({positionIsFront: !this.state.positionIsFront});
      }
    })
  }
  onTapForMinVideo() {
    app.globalData.videoIsMin = true;
    // 最小化视频，并返回上页
    Taro.navigateBack({
      delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
    });
  }
  endVideo() {

    this.endLive();
    // Taro.navigateBack({
    //   delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
    // });
  }
  onPushStateChange(e) {
    console.log('live-push code:', e.detail.code, e.detail.message);
    const { code, message } = e.detail;
    // 使用allowPush，重启推流操作时关闭状态码监听，避免导致重复报错
    if (!this.state.allowPush) return false;
    switch (code) {
      case -1301:
        this.inRtcRoomAgain('打开摄像头失败，请退出页面重进');
        break;
      case -1302:
        this.inRtcRoomAgain('打开麦克风失败，请退出页面重进');
        break;
      case -1307:
        this.inRtcRoomAgain('网络断连，请退出页面重进');
        break;
      case 1101:
        Taro.showToast({
          title: '网络状况不佳', //提示的内容,
          icon: 'none', //图标,
          duration: 1000, //延迟时间,
          mask: false //显示透明蒙层，防止触摸穿透
        });
        break;
      case 1019: // 不在房间中，自动重连
        this.txRTC();
        break;
      case 1020:
        this.addRtcUser(JSON.parse(message)); // 获取用户列表
        break;
      case 3001: //RTMP -DNS解析失败
        this.inRtcRoomAgain('DNS解析失败，请退出页面重进');
        break;
      case 3004: // RTMP服务器主动断开，请检查推流地址的合法性或防盗链有效期
        this.inRtcRoomAgain('推流地址不合法或防盗链已失效');
        break;
      case 3005: // RTMP读/写失败
        Taro.showToast({
          title: '读写视频流失败，正在自动重连',
          icon: 'none'
        });

        this.txRTC();
        // this.inRtcRoomAgain('数据读写失败，请退出页面重进');
        break;
      case 5000: // 5000 就退房
        this.txRTC();
        // this.inRtcRoomAgain();
        break;
    }
  }
  onPlayStateChange(e) {
    console.log('live-play code:', e.detail.code, e.detail.message);
  }

  onPushError(err) {
    console.log(err);
  }
  onPlayError(err) {
    console.log(err,11111);
  }

  async getStatus(groupId) {
    const { data, code } = await Api.getStatus({ groupId });
    if (code == 999) {
      clearInterval(this.state.intervalStatus);
    }
    if (data.status == 'end') {
      this.endLive();
      return;
    }
  }
  async getUsers(groupId) {
    const { data, code } = await Api.getUsers({ groupId });
    if (code == 0) {
      this.setState({userList: data});
      console.log(data)
    }
  }
  onShow() {}
  addRtcUser(msg = {}) {
    const userlist = msg.userlist || [];
    console.log(userlist);
    if (userlist.length > 0) {
      const playUrls = {};
      userlist.forEach(user => {
        playUrls[user.userid] = user.playurl;
      })
      this.setState({playUrls, otherList: userlist});
    }
  }

  /**
   * 使用腾讯实时音视频，生成pushUrl
   * @returns {Promise<void>}
   */
  async txRTC() {
    Taro.showLoading({
      title: '连接中...', //提示的内容,
      mask: false //显示透明蒙层，防止触摸穿透
    });
    const {id} = this.state;

    const resRtcKey = await rtcUtils.getRtcKey({
      expire: 7200,
      groupId: id
    });
    if (resRtcKey.code != 0) {
      this.inRtcRoomAgain();
      return false;
    }
    const pushUrl = await rtcUtils.getPushUrl(resRtcKey.data);
    this.setState({pushUrl});
    if (!pushUrl) {
      this.inRtcRoomAgain();
      return false;
    }
    console.log('pushUrl',pushUrl)
    this.setState({allowPush: true});
    this.startLive();
    Taro.hideLoading();
  }
  inRtcRoomAgain(reason = '') {
    Taro.hideLoading();
    this.setState({allowPush: false});
    Taro.showModal({
      title: '温馨提示', //提示的标题,
      content: reason || '视频连接失败，请退出页面重新连接！', //提示的内容,
      showCancel: false, //是否显示取消按钮,
      confirmText: '退出', //确定按钮的文字，默认为取消，最多 4 个字符,
      confirmColor: '#3eceb6', //确定按钮的文字颜色,
      success: res => {
        if (res.confirm) {
          Taro.navigateBack({
            delta: 1 //返回的页面数，如果 delta 大于现有页面数，则返回到首页,
          });
        }
      }
    });
  }

  async startLive() {
    const {id} = this.state;
    await Api.startLive({ groupId: id });
    const intervalStatus = setInterval(() => this.getStatus(id), 2000);
    this.setState({intervalStatus});
  }
  async endLive() {
    app.globalData.hasEnterVideoRoomAgain = false;
    app.globalData.videoIsMin = false;
    clearInterval(this.state.intervalStatus);
    const param = {
      groupId: this.state.id,
    };
    const { code } = await Api.endLive(param);
    if (code === 0) {
      await Taro.showToast({
        title: '通话结束',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        // 修复多次跳转页面的问题
        Taro.navigateBack({delta:1})
      }, 2000);
    }
  }

  render() {
    const {userList, otherList, pushUrl, isMuted, positionIsFront, fullScreenId, playUrls} = this.state;
    return (
      <View className={s.pageVideo}>
        <View className={`${s.videoBox} f-row f-wrap`}>
          <LivePusher
            url={pushUrl}
            mode='RTC'
            autopush
            onStateChange={this.onPushStateChange}
            onError={this.onPushError}
            waitingImage={`${$CDN_DOMAIN}/his-miniapp/p242/defaultHeadImg.png`}
            muted={isMuted}
            devicePosition={positionIsFront ? 'front' : 'back'}
            className={`${s.videoItem} ${s.videoPusher} f-1 ${fullScreenId != -1 ? fullScreenId == 'current' ? s.videoFull : s.videoHide : ''}`}
          >
            <CoverView onClick={() => this.onTapFullScreen('current', !!pushUrl)} />
          </LivePusher>
          {
            otherList.map((item) => {
              return (
                playUrls[item.userid] && (<LivePlayer
                  key={item.userid}
                  src={playUrls[item.userid]}
                  mode='RTC'
                  autoplay
                  objectFit='contain'
                  onStateChange={this.onPlayStateChange}
                  onError={this.onPlayError}
                  className={`${s.videoItem} ${s.videoPlayer} f-1 ${fullScreenId != -1 ? fullScreenId == item.userid ? s.videoFull : s.videoHide : ''}`}
                >
                  <CoverView onClick={() => this.onTapFullScreen(item.userid, !!pushUrl)} />
                </LivePlayer>)
              )
            })
          }
        </View>
        <View className={`${s.optBox} f-col f-m-between`}>
          <View className='f-row f-m-between'>
            <View className={`${s.optItem} f-col f-center`} onClick={this.onTapForMuted}>
              <Image src={`${$CDN_DOMAIN}/ih-miniapp/video-muted.png`} />
              <View>{isMuted ? '已' : ''}静音</View>
            </View>
            <View className={`${s.optItem} f-col f-center`} onClick={this.onTapForCamera}>
              <Image src={`${$CDN_DOMAIN}/ih-miniapp/video-rotate.png`} />
              <View>旋转摄像头</View>
            </View>
            <View className={`${s.optItem} f-col f-center`} onClick={this.onTapForMinVideo}>
              <Image src={`${$CDN_DOMAIN}/ih-miniapp/video-min.png`} />
              <View>最小化</View>
            </View>
          </View>
          <View className='f-center' onClick={this.endVideo}>
            <Image className={s.optEnd} src={`${$CDN_DOMAIN}/ih-miniapp/off.png`} />
          </View>
        </View>
      </View>
    );
  }
}
