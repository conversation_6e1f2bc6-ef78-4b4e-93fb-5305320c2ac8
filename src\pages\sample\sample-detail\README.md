# 样本详情展示页面

## 功能描述

这是一个样本详情展示页面，参考用户端的 `pages/survey/surveytools/index` 页面设计，主要用于展示和管理样本的详细信息。

## 主要功能

### 1. 产品类型选择
- 提供两种产品类型选择：普通版和儿童版
- 支持动态切换产品类型
- 选择后会调用API更新样本的产品类型

### 2. 功能按钮区域
包含三个主要功能按钮：

#### 基础信息
- 点击可查看样本的基础信息
- 目前显示为弹窗形式，可根据需要扩展为独立页面

#### 问卷详情
- 显示问卷的详细内容
- 支持未填写状态提示
- 点击后调用API获取问卷详情

#### 知情同意书
- 显示知情同意书内容
- 支持未签署状态提示
- 提供签署功能，签署后状态会更新

### 3. 状态管理
- 问卷状态：0-未填写，1-已填写
- 签署状态：0-未签署，1-已签署
- 产品类型：normal-普通版，child-儿童版

## 页面路由

页面路径：`/pages/sample/sample-detail/index`

### 路由参数
- `id`: 样本ID，用于获取样本详情信息

## API接口

### 1. 获取样本详情
- 接口：`GET /api/sample/detail`
- 参数：`{ id: string }`

### 2. 更新产品类型
- 接口：`POST /api/sample/updateType`
- 参数：`{ id: string, type: 'normal' | 'child' }`

### 3. 获取问卷详情
- 接口：`GET /api/survey/detail`
- 参数：`{ id: string }`

### 4. 签署知情同意书
- 接口：`POST /api/sample/signConsent`
- 参数：`{ id: string }`

## 样式特点

- 采用卡片式设计，界面简洁美观
- 支持响应式布局，适配不同屏幕尺寸
- 使用渐变色和阴影效果提升视觉体验
- 状态标识清晰，用户体验良好

## 使用方式

```javascript
// 跳转到样本详情页面
Taro.navigateTo({
  url: '/pages/sample/sample-detail/index?id=样本ID'
});
```

## 注意事项

1. 页面已在 `src/app.jsx` 中注册路由
2. 所有API调用都包含错误处理和加载状态
3. 支持离线模式，无ID时仍可正常展示界面
4. 样式使用CSS Modules，避免样式冲突
