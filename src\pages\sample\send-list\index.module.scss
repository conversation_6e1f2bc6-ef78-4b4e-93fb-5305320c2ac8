.page {
  font-size: 28rpx;

  &_header {
    padding: 10px 24px 24px;
    background-color: #FFF;
  }

  &_body {
    padding: 24px;
  }
}

.search {
  padding: 16px 32px 16px 32px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);

  input {
    color: #000;
  }
}

.header_daterange {
  margin-top: 24px;
  display: flex;
}

.header_daterange__picker {
  flex: 1;
  text-align: center;
}

.picker_content {
  color: rgba(0, 0, 0, 0.9);

  &::after {
    margin: 10px 0 0 12px;
    content: '';
    display: inline-block;
    border: 10px solid rgba(0, 0, 0, 0.9);
    border-right-color: transparent;
    border-left-color: transparent;
    border-bottom-color: transparent;
  }
}


.list_item {
  margin-bottom: 20rpx;
  padding: 30rpx 30rpx 20rpx 30rpx;
  background-color: #FFF;
  border-radius: 20rpx;

  &_waybill {
    color: #000;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    width: fit-content;
  }

  &_content {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 50rpx 0 30rpx 0;
  }

  &_copy {
    width: 32rpx;
    height: 32rpx;
    margin-left: 20rpx;
  }

  &_contact {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;

    &_city {
      font-size: 36rpx;
      font-weight: bold;
      color: #000;
    }

    &_name {
      font-size: 28rpx;
      color: #333;
    }

    &_status {
      font-size: 28rpx;
      color: #333;
      padding-bottom: 5rpx;
    }
  }

  &_time {
    color: #333;
    line-height: 70rpx;
  }

  &_arrow {
    width: 120rpx;
    height: 16rpx;
  }

  &_line {
    border-bottom: 1rpx solid #F2F4F4;
  }

  &_btngroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 20rpx;
  }

  &_btn {
    border: 1rpx solid rgb(220, 220, 220);
    width: 160rpx;
    height: 54rpx;
    line-height: 54rpx;
    display: flex;
    justify-content: center;
    border-radius: 27rpx;
    align-items: center;
    color: #666;
    margin: 0 10rpx;
  }
}