/* Custom Theme */
$color-brand: #30A1A6;
$color-brand-light: #6edac8;
$color-brand-dark: #32a592;

$color-primary: $color-brand;
$color-warn: #FFA14E;
$color-error: #F76260;
$color-main: #333;
$color-title: #2d2d2d;
$color-border: #e5e5e5;
$color-text: #989898;
$color-bg: #f5f5f5;
$color-white: #fff;

// 多行省略号
@mixin ellipsisLn($line: 1) {
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
}

// 文字换行
@mixin textBreak() {
  word-wrap: break-word;
  word-break: break-all;
}

