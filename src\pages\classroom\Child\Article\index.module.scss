.block{
  &_header{
    padding: 0 24px;
    background-color: #FFF;
  }
  &_body{
    padding: 24px;
    background-color: $color-bg;
  }
  &_item{
    margin-bottom: 16px;
    padding: 24px;
    background-color: #FFF;
    border-radius: 8px;
    &_title{
      font-size: 32px;
      line-height: 48px;
      @include ellipsisLn(2);
      color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
      font-weight: 600;
    }
    &_time{
      font-size: 24px;
      line-height: 36px;
      font-weight: 400;
      color: var(--grey-grey-40, rgba(0, 0, 0, 0.40));
    }
    &_img{
      flex: 0 0 140px;
      margin-left: 24px;
      width: 140px;
      height: 140px;
    }
  }
}

.detail_page{
  padding: 24px;
}
