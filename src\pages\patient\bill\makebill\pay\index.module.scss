.page{
  position: relative;
  height: 100vh;
  padding: 0 24px;
  background: linear-gradient(#30A1A6 0%, #F2F4F4 720px);
  &_header {
    display: flex;
    padding: 40px 24px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 24px;
    &_line1{
      color: #FFF;
      text-align: center;
      font-family: PingFang SC;
      font-size: 64px;
      font-style: normal;
      font-weight: 600;
      line-height: 96px; /* 150% */
    }
    &_line2{
      color: rgba(255, 255, 255, 0.78);
      text-align: center;

      /* 28R */
      font-family: PingFang SC;
      font-size: 28px;
      font-style: normal;
      font-weight: 400;
      line-height: 42px; /* 150% */
    }
  }
}

.block{
  position: relative;
  &_bar{
    position: absolute;
    left: -12px;
    right: -12px;
    top: 0;
    transform: translate(0, -50%);
    height: 32px;
    border-radius: 16px;
    background: rgba(0, 0, 0, 0.40);
    z-index: 0;
  }
  &_body{
    position: relative;
    background: var(--grey-grey-00, #FFF);
    padding: 40px 32px;
    z-index: 1;
    border-radius: 12px;
    height: calc(100vh - 242px - 352px);
    box-sizing: border-box;
    overflow-y: scroll;
  }

  &_line {
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    font-size: 32px;
    font-weight: 400;
    line-height: 48px; /* 150% */
    &_label {
      flex: 0 0 200px;
      color: var(--grey-grey-40, rgba(0, 0, 0, 0.40));
    }
    &_value{
      flex: 1;
      color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
    }
    &_value2{
      flex: 1;
      text-align: right;
      color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));
      font-size: 24px;
      font-weight: 500;
      &_inner {
        color: #D2962B;
      }
    }
  }
}

.table{
  margin-top: 48px;
  &_row{
    display: flex;
    align-items: center;
    &:nth-last-of-type(1) .table_col{
      border-bottom: 1px solid $color-bg;
    }
  }
  &_col{
    flex: 1 1 50%;
    padding: 16px;
    border-top: 1px solid $color-bg;
    border-left: 1px solid $color-bg;
    color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));
    font-size: 24px;
    font-weight: 400;
    line-height: 36px; /* 150% */
    &_h {
      padding: 8px 16px;
      color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
      font-weight: 500;
      line-height: 30px; /* 150% */
    }
    @include ellipsisLn();
    &:nth-of-type(1) {
      text-align: left;
    }
    &:nth-last-of-type(1) {
      text-align: right;
      border-right: 1px solid $color-bg;
    }
  }
}

.page_footer{
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  display: inline-flex;
  padding: 64px 0;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 24px;
  .btn{
    width: 100%;
    padding: 24px;
    border-radius: 76px;
    background: var(--Linear, linear-gradient(90deg, #30A1A6 0%, #2F848B 100%));
    color: var(--grey-grey-00, #FFF);
    text-align: center;
    font-family: PingFang SC;
    font-size: 34px;
    font-style: normal;
    font-weight: 600;
    line-height: 52px; /* 152.941% */
    &.cancel{
      background: var(--grey-grey-04, rgba(0, 0, 0, 0.04));
      color: var(--grey-grey-70, rgba(0, 0, 0, 0.70));
    }
    &::after{
      border: none;
    }
  }
}
