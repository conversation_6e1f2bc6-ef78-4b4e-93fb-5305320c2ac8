// const path = require('path');

// const pathSepArr = process.cwd().split(path.sep);

// const projectName = pathSepArr[pathSepArr.length - 1];

module.exports = {
  env: {
    NODE_ENV: '"production"'
  },
  defineConstants: {
    $DOMAIN: '""',
    $PAY_DOMAIN: '"https://pay.med.gzhc365.com"',
    $CDN_DOMAIN: `"https://static.med.gzhc365.com/miniprogram-static/fe-his-twxapp"`,
  },
  mini: {
  },
  sass: {
    data: `$cdn: "https://static.med.gzhc365.com/miniprogram-static/fe-his-twxapp";`
  },
  h5: {
    /**
     * 如果h5端编译后体积过大，可以使用webpack-bundle-analyzer插件对打包体积进行分析。
     * 参考代码如下：
     * webpackChain (chain) {
     *   chain.plugin('analyzer')
     *     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
     * }
     */
  }
}
