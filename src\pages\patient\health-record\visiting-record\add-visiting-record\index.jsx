import Taro, { Component } from '@tarojs/taro';
import moment from 'moment';
import { Image, Picker, View, RichText } from '@tarojs/components';
import s from './index.module.scss';
import * as Api from './api';
import { getFormatDate } from '../../utils/utils';


export default class Index extends Component {
  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '就诊记录',
    navigationBarTextStyle: 'black'
  };

  constructor(props) {
    super(props);
    this.state = {
      content: '',
      selector: [{ name: '仅自己可见', value: 3 }, { name: '医院内可见', value: 1 }, { name: '医联体内可见', value: 4 }, { name: '全部可见', value: 5 }],
      selectorChecked: ''
    };
  }

  inputEvent = (e) => {
    this.setState({
      content: e.target.value
    })
  }

  selectRange = (e) => {
    const { selector } = this.state;
    this.setState({
      selectorChecked: selector[e.detail.value]
    })
  }

  validateData = () => {
    const { content, selectorChecked } = this.state;
    let msg = '';
    if (!content) {
      msg = '就诊记录描述不能为空';
    } else if (!selectorChecked) {
      msg = '请选择可见范围';
    }
    if (msg) {
      Taro.showModal({
        title: '系统提示',
        icon: 'error',
        content: msg,
        showCancel: false
      });
      return false;
    }
    return true;
  }

  submit = async () => {
    if (!this.validateData()) return;
    const { content, selectorChecked } = this.state;
    const {patientPid: pid} = this.$router.params;
    let param = {
      content,
      pid,
      collectionFlag: 0,
      type: selectorChecked.value
    }
    const { code, msg = '' } = await Api.addMedicalRecord(param);
    if (code == 0) {
      Taro.showToast({
        title: '提交成功！',
        icon: 'success',
        duration: 1000,
        success() {
          setTimeout(function () {
            Taro.navigateBack();
          }, 1000)
        }
      })
    } else {
      Taro.showToast({
        title: msg,
        icon: 'none',
        duration: 1500,
      })
    }
  }

  cancel() {
    Taro.navigateBack();
  }

  render() {
    const {
      content,
      selector,
      selectorChecked
    } = this.state;
    return (
      <View className={`${s.container}`}>
        <Textarea value={content} onInput={(e) => this.inputEvent(e)} placeholder={'请详细描述就诊记录，限200字内'} maxlength={200} className={`${s.textarea}`} autoHeight />
        <Picker mode='selector' range={selector} range-key="name" onChange={this.selectRange}>
          <View className={`${s.selectRange}`}>
            请选择可见范围 :<text>{selectorChecked.name} ></text>
          </View>
        </Picker>
        <View className={`${s.actionButton}`}>
          <View className={`${s.submit}`} onClick={this.submit}>提交</View>
          <View className={`${s.cancel}`} onClick={this.cancel}>取消</View>
        </View>
      </View>
    );
  }
}
