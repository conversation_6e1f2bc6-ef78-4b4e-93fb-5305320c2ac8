const path = require('path');

const run_env = process.env.RUN_ENV;
// const projectName = process.env.npm_config_dir;

const config = {
  projectName: 'h242',
  date: '2020-02-20',
  designWidth: 750,
  deviceRatio: {
    '640': 2.34 / 2,
    '750': 1,
    '828': 1.81 / 2
  },
  sourceRoot: `src/`,
  outputRoot: `dist`,
  babel: {
    sourceMap: true,
    presets: [
      ['env', {
        modules: false,
      }]
    ],
    plugins: [
      'transform-decorators-legacy',
      'transform-class-properties',
      'transform-object-rest-spread',
      ['transform-runtime', {
        "helpers": false,
        "polyfill": false,
        "regenerator": true,
        "moduleName": 'babel-runtime'
      }]
    ]
  },
  plugins: [],
  defineConstants: {
    $RUN_ENV: `"${run_env}"`,
    $IPX_BOTTOM_HEIGHT: '"16"',
    $PRIMARY_COLOR: '"#30A1A6"',
    $HIS_NAME: '"海鹚虚拟医院"',
  },
  alias: {
    '@': path.resolve(__dirname, '..', `src/`),
  },
  sass: {
    resource: [
      `src/utils/mixin.scss`
    ],
    projectDirectory: path.resolve(__dirname, '..'),
  },
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {}
      },
      url: {
        enable: true,
        config: {
          limit: 10240 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    compile: {
      exclude: [
        path.resolve(__dirname, '..', `src/static/`)
      ]
    },
  },
  h5: {
    publicPath: `/`,
    staticDirectory: `./src/static`,
    esnextModules: ['taro-ui'],
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
          browsers: [
            'last 3 versions',
            'Android >= 4.1',
            'ios >= 8'
          ]
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    compile: {
      exclude: [
        path.resolve(__dirname, '..', `src/static/`)
      ]
    },
    devServer: {
      proxy: {
        '/api': {
          target: 'https://smp.med.gzhc365.com',
          secure: false,
          changeOrigin: true
        }
      }
    }
  }
}

module.exports = function (merge) {
  if (!run_env) {
    return config;
  }
  return merge({}, config, require(`./${run_env}`))
}
