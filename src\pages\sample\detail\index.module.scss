.page{
  padding: 24px;
}

.block{
  padding: 0 32px;
  background: #FFF;
  border-radius: 8px;
}

.item{
  padding: 32px 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.06) inset;
  &:last-of-type{
    box-shadow: none;
  }
  &_title{
    color: rgba(0, 0, 0, 0.90);
    font-size: 28px;
    font-weight: bold;
    line-height: 42px;
  }
  &_value{
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.70);
    font-size: 32px;
    font-weight: 400;
    line-height: 48px;
  }
  &_files{
    margin-top: 24px;
    display: flex;
    flex-wrap: wrap;
    &__item{
      padding: 16px;
    }
    &__inner{
      width: 160px;
      height: 160px;
    }
  }
}

