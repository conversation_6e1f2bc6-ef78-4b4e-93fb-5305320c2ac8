import Taro, { Component } from '@tarojs/taro';
import { View, Input, Text, Button } from '@tarojs/components';

import styles from './index.module.scss';

export default class Index extends Component {
  constructor(props) {
    super(props);

    this.state = {
      activeAmount: 0,
      totalFee: '',
    };
  }

  componentWillMount () {}

  componentDidMount () {
    this.userSelect(0);
  }

  componentWillUnmount () { }

  componentDidShow () { }

  componentDidHide () { }

  userIO = (e) => {
    const { onValueChange = () => {} } = this.props;
    const { value } = e.detail;
    if (!value && !value.length) {
      this.userSelect(0);
    } else {
      this.setState({ activeAmount: -1, totalFee: value });
      onValueChange(value);
    }
  }

  userSelect = (idx) => {
    const { onValueChange = () => {}, boardList = [] } = this.props;
    this.setState({ activeAmount: idx, totalFee: '' });
    onValueChange(boardList[idx]);
  }

  render () {
    const { boardList = [], balance } = this.props;

    const { activeAmount, totalFee } = this.state;

    return (
      <View className={styles.mcard}>
        <View className={styles.amounthead}>
          <View className={styles.headlt}>
            <View className={styles.amounttitle}>选择充值金额</View>
          </View>
          <View className={styles.headrt}>
            <Text className={styles.balancelabel}>余额：</Text>
            <Text className={styles.balance}>{((balance || 0) / 100).toFixed(2)}</Text>
          </View>
        </View>
        <View className={styles.amountbody}>
          <View className={styles.amountline}>
            {
              boardList.map((item, idx) => {
                return (
                  <View
                    className={`${styles.amountitem} ${styles[`money${item}`]} ${activeAmount === idx ? styles.activeamount : ''}`}
                    onClick={() => this.userSelect(idx)}
                    key={item}
                  >
                    {item}元
                    {
                      activeAmount === idx ? <View className={styles.activetop} /> : null
                    }
                  </View>
                );
              })
            }
            <View className={styles.amountinput}>
              <Input
                className={styles.input}
                type='number'
                placeholder='自定义金额' maxLength='5'
                placeholderStyle={{color: '#999'}} value={totalFee}
                onInput={this.userIO}
              />
            </View>
          </View>
        </View>
      </View>
    )
  }
}
