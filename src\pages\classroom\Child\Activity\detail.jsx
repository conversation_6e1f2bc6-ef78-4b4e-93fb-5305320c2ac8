import Taro, { Component } from '@tarojs/taro';
import { View, RichText, Button } from '@tarojs/components';
import s from './index.module.scss'
import * as API from '../../api'

const DAY_TIMESTAMP = 1000 * 60 * 60 * 24
export default class Index extends Component {

  constructor(props) {
    super(props)
    this.state = {
      needSign: false,
      contetn: '',
    }
  }

  componentWillMount() {
    this.getDetail()
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '培训课堂',
    navigationBarTextStyle: 'black'
  };

  getDetail = async () => {
    const { id } = this.$router.params;
    const { code, data } = await API.getActivityDetail({ id })
    if (code !== 0) return
    const { contetn, needSign, isSign, isCommit, isLimit, freeNum, signBegin, signEnd } = data
    this.setState({ contetn, needSign, isSign, isCommit, isLimit, freeNum, signBegin, signEnd })
  }


  application = () => {
    /** 没传入代表扫码进来 */
    const { id, isGuest = 1 } = this.$router.params;
    Taro.navigateTo({ url: `/pages/classroom/application/index?id=${id}&isGuest=${isGuest}` })
  }

  toSignRecods = () => {
    Taro.navigateTo({ url: '/pages/classroom/records/index' })
  }

  render() {
    const {
      /** 是否需要报名 */
      needSign,
      contetn,
      /** 提交待审核 */
      isCommit,
      /** 审核通过 */
      isSign,
      /** 是否限制名额 */
      isLimit,
      /** 剩余名额  */
      freeNum,
      signBegin,
      signEnd
    } = this.state
    return (
      <View className={[s.detail_page, needSign && s.need_sign]} >
        <View className={s.detail_page_body}>
          <RichText  nodes={contetn}></RichText>
        </View>

        { needSign ? <View className={[s.detail_page_footer, 'f-col', 'f-m-around', 'f-c-center']}>
          {
            isSign ? <Button className={s.detail_page_footer_disabled} disabled>您已报名成功</Button>
            : isCommit ? <Button className={s.detail_page_footer_disabled} disabled>您已提交报名申请，请等待审核</Button>
            : Date.now() < new Date(signBegin).getTime() ? <Button className={s.detail_page_footer_disabled} disabled>报名未开始</Button>
            : Date.now() > new Date(signEnd).getTime() + DAY_TIMESTAMP ? <Button className={s.detail_page_footer_disabled} disabled>报名已结束</Button>
            : (isLimit && freeNum <= 0) ? <Button className={s.detail_page_footer_disabled} disabled>名额已满，感谢您的参与</Button>
            : <Button className={s.detail_page_footer_submit} onClick={this.application}>我要报名</Button>
          }
         
        </View> : null }
        
        <View className={[s.block_footer]}>
          <Text className={s.block_footer_record} onClick={this.toSignRecods}>我的报名记录</Text>
        </View>
      </View>
    )
  }
}
