.card{
  margin: 20px;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 30px;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.02);
  position: relative;

  &:after{
    content: ' ';
    position: absolute;
    right: 30px;
    top: 50%;
    width: 17px;
    height: 17px;
    border-right: 5px solid #C7C7CC;
    border-bottom: 5px solid #C7C7CC;
    transform: translate(-8px, -50%) rotate(-45deg);
  }
  
  .cardInfo{
    padding:39px 0 36px;
  }
  .infoMain{
    display: flex;
    align-items: flex-start;
    padding-left: 2px;
  }
  .mainName{
    display: flex;
    align-items: center;
    flex: 1;
  }
  .name{
    font-size: 37px;
    font-weight: bold;
    //height: 37px;
  }
  .status{
    font-size: 24px;
    height: 40px;
    line-height: 40px;
    color:$color-text;
    margin-left: 15px;
    border:1px solid $color-text;
    border-radius: 4px;
    padding: 0 7px;
  }
  .signStatus{
    color: $color-warn;
    border:1px solid $color-warn;
  }

  .infoExtra{
    margin-top: 20px;
    color: $color-text;
    font-size: 28px;
  }
}

.adduser{
  padding: 49px 37px 48px 154px;
  background: url(#{$cdn}/icon-add.png) no-repeat 30px center;
  background-size: 98px;
  background-color: #fff;
  margin: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,0.02);
  position: relative;
  
  &:after{
    content: ' ';
    position: absolute;
    right: 30px;
    top: 50%;
    width: 17px;
    height: 17px;
    border-right: 5px solid #C7C7CC;
    border-bottom: 5px solid #C7C7CC;
    transform: translate(-8px, -50%) rotate(-45deg);
  }

  .addTitle{
    font-size: 34px;
  }
  .addText{
    font-size: 28px;
    color: $color-text;
  }

}