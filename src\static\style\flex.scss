// 定义常用的flex布局属性
:global{
  .flex,.f-row,.f-row-rvs,.f-col,.f-col-rvs,.f-center{
    display: flex;
    display: -webkit-flex;
  }
  .f-row-rvs{
    flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
  }
  .f-col{
    flex-direction: column;
    -ms-flex-direction: column;
  }
  .f-col-rvs{
    flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
  }

  .f-center{
    justify-content: center;
    align-items: center;
  }
  .f-wrap{
    flex-wrap: wrap;
  }
  // m 为主轴
  .f-m-start{justify-content: flex-start;}
  .f-m-center{justify-content: center;}
  .f-m-end{justify-content: flex-end;}
  .f-m-between{justify-content: space-between;}
  .f-m-around{justify-content: space-around;}
  // c 为交叉轴即副轴
  .f-c-start{align-items: flex-start;}
  .f-c-center{align-items: center;}
  .f-c-end{align-items: flex-end;}
  .f-c-stretch{align-items: stretch;}
  .f-c-baseline{align-items: baseline;}
  .f-1{flex:1;}
  .f-2{flex:2;}
  .f-3{flex:3;}
  .f-4{flex:4;}
  .f-5{flex:5;}
}

