page {
  height: 100%;
  background: $color-bg;
}

textarea {
  width: 100%;
  color: $color-title;
}
.colorRed{
  color: red;
  position: absolute;
  left: -30px;
  top: 3px;
}
.container {
  padding-bottom: 80px;
  background-color: $color-white;

  .tip {
    background-color: #FFEFCF;
    color: #FF8C26;
    padding: 26px;
    font-size: 28px;
  }

  .rowModule {
    display: flex;
    background-color: #fff;
    justify-content: space-between;
    margin: 30px 30px 0;
    padding-bottom: 30px;
    align-items: center;
    border-bottom: 2px solid #E5E5E5;

    .patientName {
      color: $color-title;
      margin-right: 10px;
      font-size: 34px;
    }
  }

  .inputModule {
    margin: 30px 30px 0;
    padding-bottom: 30px;
    border-bottom: 2px solid #E5E5E5;
  }
  .between{
    display: flex;
    justify-content: space-between;
  }

  .mainSuit {
    position: relative;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2D2D2D;
    font-size: 34px;

    text {
      margin-left: 10px;
      color: #f00;
    }
  }

  .textarea {
    // min-height: 150px;
    box-sizing: border-box;
    font-size: 30px;
  }

  .inputcss {
    min-height: 44px;
    box-sizing: border-box;
  }

  .fileContainer {
    display: flex;
    .fileContent {
      width: 148px;
      height: 148px;
      margin-top: 20px;
      margin-right: 30px;
      position: relative;

      image {
        width: 100%;
        height: 100%;
      }

      .deleteBtn {
        position: absolute;
        right: -16px;
        top: -16px;
        width:32px;
        height:32px;
        display: flex;
        justify-content: center;
        align-items: center;
        background:rgba(200,200,200,1);
        border-radius: 50%;
        color: $color-white;
      }
    }

    .addBtn {
      width: 148px;
      height: 148px;
      margin-top: 20px;
      border: 3px dashed $color-border;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      font-size: 60px;
    }
  }

  .submit {
    display: flex;
    position: fixed;
    margin: auto;
    left: 0;
    right: 0;
    width: 90%;
    bottom: 40px;
    background-color: #3ECEB6;
    color: #fff;
    padding: 15px 0;
    border-radius: 10px;
    font-size: 36px;

    text {
      margin: auto;
    }
  }
}
.suggestion{
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background-color: #3ECEB6;
  width: 80%;
  border-radius: 10px;
  margin: 30px auto 0;
  color: #FFFFFF;
  font-size: 36px;
}

.medicalImgs{
  color: #3ECEB6;
}

.title{
  font-size: 36px;
  font-weight: 600;
  color: #2D2D2D;
  background-color: rgb(228, 228, 228);
  text-align: center;
  padding: 10px 0;
}

.radioList{
  display: flex;
  flex-wrap: wrap;
  .radioListLabel{
    margin-right: 30px;
    line-height: 80px;
    width: 40vw;
  }
}