import Taro, { Component } from "@tarojs/taro";
import { View, Button } from '@tarojs/components';
import Upload from '@/component/upload';

import * as API from './api'
import s from './index.module.scss'

export default class SampleUpload extends Component {

  constructor(props) {
    super(props)
    this.state = {
      barCode: '',
      fileList: [],
    }
  }

  componentWillMount() {
    const options = this.$router.params
    const barCode = decodeURIComponent(options.scene)
    this.setState({ barCode })
    this.getFileList(barCode)
  }

  config = {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '图片上传',
    navigationBarTextStyle: 'black'
  };

  async validate() {
    if (!this.state.fileList.length) return Promise.resolve('请先上传图片')
    return Promise.resolve()
  }

  async getFileList(sampleNumber) {
    const { code, data } = await API.getSampleFiles({ sampleNumber })
    if (code !== 0 || !data) return
    this.setState({ fileList: data.split(',') })
  }

  submit = async () => {
    const error = await this.validate()
    if (error) return Taro.showToast({ title: error, icon: 'none' });
    const { code } = await API.uploadSampleFiles({ sampleNumber: this.state.barCode, paths: this.state.fileList.join(',') })
    if (code !== 0) return
    Taro.showToast({ title: '上传成功', icon: 'none' });
    setTimeout(Taro.navigateBack, 1500)
  }

  

  render() {
    const { fileList, barCode } = this.state
    const length = fileList.length
    return (
      <View className={s.page}>

        <View className={s.page_body}>

          <View className={s.title}>样本编号：{barCode}</View>

          <Upload
            // sourceType={['camera']}
            sizeType={['original']}
            fileList={fileList}
            updateFileList={list => this.setState({ fileList: list })}
          />
        </View>

        <View className={s.page_footer}>
          <Button className={[s.submit, length === 0 && s.disabled]} onClick={this.submit}>上 传</Button>
        </View>
        
      </View>
    )
  }
}
