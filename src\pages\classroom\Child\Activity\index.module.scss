.block{
  &_header{
    padding: 0 24px;
    background-color: #FFF;
  }
  &_body{
    padding: 24px;
    padding-bottom: 0;
    box-sizing: border-box;
    background-color: $color-bg;
    height: calc(100vh - 116px - 96px);
    overflow-y: scroll;
    &.is_guest{
      height: calc(100vh - 96px);
    }
  }
  &_item{
    margin-bottom: 16px;
    padding: 24px;
    background-color: #FFF;
    border-radius: 8px;
    &_title{
      font-size: 32px;
      font-weight: 600;
      line-height: 48px;
      // color: #000;
      @include ellipsisLn(2);
      color: var(--grey-grey-90, rgba(0, 0, 0, 0.90));
    }
    &_status{
      display: inline-block;
      padding: 2px 4px;
      font-size: 24px;
      background-color: antiquewhite;
      color: $color-text;
      border-radius: 4px;
      &.primary{
        background: rgba(63, 150, 157, 0.15);
        // color: $color-primary;
        color: #3F969D;
      }
      &.warn{
        background: rgba(136, 47, 56, 0.15);
        color: #882F38;
        // color: $color-warn;
        // background-color: rgba(36,47,56,0.15);
      }
    }
    &_date_range{
      @include ellipsisLn(1);
      color: var(--grey-grey-40, rgba(0, 0, 0, 0.40));
      font-size: 24px;
      font-weight: 400;
      line-height: 30px;
    }
    &_img{
      flex: 0 0 140px;
      margin-left: 24px;
      width: 140px;
      height: 140px;
    }
  }
  &_footer{
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 96px;
    line-height: 96px;
    box-sizing: border-box;
    text-align: center;
    &_record{
      color: $color-primary;
      font-size: 32px;
    }
  }
}

.detail_page{
  box-sizing: border-box;
  &_body{
    padding: 24px;
    height: 100vh;
    box-sizing: border-box;
    overflow-y: scroll;
    height: calc(100vh - 116px);
  }
  &.need_sign{
    .detail_page_body{
      height: calc(100vh - 160px - 116px);
    }
  }
  &_footer{
    padding: 0 24px;
    position: absolute;
    bottom: 96px;
    left: 0;
    right: 0;
    height: 160px;
    background-color: #FFF;
    box-sizing: border-box;
    & Button{
      height: 96rpx;
      width: 100%;
      line-height: 96rpx;
      font-size: 36px;
      border-radius: 76rpx;
      color: #fff;
      &::after{
        border: none;
      }
    }
    &_submit{
      background: $color-primary;
      &::after{
        border: none;
      }
    }
    &_disabled {
      background-color: $color-brand-light;
    }
    &_record{
      color: $color-primary;
      font-size: 28px;
    }
  }
}
