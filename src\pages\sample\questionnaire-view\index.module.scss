.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.scrollView {
  width: 100%;
}

.loading, .error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 32px;
  color: #666;
}

.error {
  color: #ff4d4f;
}

/* 问卷头部样式 */
.surveyHead {
  background: white;
  padding: 40px 30px;
  margin-bottom: 20px;
  border-radius: 0 0 20px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.surveyTitle {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
  line-height: 1.4;
}

.surveyTime {
  font-size: 26px;
  color: #666;
  text-align: center;
  margin-bottom: 30px;
}

.surveyDes {
  font-size: 28px;
  color: #555;
  line-height: 1.6;
}

.descLine {
  margin-bottom: 10px;
  text-indent: 2em;
}

.publishUnit {
  margin-top: 20px;
  font-size: 26px;
  color: #888;
  text-align: right;
}

/* 问卷内容样式 */
.surveyBody {
  padding: 0 20px 40px;
}

.questionItem {
  margin-bottom: 30px;
}

.questionContainer {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.questionTitle {
  font-size: 30px;
  font-weight: 600;
  color: #333;
  margin-bottom: 25px;
  line-height: 1.5;
}

.required {
  color: #ff4d4f;
  font-size: 32px;
  margin-left: 5px;
}

/* 选项列表样式 */
.optionList {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.optionItem {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.optionContent {
  display: flex;
  align-items: center;
  gap: 15px;
}

.optionText {
  font-size: 28px;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

/* 单选按钮样式 */
.radioButton {
  width: 40px;
  height: 40px;
  border: 3px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.radioButton.checked {
  border-color: #30A1A6;
}

.radioInner {
  width: 20px;
  height: 20px;
  background-color: #30A1A6;
  border-radius: 50%;
}

/* 复选框样式 */
.checkboxButton {
  width: 40px;
  height: 40px;
  border: 3px solid #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.checkboxButton.checked {
  border-color: #30A1A6;
  background-color: #30A1A6;
}

.checkboxInner {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

/* 二级内容样式 */
.secondaryContent {
  background: rgba(48, 161, 166, 0.1);
  border: 1px solid #30A1A6;
  border-radius: 12px;
  padding: 20px;
  margin-left: 55px;
}

.secondaryText {
  font-size: 26px;
  color: #30A1A6;
  line-height: 1.4;
}

.secondaryTitle {
  font-size: 24px;
  color: #666;
  margin-bottom: 10px;
  font-weight: 600;
}

/* 二级复选框样式 */
.secondaryCheckboxList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.secondaryCheckboxItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.secondaryCheckbox {
  width: 20px;
  height: 20px;
  background-color: #30A1A6;
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
}

.secondaryCheckboxText {
  font-size: 24px;
  color: #30A1A6;
  line-height: 1.4;
}

/* 二级多值填空样式 */
.secondaryFillBlankContainer {
  display: block;
  width: 100%;
  line-height: 2;
  word-wrap: break-word;
  font-size: 26px;
}

.secondaryFillBlankPart {
  display: inline;
  vertical-align: baseline;
  margin: 0;
  padding: 0;
}

.secondaryFillBlankText {
  display: inline;
  vertical-align: baseline;
  line-height: inherit;
  margin: 0;
  padding: 0;
  font-size: 26px;
  color: #30A1A6;
  white-space: pre-wrap;
}

.secondaryFillBlankAnswer {
  display: inline-block;
  vertical-align: baseline;
  min-width: 60px;
  background-color: rgba(48, 161, 166, 0.2);
  border: 1px solid #30A1A6;
  border-radius: 4px;
  padding: 2px 8px;
  margin: 0 2px;
  font-size: 26px;
  text-align: center;
}

.secondaryFillBlankValue {
  color: #30A1A6;
  font-weight: 600;
}

/* 填空题样式 */
.fillBlankContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.fillBlankItem {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.fillBlankAnswer {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(48, 161, 166, 0.1);
  border-radius: 12px;
  padding: 15px 20px;
}

.fillBlankLabel {
  font-size: 26px;
  color: #666;
  font-weight: 600;
  min-width: 120px;
}

.fillBlankValue {
  font-size: 28px;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

/* 不支持的题型样式 */
.unsupportedType {
  background: #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.unsupportedType text {
  font-size: 26px;
  color: #999;
}

/* 附件样式 */
.attachmentSection {
  margin-top: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.attachmentTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
}

.attachmentTitleText {
  font-size: 26px;
  font-weight: 600;
  color: #495057;
}

.attachmentDesc {
  font-size: 22px;
  color: #6c757d;
}

.attachmentList {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.attachmentItem {
  flex: 0 0 auto;
}

/* 图片附件样式 */
.imageAttachment {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.imageAttachment:active {
  transform: scale(0.95);
}

.attachmentImage {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  border: 2px solid #dee2e6;
  object-fit: cover;
}

.attachmentLabel {
  font-size: 22px;
  color: #6c757d;
  text-align: center;
}

/* PDF附件样式 */
.pdfAttachment {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  min-width: 120px;
}

.pdfAttachment:active {
  transform: scale(0.95);
}

.pdfIcon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  border: 2px solid #dee2e6;
}

.pdfName {
  font-size: 20px;
  color: #6c757d;
  text-align: center;
  word-break: break-all;
  max-width: 120px;
  line-height: 1.3;
}

/* 其他文件附件样式 */
.otherAttachment {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  min-width: 120px;
}

.otherAttachment:active {
  transform: scale(0.95);
}

.fileIcon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  border: 2px solid #dee2e6;
}

.fileName {
  font-size: 20px;
  color: #6c757d;
  text-align: center;
  word-break: break-all;
  max-width: 120px;
  line-height: 1.3;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .surveyHead {
    padding: 30px 20px;
  }

  .surveyTitle {
    font-size: 32px;
  }

  .surveyTime {
    font-size: 24px;
  }

  .surveyDes {
    font-size: 26px;
  }

  .publishUnit {
    font-size: 24px;
  }

  .questionContainer {
    padding: 25px 20px;
  }

  .questionTitle {
    font-size: 28px;
  }

  .required {
    font-size: 30px;
  }

  .optionText {
    font-size: 26px;
  }

  .radioButton, .checkboxButton {
    width: 36px;
    height: 36px;
  }

  .radioInner {
    width: 18px;
    height: 18px;
  }

  .checkboxInner {
    font-size: 22px;
  }

  .secondaryText {
    font-size: 24px;
  }

  .secondaryTitle {
    font-size: 22px;
  }

  .secondaryCheckbox {
    width: 18px;
    height: 18px;
    font-size: 12px;
  }

  .secondaryCheckboxText {
    font-size: 22px;
  }

  .secondaryFillBlankContainer {
    font-size: 24px;
  }

  .secondaryFillBlankText {
    font-size: 24px;
  }

  .secondaryFillBlankAnswer {
    font-size: 24px;
    min-width: 50px;
    padding: 1px 6px;
  }

  .fillBlankLabel {
    font-size: 24px;
    min-width: 100px;
  }

  .fillBlankValue {
    font-size: 26px;
  }

  .unsupportedType text {
    font-size: 24px;
  }

  /* 移动端附件样式适配 */
  .attachmentSection {
    padding: 15px;
    margin-top: 15px;
  }

  .attachmentTitleText {
    font-size: 24px;
  }

  .attachmentDesc {
    font-size: 20px;
  }

  .attachmentList {
    gap: 12px;
  }

  .attachmentImage {
    width: 100px;
    height: 100px;
  }

  .attachmentLabel {
    font-size: 20px;
  }

  .pdfAttachment, .otherAttachment {
    min-width: 100px;
  }

  .pdfIcon, .fileIcon {
    width: 100px;
    height: 100px;
    font-size: 20px;
  }

  .pdfName, .fileName {
    font-size: 18px;
    max-width: 100px;
  }
}
